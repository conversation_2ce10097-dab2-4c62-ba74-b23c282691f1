// scripts/transfer-ownership-to-timelock.js
// 将 OrderManagement 合约所有权转移给 Timelock

require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("🔐 转移 OrderManagement 合约所有权给 Timelock...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取部署者
    const [deployer] = await ethers.getSigners();
    console.log("📝 操作者地址:", deployer.address);
    console.log("💰 操作者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

    // 2. 从环境变量获取合约地址
    const orderMgmtAddr = process.env.ORDER_MANAGEMENT_ADDRESS;
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;

    console.log("📋 合约地址:");
    console.log("   • OrderManagement地址:", orderMgmtAddr);
    console.log("   • Timelock地址:", timelockAddr);

    if (!orderMgmtAddr) {
      throw new Error("请在 .env 文件中设置 ORDER_MANAGEMENT_ADDRESS");
    }
    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
    }

    // 3. 连接到 OrderManagement 合约
    console.log("⏳ 连接到 OrderManagement 合约...");
    const OrderManagement = await ethers.getContractFactory("OrderManagement");
    const orderManagement = OrderManagement.attach(orderMgmtAddr);

    // 4. 检查当前所有者
    console.log("🔍 检查当前所有者...");
    const currentOwner = await orderManagement.owner();
    const storedTimelock = await orderManagement.timelock();
    
    console.log("📊 当前所有者:", currentOwner);
    console.log("📊 存储的 Timelock 地址:", storedTimelock);
    console.log("📊 目标 Timelock 地址:", timelockAddr);

    // 5. 验证操作权限
    if (currentOwner.toLowerCase() !== deployer.address.toLowerCase()) {
      throw new Error(`权限不足: 当前所有者是 ${currentOwner}，但操作者是 ${deployer.address}`);
    }

    // 6. 检查是否已经转移
    if (currentOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("✅ 所有权已经属于 Timelock，无需转移");
      return;
    }

    // 7. 验证 Timelock 地址一致性
    if (storedTimelock.toLowerCase() !== timelockAddr.toLowerCase()) {
      console.log("⚠️  存储的 Timelock 地址与环境变量不一致");
      console.log("🔄 更新 Timelock 地址...");
      
      const updateTx = await orderManagement.setTimelock(timelockAddr);
      console.log("📋 更新交易哈希:", updateTx.hash);
      
      await updateTx.wait();
      console.log("✅ Timelock 地址已更新");
    }

    // 8. 转移所有权
    console.log("⏳ 转移所有权给 Timelock...");
    console.log("⚠️  这是一个不可逆操作！");
    
    // 给用户一些时间考虑
    console.log("⏰ 5秒后开始转移...");
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const tx = await orderManagement.transferOwnershipToTimelock();
    console.log("📋 转移交易哈希:", tx.hash);

    // 9. 等待交易确认
    console.log("⏳ 等待交易确认...");
    const receipt = await tx.wait();
    console.log("✅ 交易已确认，区块号:", receipt.blockNumber);

    // 10. 验证转移结果
    console.log("🔍 验证转移结果...");
    const newOwner = await orderManagement.owner();
    console.log("📊 新所有者:", newOwner);

    if (newOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("✅ 所有权转移成功！");
    } else {
      console.log("❌ 所有权转移失败！");
      process.exit(1);
    }

    // 11. 输出转移总结
    console.log("\n🎉 所有权转移完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("🔐 转移操作: 成功");
    console.log("📦 OrderManagement地址:", orderMgmtAddr);
    console.log("👤 原所有者:", deployer.address);
    console.log("🏛️  新所有者:", newOwner);
    console.log("🌐 网络:", network.name);
    console.log("📋 交易哈希:", tx.hash);
    console.log("🔢 区块号:", receipt.blockNumber);
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    console.log("\n📋 重要提醒:");
    console.log("• ✅ OrderManagement 合约现在由 Timelock 拥有");
    console.log("• ⚠️  后续所有管理操作都需要通过 Timelock 延迟机制");
    console.log("• 🔧 合约升级需要通过 Timelock 提案和执行");
    console.log("• 🔐 授权管理需要通过 Timelock 进行");

    console.log("\n📋 下一步操作:");
    console.log("1. 系统现在完全由 Timelock 治理");
    console.log("2. 可以开始正常的业务操作");
    console.log("3. 如需管理操作，请使用 Timelock 提案机制");

  } catch (error) {
    console.error("❌ 所有权转移失败:", error);
    
    // 提供详细的错误信息
    if (error.message.includes("Ownable: caller is not the owner")) {
      console.error("💡 提示: 只有当前所有者才能转移所有权");
    } else if (error.message.includes("Already owned by timelock")) {
      console.error("💡 提示: 所有权已经属于 Timelock");
    } else if (error.message.includes("Timelock not set")) {
      console.error("💡 提示: 请先设置正确的 Timelock 地址");
    }
    
    process.exit(1);
  }
}

// 错误处理
main()
  .then(() => {
    console.log("🎯 所有权转移脚本执行完成");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 所有权转移脚本执行失败:", error);
    process.exit(1);
  });
