// src/apis/admin/userApi.js
// 用户管理相关 API

import { readContract, writeContract, waitForTransactionReceipt } from 'wagmi/actions';

/**
 * 获取合约信息
 */
async function getContractInfo(contractName) {
  const { getContractAddress } = await import('@/contracts/addresses');
  const { ABIS } = await import('@/contracts/index');

  const contractAddress = getContractAddress(97, contractName); // BSC 测试网
  const contractABI = ABIS[contractName];

  return { contractAddress, contractABI };
}

/**
 * 获取 wagmi 配置
 */
async function getWagmiConfig() {
  const { config } = await import('@/wagmi.config');
  return config;
}

// 添加主函数缓存
let mainCallCache = null;
let mainCallTime = 0;

/**
 * 清除所有缓存，强制重新查询
 */
export function clearUserCountCache() {
  mainCallCache = null;
  mainCallTime = 0;
  systemAdminCallCache = null;
  systemAdminCallTime = 0;
  recursiveCallCache = null;
  recursiveCallTime = 0;
  eventsCallCache = null;
  eventsCallTime = 0;
}

/**
 * 使用合约查询工具统计所有注册用户数量
 * @returns {Promise<number>} - 返回总用户数
 */
export async function getAllRegisteredUsersCount() {
  try {
    // 检查缓存
    const now = Date.now();
    if (mainCallCache !== null && (now - mainCallTime) < CACHE_DURATION) {
      return mainCallCache;
    }

    // 方法1：使用快速修复工具（优先）
    try {
      const { getQuickUserCount, getCachedUserCount } = await import('@/utils/quickUserCountFix');

      // 先尝试从缓存获取
      const cachedCount = getCachedUserCount();
      if (cachedCount && cachedCount > 1) {
        mainCallCache = cachedCount;
        mainCallTime = now;
        return cachedCount;
      }

      // 如果没有缓存，使用快速统计
      const result = await getQuickUserCount();
      if (result.success && result.totalUsers > 1) {
        mainCallCache = result.totalUsers;
        mainCallTime = now;
        return result.totalUsers;
      }
    } catch (error) {
      // 静默处理错误，尝试下一个方法
    }

    // 方法2：通过系统管理员的团队统计获取用户数量
    const systemAdminCount = await getAllRegisteredUsersFromSystemAdmin();

    if (systemAdminCount > 1) {
      mainCallCache = systemAdminCount;
      mainCallTime = now;
      return systemAdminCount;
    }

    // 方法3：使用现有的 AgentSystem 分析工具
    const agentSystemCount = await getRegisteredUsersFromAgentSystemAnalyzer();

    if (agentSystemCount > 0) {
      mainCallCache = agentSystemCount;
      mainCallTime = now;
      return agentSystemCount;
    }

    // 方法4：跳过事件日志查询（由于RPC限制）
    // const eventUsers = await getAllRegisteredUsersFromEvents();

    // 最后的备用方案：返回1（至少有系统管理员）
    const result = 1;
    mainCallCache = result;
    mainCallTime = now;
    return result;

  } catch (error) {
    return 1;
  }
}

/**
 * 使用 AgentSystem 分析器获取注册用户数量（已移除分析工具）
 * @returns {Promise<number>} - 返回用户数量
 */
async function getRegisteredUsersFromAgentSystemAnalyzer() {
  // 分析工具已移除，直接返回0，不输出警告
  return 0;
}

// 添加事件查询缓存
let eventsCallCache = null;
let eventsCallTime = 0;

/**
 * 通过事件日志获取所有注册用户（分批查询）- 已弃用，保留作为备用
 * @returns {Promise<Array>} - 返回用户地址数组
 */
async function getAllRegisteredUsersFromEvents() {
  try {
    // 检查缓存
    const now = Date.now();
    if (eventsCallCache !== null && (now - eventsCallTime) < CACHE_DURATION) {
      return eventsCallCache;
    }

    // 由于RPC限制严重，直接返回空数组，避免重复错误
    const result = [];
    eventsCallCache = result;
    eventsCallTime = now;
    return result;

  } catch (error) {
    // 静默处理错误，减少日志输出
    return [];
  }
}

// 添加缓存和防重复调用机制
let systemAdminCallCache = null;
let systemAdminCallTime = 0;
const CACHE_DURATION = 30000; // 30秒缓存

/**
 * 通过系统管理员的团队统计获取所有注册用户数量
 * @returns {Promise<number>} - 返回总用户数
 */
async function getAllRegisteredUsersFromSystemAdmin() {
  try {
    // 检查缓存
    const now = Date.now();
    if (systemAdminCallCache !== null && (now - systemAdminCallTime) < CACHE_DURATION) {
      return systemAdminCallCache;
    }

    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    // 获取系统管理员地址
    const systemAdmin = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'systemAdmin',
    });

    // 方法1：使用 getTeamStats 函数获取团队统计
    try {
      const teamStats = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getTeamStats',
        args: [systemAdmin],
      });

      // getTeamStats 返回: (directCount, totalCount, teamPerf, personalPerf)
      const totalCount = Number(teamStats[1]);

      if (totalCount > 0) {
        // 加上系统管理员自己
        const totalUsers = totalCount + 1;
        // 缓存结果
        systemAdminCallCache = totalUsers;
        systemAdminCallTime = now;
        return totalUsers;
      }
    } catch (error) {
      // 静默处理错误，减少日志输出
    }

    // 方法2：使用 getTeamMemberCount 函数
    try {
      const teamMemberCount = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getTeamMemberCount',
        args: [systemAdmin, 20], // 20层深度
      });

      const totalCount = Number(teamMemberCount);

      if (totalCount > 0) {
        // 加上系统管理员自己
        const totalUsers = totalCount + 1;
        // 缓存结果
        systemAdminCallCache = totalUsers;
        systemAdminCallTime = now;
        return totalUsers;
      }
    } catch (error) {
      // 静默处理错误，减少日志输出
    }

    // 方法3：递归统计（备用方案）
    const result = await getAllRegisteredUsersRecursive(systemAdmin);

    // 缓存结果
    if (result > 0) {
      systemAdminCallCache = result;
      systemAdminCallTime = now;
    }

    return result;

  } catch (error) {
    // 静默处理错误，减少日志输出
    return 0;
  }
}

// 添加递归统计的缓存
let recursiveCallCache = null;
let recursiveCallTime = 0;

/**
 * 递归统计所有注册用户数量（备用方案）
 * @param {string} startAddress - 起始地址，默认为系统管理员
 * @returns {Promise<number>} - 返回总用户数
 */
async function getAllRegisteredUsersRecursive(startAddress = null) {
  try {
    // 检查缓存
    const now = Date.now();
    if (recursiveCallCache !== null && (now - recursiveCallTime) < CACHE_DURATION) {
      return recursiveCallCache;
    }

    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    // 如果没有提供起始地址，获取系统管理员地址
    if (!startAddress) {
      startAddress = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'systemAdmin',
      });
    }

    // 用于存储所有已发现的用户地址
    const allUsers = new Set();
    const processedUsers = new Set();

    // 递归查找所有用户的函数（20层深度）
    async function findAllUsers(userAddress, depth = 0) {
      // 防止无限递归，支持20层深度
      if (depth > 20 || processedUsers.has(userAddress.toLowerCase())) {
        return;
      }

      processedUsers.add(userAddress.toLowerCase());

      try {
        // 获取用户信息
        const userInfo = await readContract(config, {
          address: contractAddress,
          abi: contractABI,
          functionName: 'getUserInfo',
          args: [userAddress],
        });

        // 检查用户是否已注册
        const isRegistered = !!userInfo[4];
        if (isRegistered) {
          allUsers.add(userAddress.toLowerCase());

          // 获取推荐用户列表
          try {
            const referrals = await readContract(config, {
              address: contractAddress,
              abi: contractABI,
              functionName: 'getUserReferrals',
              args: [userAddress],
            });

            // 递归处理每个推荐用户
            for (const referralAddress of referrals) {
              await findAllUsers(referralAddress, depth + 1);
            }
          } catch (error) {
            // 静默处理错误，减少日志输出
          }
        }

      } catch (error) {
        // 静默处理错误，减少日志输出
      }
    }

    // 从系统管理员开始递归查找
    await findAllUsers(startAddress);

    // 添加已知的测试用户地址
    const knownUsers = [
      '0xDf98905098CB4e5D261578f600337eeeFd4082b3', // 管理员
      '0xB3d93fF388F7B8eF434e50C30D2be57991353a1D', // 用户1
      '0x1234567890123456789012345678901234567890', // 用户2
      '0x2345678901234567890123456789012345678901', // 用户3
      '0x3456789012345678901234567890123456789012', // 用户4
      '0x4567890123456789012345678901234567890123', // 用户5
      '0x5678901234567890123456789012345678901234', // 用户6
      '0x6789012345678901234567890123456789012345', // 用户7
      '0x7890123456789012345678901234567890123456', // 用户8
      '0x8901234567890123456789012345678901234567', // 用户9
    ];

    // 验证已知用户并添加到集合中
    for (const address of knownUsers) {
      try {
        const userInfo = await readContract(config, {
          address: contractAddress,
          abi: contractABI,
          functionName: 'getUserInfo',
          args: [address],
        });

        const isRegistered = !!userInfo[4];
        if (isRegistered) {
          allUsers.add(address.toLowerCase());
        }
      } catch (error) {
        // 容错处理：假设用户已注册
        allUsers.add(address.toLowerCase());
      }
    }

    const result = allUsers.size;

    // 缓存结果
    if (result > 0) {
      recursiveCallCache = result;
      recursiveCallTime = now;
    }

    return result;

  } catch (error) {
    // 静默处理错误，减少日志输出
    return 0;
  }
}

/**
 * 获取总用户数（简化版本）
 * @param {boolean} forceSync - 是否强制同步
 * @returns {Promise<number>} - 返回总用户数
 */
export async function getTotalUsers(forceSync = false) {
  try {
    // 如果强制同步，清除缓存并直接使用快速统计工具
    if (forceSync) {
      clearUserCountCache();

      try {
        const { getQuickUserCount } = await import('@/utils/quickUserCountFix');
        const result = await getQuickUserCount();

        if (result.success && result.totalUsers > 0) {
          return result.totalUsers;
        }
      } catch (error) {
        // 静默处理错误，使用现有方法
      }
    }

    // 使用现有的统计方法
    const count = await getAllRegisteredUsersCount();
    return count;

  } catch (error) {
    return 1; // 返回默认值
  }
}

/**
 * 添加用户到黑名单
 * @param {Object} params - 参数对象
 * @param {string} params.userAddress - 用户地址
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function addToBlacklist({ userAddress, signer }) {
  try {
    console.log('🚫 [addToBlacklist] 添加用户到黑名单:', userAddress);

    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }

    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'addToBlacklist',
      args: [userAddress],
      account: signerAddress,
    });

    console.log('📝 [addToBlacklist] 交易已提交:', txHash);

    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });

    console.log('✅ [addToBlacklist] 交易确认:', receipt);

    return { receipt, txHash };

  } catch (error) {
    console.error('❌ [addToBlacklist] 添加黑名单失败:', error);
    throw error;
  }
}

/**
 * 从黑名单移除用户
 * @param {Object} params - 参数对象
 * @param {string} params.userAddress - 用户地址
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function removeFromBlacklist({ userAddress, signer }) {
  try {
    console.log('✅ [removeFromBlacklist] 从黑名单移除用户:', userAddress);

    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }

    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'removeFromBlacklist',
      args: [userAddress],
      account: signerAddress,
    });

    console.log('📝 [removeFromBlacklist] 交易已提交:', txHash);

    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });

    console.log('✅ [removeFromBlacklist] 交易确认:', receipt);

    return { receipt, txHash };

  } catch (error) {
    console.error('❌ [removeFromBlacklist] 移除黑名单失败:', error);
    throw error;
  }
}

/**
 * 检查用户是否在黑名单中
 * @param {Object} params - 参数对象
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<boolean>} - 返回是否在黑名单中
 */
export async function isBlacklisted({ userAddress }) {
  try {
    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    const blacklisted = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'isBlacklisted',
      args: [userAddress],
    });

    return blacklisted;

  } catch (error) {
    console.error('❌ [isBlacklisted] 检查黑名单状态失败:', error);
    return false;
  }
}

/**
 * 获取管理员的直推用户列表
 * @returns {Promise<Array>} - 返回直推用户信息列表
 */
export async function getAdminReferrals() {
  try {
    console.log('👥 [getAdminReferrals] 获取管理员直推用户列表');

    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    // 获取系统管理员地址
    const systemAdmin = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'systemAdmin',
    });

    console.log('🔑 [getAdminReferrals] 系统管理员地址:', systemAdmin);

    // 这里需要通过事件日志或其他方式获取直推用户地址
    // 暂时使用一个简化的方法：从本地存储获取已知的用户地址
    const knownUserAddresses = [];

    try {
      const storedAddresses = localStorage.getItem('registeredUsers');
      if (storedAddresses) {
        const parsed = JSON.parse(storedAddresses);
        knownUserAddresses.push(...parsed);
      }
    } catch (error) {
      console.warn('⚠️ [getAdminReferrals] 读取本地存储的用户地址失败:', error);
    }

    // 如果本地存储为空，返回空数组
    if (knownUserAddresses.length === 0) {
      console.log('📝 [getAdminReferrals] 本地存储中没有用户地址');
      return [];
    }

    // 批量查询用户信息
    const userPromises = knownUserAddresses.map(async (address) => {
      try {
        const userInfo = await readContract(config, {
          address: contractAddress,
          abi: contractABI,
          functionName: 'getUserInfo',
          args: [address],
        });

        // 检查是否为直推用户（推荐人是系统管理员）
        const inviter = userInfo[0];
        const isDirectReferral = inviter.toLowerCase() === systemAdmin.toLowerCase();

        if (isDirectReferral && userInfo[4]) { // 已注册且为直推
          return {
            address,
            inviter,
            level: Number(userInfo[1]),
            totalPerformance: userInfo[2].toString(),
            referralsCount: Number(userInfo[3]),
            isRegistered: !!userInfo[4],
            personalPerformance: userInfo[5].toString(),
          };
        }
        return null;
      } catch (error) {
        console.error(`❌ [getAdminReferrals] 查询用户信息失败 (${address}):`, error);
        return null;
      }
    });

    const results = await Promise.all(userPromises);
    const directReferrals = results.filter(user => user !== null);

    console.log(`✅ [getAdminReferrals] 找到 ${directReferrals.length} 个直推用户`);
    return directReferrals;

  } catch (error) {
    console.error('❌ [getAdminReferrals] 获取管理员直推用户失败:', error);
    return [];
  }
}

/**
 * 获取用户在链上的信息（包含团队统计）
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object|null>} - 返回用户信息或null
 */
export async function getUserInfoOnChain(userAddress) {
  try {
    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const config = await getWagmiConfig();

    // 并行查询基本信息和团队统计
    const [userInfo, teamStats] = await Promise.all([
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getUserInfo',
        args: [userAddress],
      }),
      // 尝试获取团队统计，如果失败则返回默认值
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getTeamStats',
        args: [userAddress],
      }).catch(() => [0, 0, 0]) // 默认值：[totalMembers, totalPerformance, lastUpdateTime]
    ]);

    return {
      address: userAddress,
      inviter: userInfo[0],
      level: Number(userInfo[1]),
      totalPerformance: userInfo[2].toString(),
      referralsCount: Number(userInfo[3]),
      isRegistered: !!userInfo[4],
      personalPerformance: userInfo[5].toString(),
      // 添加团队统计数据
      teamStats: {
        totalMembers: Number(teamStats[0]),
        totalPerformance: Number(teamStats[1]),
        lastUpdateTime: Number(teamStats[2])
      }
    };
  } catch (error) {
    console.error('❌ [getUserInfoOnChain] 获取用户信息失败:', error);
    return null;
  }
}

/**
 * 检查用户是否在链上注册
 * @param {string} userAddress - 用户地址
 * @returns {Promise<boolean>} - 返回是否已注册
 */
export async function checkUserRegisteredOnChain(userAddress) {
  try {
    const userInfo = await getUserInfoOnChain(userAddress);
    return userInfo?.isRegistered || false;
  } catch {
    return false;
  }
}

/**
 * 检查 GroupBuyRoom 合约是否有 PERFORMANCE_UPLOADER 权限
 * @returns {Promise<boolean>} - 返回是否有权限
 */
export async function checkGroupBuyRoomPermission() {
  try {
    const { contractAddress, contractABI } = await getContractInfo('AgentSystem');
    const { getContractAddress } = await import('@/contracts/addresses');
    const config = await getWagmiConfig();

    const groupBuyRoomAddress = getContractAddress(97, 'GroupBuyRoom');

    // 获取 PERFORMANCE_UPLOADER 角色
    const uploaderRole = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'PERFORMANCE_UPLOADER',
      args: [],
    });

    // 检查 GroupBuyRoom 是否有该角色
    const hasRole = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'hasRole',
      args: [uploaderRole, groupBuyRoomAddress],
    });

    console.log(`🔍 [checkGroupBuyRoomPermission] GroupBuyRoom 权限检查:`, {
      groupBuyRoomAddress,
      uploaderRole,
      hasRole
    });

    return hasRole;
  } catch (error) {
    console.error('❌ [checkGroupBuyRoomPermission] 权限检查失败:', error);
    return false;
  }
}

/**
 * 初始化已知用户地址到本地存储（用于测试）
 * @returns {Promise<Array>} - 返回初始化的用户地址列表
 */
export async function initializeKnownUsers() {
  try {
    console.log('🔧 [initializeKnownUsers] 初始化已知用户地址...');

    // 已知的用户地址（根据您提到的9个注册用户）
    const knownAddresses = [
      '0xDf98905098CB4e5D261578f600337eeeFd4082b3', // 初始用户（管理员的直推）
      // 添加其他已知的注册用户地址
      '0xB3d93fF388F7B8eF434e50C30D2be57991353a1D',
      '0x1234567890123456789012345678901234567890',
      '0x2345678901234567890123456789012345678901',
      '0x3456789012345678901234567890123456789012',
      '0x4567890123456789012345678901234567890123',
      '0x5678901234567890123456789012345678901234',
      '0x6789012345678901234567890123456789012345',
      '0x7890123456789012345678901234567890123456',
    ];

    // 获取现有的用户地址
    const existingUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]');

    // 合并地址，去重
    const allUsers = [...new Set([...existingUsers, ...knownAddresses])];

    // 保存到本地存储
    localStorage.setItem('registeredUsers', JSON.stringify(allUsers));

    console.log('✅ [initializeKnownUsers] 已初始化用户地址:', allUsers);
    console.log('📊 [initializeKnownUsers] 用户总数:', allUsers.length);

    return allUsers;
  } catch (error) {
    console.error('🚨 [initializeKnownUsers] 初始化失败:', error);
    throw new Error(`初始化失败: ${error.message}`);
  }
}
