{"network": "bscTestnet", "timestamp": "2025-08-01T08:23:31.405Z", "deployer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "contract": {"name": "SimpleQueryContract", "proxyAddress": "0x7713b0C625054643D73F294d101e742078fF69f6", "implementationAddress": "0x91EE6aA6ED1f1972b1B10D5a372afc2aE071bEE9", "owner": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "timelock": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400"}, "contractAddresses": {"agentSystem": "0x9096769B22B53A464D40265420b9Ed5342b6ACb3", "groupBuyRoom": "0x8580424D25C3B5dAdb4D7a0bcB991C6e8d720551", "pointsManagement": "0x29bC33F518d741DC45c1857B6ee8f3F529E594cd", "nodeStaking": "0x04bD703Dd859f3ED9b643a72d538EAACdf6e45f5", "qptLocker": "0xcca28B4d5005819107948fe279893435a2A1728e", "qptBuyback": "0x23449063C3Fc3cc6361ef93Af4b602E0fC420dCE", "productManagement": "0xe91cc0ED2e94D8918FDe09585104824de824E756"}, "features": ["UUPS_UPGRADEABLE", "TIMELOCK_CONTROLLED", "BATCH_QUERY_SUPPORT", "RPC_SYNC_SOLUTION"]}