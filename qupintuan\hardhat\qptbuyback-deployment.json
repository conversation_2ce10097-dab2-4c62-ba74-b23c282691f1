{"proxyAddress": "0x23449063C3Fc3cc6361ef93Af4b602E0fC420dCE", "implementationAddress": "0xf66D545eC5b6E4929c22428B7D1732740b8E75Fe", "deployer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "owner": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "usdtToken": "0x2c6431CA0A2e7F5D2015179BdD7241fA1714DE86", "agentSystem": "0x9096769B22B53A464D40265420b9Ed5342b6ACb3", "qptToken": "0x1Ed10648278333D71E2d9fd880998bB775Ec221A", "adminAddress": "0xc05aaF414836c7AFdb0d60D51b44DA34cb9BbF7F", "timelock": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "timestamp": "2025-08-01T07:50:45.219Z", "network": "bscTestnet", "features": ["UUPS_UPGRADEABLE", "BNB_RECEIVE", "TIMELOCK_PROTECTED", "ADMIN_ONLY_CREATE_ROOMS", "USER_QPT_AUTHORIZATION_REQUIRED"], "businessLogic": {"adminCreateRoom": "管理员创建房间不需要锁仓QPT，只需要管理员权限", "userJoinRoom": "用户参与房间需要QPT授权和锁仓对应档位的QPT数量", "roomTiers": ["100 QPT", "200 QPT", "500 QPT", "1000 QPT"]}, "securityLevel": "HIGH"}