# 🔧 MerchantManagement 合约 UUPS 安全升级流程总结

## 🎯 升级目的

解决商城购买失败问题：允许 ProductManagement 合约调用 `updateMerchantSalesPoints` 函数。

## 📋 升级内容

### 合约修改
- ✅ 新增 `productManagementAddress` 状态变量
- ✅ 修改 `updateMerchantSalesPoints` 权限检查
- ✅ 新增 `setProductManagementAddress` 管理函数
- ✅ 新增 `getProductManagementAddress` 查询函数
- ✅ 新增 `ProductManagementUpdated` 事件

### 权限控制逻辑
```solidity
// 修改前
require(msg.sender == address(pointsManagement), "Not authorized");

// 修改后
require(
    msg.sender == address(pointsManagement) || 
    msg.sender == productManagementAddress, 
    "Not authorized"
);
```

## 🛡️ 4阶段安全升级流程

### ✅ 第一阶段：升级前验证
**脚本**: `scripts/upgrade/01-merchant-pre-upgrade-verification.js`

**验证内容**:
- ✅ 当前合约状态检查
- ✅ 存储数据测试
- ✅ 关键函数状态备份
- ⚠️ 升级前布局验证（跳过，升级时再验证）

**结果**: 
- 合约状态正常
- 存储数据完整
- 关键状态已备份到 `upgrade-backup-merchantmanagement.json`

### ✅ 第二阶段：部署新实现
**脚本**: `scripts/upgrade/02-merchant-deploy-new-implementation.js`

**部署内容**:
- ✅ 新实现合约部署成功
- ✅ 合约代码验证通过
- ✅ 存储布局兼容性确认

**结果**:
- 新实现合约地址: `0xC789526111F33C722895Cc81AAB5D5E3e9c37f3D`
- 部署信息已保存到 `upgrade-deployment-info.json`

### ✅ 第三阶段：兼容性测试
**脚本**: `scripts/upgrade/03-merchant-compatibility-test.js`

**测试内容**:
- ✅ 接口兼容性验证通过
- ⚠️ 存储布局兼容性检查（跳过）
- ✅ 功能完整性测试通过
- ✅ 升级模拟测试通过

**结果**:
- 所有兼容性测试通过
- 测试结果已保存到 `upgrade-test-results.json`

### ✅ 第四阶段：多签升级流程
**脚本**: `scripts/upgrade/04-generate-multisig-proposal.js`

**生成内容**:
- ✅ Timelock Schedule 调用数据
- ✅ Timelock Execute 调用数据
- ✅ 提案ID和执行时间计算

**结果**:
- 多签交易数据已保存到 `multisig-upgrade-transactions.json`
- 提案ID: `0x120e8c56a4edf6472bce34bbbb8538506994f5ce9d7dc4eba62b4fcc8de5b80e`
- 延迟期: 10分钟 (600秒)

## 🔐 多签钱包操作步骤

### 步骤1：创建升级提案
**目标合约**: `0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400` (Timelock)
**发送金额**: 0
**调用数据**: 
```
0x01d5062a00000000000000000000000013a311f1d52376861207ae641278f6e5de55f4b5000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000003f95a9de0d205f4a1dffd2d74cdaa80cd75673c7ba69b5c967ba865b53b9c98a000000000000000000000000000000000000000000000000000000000000025800000000000000000000000000000000000000000000000000000000000000243659cfe6000000000000000000000000c789526111f33c722895cc81aab5d5e3e9c37f3d00000000000000000000000000000000000000000000000000000000
```

### 步骤2：等待延迟期
**延迟时间**: 10分钟
**可执行时间**: `2025-08-01T01:52:40.000Z`

### 步骤3：执行升级
**目标合约**: `0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400` (Timelock)
**发送金额**: 0
**调用数据**:
```
0x134008d300000000000000000000000013a311f1d52376861207ae641278f6e5de55f4b5000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a000000000000000000000000000000000000000000000000000000000000000003f95a9de0d205f4a1dffd2d74cdaa80cd75673c7ba69b5c967ba865b53b9c98a00000000000000000000000000000000000000000000000000000000000000243659cfe6000000000000000000000000c789526111f33c722895cc81aab5d5e3e9c37f3d00000000000000000000000000000000000000000000000000000000
```

## 📋 升级后配置步骤

### 1. 验证升级成功
```bash
npx hardhat run scripts/upgrade/05-post-upgrade-verification.js --network bscTestnet
```

### 2. 设置 ProductManagement 地址
```bash
npx hardhat run scripts/configure-marketplace-permissions.js --network bscTestnet
```

### 3. 测试商城购买功能
- 进入商城页面
- 尝试购买商品
- 验证积分扣除和商家积分增加

## 🎯 预期效果

升级完成并配置后：
- ✅ **ProductManagement 可以调用** `updateMerchantSalesPoints`
- ✅ **商城购买功能正常**：用户可以成功购买商品
- ✅ **积分正确处理**：用户积分扣除，商家积分增加
- ✅ **权限安全可控**：只有授权的合约可以调用

## 📁 生成的文件

1. `upgrade-backup-merchantmanagement.json` - 升级前状态备份
2. `upgrade-deployment-info.json` - 新实现部署信息
3. `upgrade-test-results.json` - 兼容性测试结果
4. `multisig-upgrade-transactions.json` - 多签钱包交易数据

## ⚠️ 重要提醒

1. **多签确认**：步骤1和步骤3都需要多签钱包所有者确认
2. **延迟期限制**：步骤3必须在延迟期结束后才能执行
3. **备份重要**：请保存好所有生成的JSON文件
4. **测试验证**：升级后务必测试商城购买功能
5. **监控日志**：关注合约调用日志，确保权限正常工作

## 🔄 回滚方案

如果升级后出现问题，可以：
1. 通过相同的多签流程回滚到旧实现
2. 使用备份的状态数据验证回滚结果
3. 重新分析问题并准备新的升级方案

---

**升级流程已完成，等待多签钱包执行！** 🎉
