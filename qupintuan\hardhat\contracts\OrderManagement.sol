// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "./interfaces/IAddressManagement.sol";

/// @title OrderManagement - 订单管理合约
/// @notice 管理商品订单、发货状态和快递信息
contract OrderManagement is 
    Initializable, 
    OwnableUpgradeable, 
    ReentrancyGuardUpgradeable, 
    PausableUpgradeable, 
    UUPSUpgradeable 
{
    /// @notice 订单状态枚举
    enum OrderStatus {
        Pending,    // 待发货
        Shipped,    // 已发货
        Delivered,  // 已送达
        Completed   // 已完成
    }

    /// @notice 订单信息结构
    struct OrderInfo {
        uint256 orderId;
        address buyer;
        address merchant;
        uint256 productId;
        uint256 quantity;
        uint256 totalPrice;
        uint256 addressId;
        OrderStatus status;
        string trackingNumber;
        string shippingCompany;
        uint256 createTime;
        uint256 shippedTime;
        uint256 deliveredTime;
    }

    // —— Events —— //
    
    /// @notice 订单创建事件
    event OrderCreated(
        uint256 indexed orderId,
        address indexed buyer,
        address indexed merchant,
        uint256 productId,
        uint256 quantity,
        uint256 totalPrice
    );

    /// @notice 订单状态更新事件
    event OrderStatusUpdated(
        uint256 indexed orderId,
        address indexed merchant,
        address indexed buyer,
        OrderStatus newStatus,
        uint256 timestamp
    );

    /// @notice 订单发货事件
    event OrderShipped(
        uint256 indexed orderId,
        address indexed merchant,
        address indexed buyer,
        string trackingNumber,
        string shippingCompany,
        uint256 shippedTime
    );

    // —— State Variables —— //
    
    mapping(uint256 => OrderInfo) public orders;
    mapping(address => uint256[]) public buyerOrders;    // 买家订单列表
    mapping(address => uint256[]) public merchantOrders; // 商家订单列表
    uint256 public orderCount;

    // 授权的合约地址（只有 ProductManagement 可以创建订单）
    mapping(address => bool) public authorizedContracts;

    // Timelock 合约地址（最终所有者）
    address public timelock;

    // —— Modifiers —— //
    
    modifier onlyAuthorized() {
        require(authorizedContracts[msg.sender], "Not authorized");
        _;
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /// @notice 初始化合约
    /// @param _timelock Timelock合约地址（最终所有者）
    function initialize(address _timelock) public initializer {
        __Ownable_init();
        __ReentrancyGuard_init();
        __Pausable_init();
        __UUPSUpgradeable_init();

        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;

        // 初始化时部署者是临时所有者，配置完成后会转移给 Timelock
    }

    /// @notice 授权升级（仅所有者）
    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    // —— 管理函数 —— //

    /// @notice 设置授权合约
    /// @param contractAddr 合约地址
    /// @param authorized 是否授权
    function setAuthorizedContract(address contractAddr, bool authorized) external onlyOwner {
        authorizedContracts[contractAddr] = authorized;
    }

    /// @notice 转移所有权给 Timelock（仅在初始配置完成后调用）
    /// @dev 这是一个不可逆操作，调用后只能通过 Timelock 管理合约
    function transferOwnershipToTimelock() external onlyOwner {
        require(timelock != address(0), "Timelock not set");
        require(owner() != timelock, "Already owned by timelock");

        // 转移所有权给 Timelock
        _transferOwnership(timelock);

        emit OwnershipTransferred(msg.sender, timelock);
    }

    /// @notice 设置新的 Timelock 地址（仅所有者）
    /// @param _timelock 新的 Timelock 地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    // —— 订单管理函数 —— //

    /// @notice 创建订单（仅授权合约可调用）
    /// @param buyer 买家地址
    /// @param merchant 商家地址
    /// @param productId 商品ID
    /// @param quantity 数量
    /// @param totalPrice 总价
    /// @param addressId 地址ID
    /// @return orderId 订单ID
    function createOrder(
        address buyer,
        address merchant,
        uint256 productId,
        uint256 quantity,
        uint256 totalPrice,
        uint256 addressId
    ) external onlyAuthorized returns (uint256 orderId) {
        orderCount++;
        orderId = orderCount;
        
        orders[orderId] = OrderInfo({
            orderId: orderId,
            buyer: buyer,
            merchant: merchant,
            productId: productId,
            quantity: quantity,
            totalPrice: totalPrice,
            addressId: addressId,
            status: OrderStatus.Pending,
            trackingNumber: "",
            shippingCompany: "",
            createTime: block.timestamp,
            shippedTime: 0,
            deliveredTime: 0
        });

        // 添加到买家和商家的订单列表
        buyerOrders[buyer].push(orderId);
        merchantOrders[merchant].push(orderId);

        emit OrderCreated(orderId, buyer, merchant, productId, quantity, totalPrice);
        return orderId;
    }

    /// @notice 商家发货
    /// @param orderId 订单ID
    /// @param trackingNumber 快递单号
    /// @param shippingCompany 快递公司
    function shipOrder(
        uint256 orderId,
        string calldata trackingNumber,
        string calldata shippingCompany
    ) external whenNotPaused nonReentrant {
        require(orderId > 0 && orderId <= orderCount, "Invalid order ID");
        
        OrderInfo storage order = orders[orderId];
        require(order.merchant == msg.sender, "Not the merchant");
        require(order.status == OrderStatus.Pending, "Order already shipped");
        require(bytes(trackingNumber).length > 0, "Tracking number required");
        require(bytes(shippingCompany).length > 0, "Shipping company required");

        // 更新订单状态
        order.status = OrderStatus.Shipped;
        order.trackingNumber = trackingNumber;
        order.shippingCompany = shippingCompany;
        order.shippedTime = block.timestamp;

        emit OrderShipped(orderId, msg.sender, order.buyer, trackingNumber, shippingCompany, block.timestamp);
        emit OrderStatusUpdated(orderId, msg.sender, order.buyer, OrderStatus.Shipped, block.timestamp);
    }

    /// @notice 更新订单状态
    /// @param orderId 订单ID
    /// @param newStatus 新状态
    function updateOrderStatus(
        uint256 orderId,
        OrderStatus newStatus
    ) external whenNotPaused nonReentrant {
        require(orderId > 0 && orderId <= orderCount, "Invalid order ID");
        
        OrderInfo storage order = orders[orderId];
        require(order.merchant == msg.sender, "Not the merchant");

        // 状态转换验证
        if (newStatus == OrderStatus.Delivered) {
            require(order.status == OrderStatus.Shipped, "Order must be shipped first");
            order.deliveredTime = block.timestamp;
        } else if (newStatus == OrderStatus.Completed) {
            require(order.status == OrderStatus.Delivered, "Order must be delivered first");
        }

        order.status = newStatus;
        emit OrderStatusUpdated(orderId, msg.sender, order.buyer, newStatus, block.timestamp);
    }

    // —— 查询函数 —— //

    /// @notice 获取商家的订单列表
    /// @param merchant 商家地址
    /// @return orderIds 订单ID数组
    function getMerchantOrders(address merchant) external view returns (uint256[] memory) {
        return merchantOrders[merchant];
    }

    /// @notice 获取买家的订单列表
    /// @param buyer 买家地址
    /// @return orderIds 订单ID数组
    function getBuyerOrders(address buyer) external view returns (uint256[] memory) {
        return buyerOrders[buyer];
    }

    /// @notice 获取订单详细信息
    /// @param orderId 订单ID
    /// @return order 订单信息
    function getOrderInfo(uint256 orderId) external view returns (OrderInfo memory) {
        require(orderId > 0 && orderId <= orderCount, "Invalid order ID");
        return orders[orderId];
    }

    /// @notice 批量获取订单信息
    /// @param orderIds 订单ID数组
    /// @return orderInfos 订单信息数组
    function getOrderInfoBatch(uint256[] calldata orderIds) external view returns (OrderInfo[] memory) {
        OrderInfo[] memory orderInfos = new OrderInfo[](orderIds.length);
        for (uint256 i = 0; i < orderIds.length; i++) {
            if (orderIds[i] > 0 && orderIds[i] <= orderCount) {
                orderInfos[i] = orders[orderIds[i]];
            }
        }
        return orderInfos;
    }

    /// @notice 暂停合约
    function pause() external onlyOwner {
        _pause();
    }

    /// @notice 恢复合约
    function unpause() external onlyOwner {
        _unpause();
    }
}
