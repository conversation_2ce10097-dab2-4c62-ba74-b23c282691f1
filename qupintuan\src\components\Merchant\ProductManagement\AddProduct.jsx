// src/components/Merchant/ProductManagement/AddProduct.jsx
import { useState, useRef } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { createProduct } from '@/apis/mallApi';
import { uploadToCloudinary } from '@/utils/cloudinaryUpload';
import { IMAGE_STANDARDS } from '@/utils/imageValidation';
import IPFSImageUpload from '@/components/Common/IPFSImageUpload';
import './AddProduct.css';

export default function AddProduct({ onProductAdded }) {
  const { address: account } = useAccount();

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    stock: '',
    category: 'electronics'
  });

  // 图片上传状态 (使用新的IPFS组件)
  const [uploadedImages, setUploadedImages] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const imageUploadRef = useRef(null);

  // 商品分类选项
  const categories = [
    { value: 'electronics', label: '📱 电子产品' },
    { value: 'clothing', label: '👕 服装配饰' },
    { value: 'food', label: '🍎 食品饮料' },
    { value: 'books', label: '📚 图书文具' },
    { value: 'home', label: '🏠 家居用品' },
    { value: 'sports', label: '⚽ 运动户外' },
    { value: 'beauty', label: '💄 美妆护肤' },
    { value: 'other', label: '🔧 其他商品' }
  ];

  // 处理表单输入
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理图片预览变化（不自动上传）
  const handleImagesChange = (images) => {
    setUploadedImages(images);
  };

  // 处理上传进度
  const handleUploadProgress = (fileIndex, progress) => {
    // 上传进度更新
  };

  // 处理上传完成
  const handleUploadComplete = (results) => {
    setIsUploading(false);
  };

  // 处理上传错误
  const handleUploadError = (error) => {
    setIsUploading(false);
  };

  // 获取已成功上传的图片哈希
  const getUploadedImageHashes = () => {
    return uploadedImages
      .filter(img => img.success && img.hash)
      .map(img => img.hash);
  };



  // 提交表单
  const handleSubmit = async (e) => {
    e.preventDefault();

    // 表单验证
    if (!formData.name.trim()) {
      toast.error('请输入商品名称');
      return;
    }
    if (!formData.description.trim()) {
      toast.error('请输入商品描述');
      return;
    }
    if (!formData.price || parseFloat(formData.price) <= 0) {
      toast.error('请输入有效的商品价格');
      return;
    }
    if (!formData.stock || parseInt(formData.stock) <= 0) {
      toast.error('请输入有效的库存数量');
      return;
    }
    // 检查图片选择状态（预览模式下检查是否有图片文件）
    if (uploadedImages.length === 0) {
      toast.error('请至少选择一张商品图片');
      return;
    }

    // 检查是否有正在上传的图片（在预览模式下应该没有）
    if (uploadedImages.some(img => img.uploading)) {
      toast.error('请等待图片上传完成');
      return;
    }

    // 检查是否有错误的图片
    if (uploadedImages.some(img => img.error)) {
      toast.error('存在图片错误，请重新选择');
      return;
    }

    setIsSubmitting(true);
    setIsUploading(true);

    try {
      // 1. 先上传所有图片到 IPFS
      let imageHashes = [];

      if (imageUploadRef.current && uploadedImages.length > 0) {
        toast.loading('正在上传图片到 IPFS...', { id: 'upload-images' });
        try {
          const uploadResults = await imageUploadRef.current.uploadAllPreviews();
          imageHashes = uploadResults.map(result => result.hash);
          toast.success(`成功上传 ${imageHashes.length} 张图片`, { id: 'upload-images' });
        } catch (uploadError) {
          toast.error('图片上传失败，请重试', { id: 'upload-images' });
          throw uploadError;
        }
      } else {
        toast.error('没有可上传的图片');
        return;
      }

      // 2. 创建signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        address: account
      };

      // 3. 调用合约创建商品
      // 导入积分格式化工具来正确转换价格（使用6位精度）
      const { parsePointsInput } = await import('@/utils/pointsFormatter');

      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        images: imageHashes, // 存储IPFS哈希或备用URL
        price: parsePointsInput(formData.price.toString()), // 使用6位精度转换价格
        stock: parseInt(formData.stock),
        signer
      };

      const result = await createProduct(productData);



      toast.success('🎉 商品创建成功！', {
        duration: 4000,
        position: 'top-center',
      });

      // 重置表单
      setFormData({
        name: '',
        description: '',
        price: '',
        stock: '',
        category: 'electronics'
      });
      setUploadedImages([]);

      // 通知父组件刷新列表
      if (onProductAdded) {
        onProductAdded(result);
      }

    } catch (error) {


      let errorMessage = error.message;
      if (error.message.includes('Not a merchant')) {
        errorMessage = '您不是认证商家，无法创建商品';
      } else if (error.message.includes('请先注册代理系统')) {
        errorMessage = '请先注册代理系统才能创建商品';
      }

      toast.error(`商品创建失败: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
    }
  };

  return (
    <div className="add-product">
      <div className="form-header">
        <h4>➕ 添加新商品</h4>
        <p>填写商品信息并上传图片</p>
      </div>

      <form onSubmit={handleSubmit} className="product-form">
        {/* 基本信息 */}
        <div className="form-section">
          <h5>📝 基本信息</h5>

          <div className="form-group">
            <label htmlFor="name">商品名称 *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="请输入商品名称"
              maxLength={100}
              required
            />
            <div className="char-count">{formData.name.length}/100</div>
          </div>

          <div className="form-group">
            <label htmlFor="description">商品描述 *</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="请详细描述商品特点、规格、使用方法等"
              rows={4}
              maxLength={500}
              required
            />
            <div className="char-count">{formData.description.length}/500</div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="price">价格 (积分) *</label>
              <input
                type="number"
                id="price"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                placeholder="0"
                min="0.01"
                step="0.01"
                required
              />
              <div className="form-hint">用户将使用拼团积分购买，支持小数点</div>
            </div>

            <div className="form-group">
              <label htmlFor="stock">库存数量 *</label>
              <input
                type="number"
                id="stock"
                name="stock"
                value={formData.stock}
                onChange={handleInputChange}
                placeholder="0"
                min="1"
                step="1"
                required
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="category">商品分类</label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
            >
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* IPFS 图片上传 */}
        <div className="form-section">
          <h5>📷 商品图片</h5>
          <p className="section-desc">使用 IPFS 去中心化存储，最多上传{IMAGE_STANDARDS.maxImages}张图片</p>

          <IPFSImageUpload
            ref={imageUploadRef}
            maxImages={IMAGE_STANDARDS.maxImages}
            onImagesChange={handleImagesChange}
            onUploadProgress={handleUploadProgress}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            initialImages={uploadedImages}
            disabled={isSubmitting}
            previewOnly={true}
            autoUpload={false}
            uploadOptions={{
              productId: null, // 商品创建时还没有ID
              merchantAddress: account
            }}
          />
        </div>

        {/* 提交按钮 */}
        <div className="form-actions">
          <button
            type="submit"
            className="submit-btn"
            disabled={isSubmitting || isUploading}
          >
            {isSubmitting ? '⏳ 创建中...' :
             (isUploading || uploadedImages.some(img => img.uploading)) ? '📤 上传图片中...' :
             '✅ 创建商品'}
          </button>
        </div>
      </form>
    </div>
  );
}
