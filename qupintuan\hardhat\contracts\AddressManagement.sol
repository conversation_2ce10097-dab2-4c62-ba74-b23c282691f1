// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol";
import "./StorageValidator.sol";

contract AddressManagement is
    Initializable,
    StorageValidator,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using AddressUpgradeable for address;

    // Timelock 控制
    address public timelock;

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    /// @notice 权限检查修饰符：用户本人、合约所有者或授权合约
    /// @param user 目标用户地址
    modifier onlyAuthorized(address user) {
        require(
            msg.sender == user ||
            msg.sender == owner() ||
            authorizedContracts[msg.sender],
            "Unauthorized"
        );
        _;
    }

    // —— 常量 —— //
    uint256 public constant MAX_ADDRESSES_PER_USER = 20;
    uint256 public constant NO_DEFAULT = type(uint256).max;

    uint256 public constant MAX_NAME_LENGTH     = 64;
    uint256 public constant MAX_PHONE_LENGTH    = 32;
    uint256 public constant MAX_REGION_LENGTH   = 64;  // province/city/district
    uint256 public constant MAX_DETAIL_LENGTH   = 256;

    // —— 结构体 —— //
    struct AddressInfo {
        uint256 addressId;     // 地址ID
        address user;          // 用户地址
        string name;           // 收货人姓名
        string phone;          // 联系电话
        string province;       // 省份
        string city;           // 城市
        string district;       // 区县
        string detail;         // 详细地址
        bool isDefault;        // 是否默认地址
        uint256 createTime;    // 创建时间
    }

    // —— 状态变量 —— //
    mapping(address => AddressInfo[]) private _userAddresses;
    mapping(address => uint256)        private _defaultAddressIds;

    // —— 授权机制 —— //
    /// @notice 授权合约映射，允许特定合约访问用户地址信息
    mapping(address => bool) public authorizedContracts;

    // —— 事件 —— //
    event AddressAdded(address indexed user, uint256 addressId);
    event AddressUpdated(address indexed user, uint256 addressId);
    event AddressDeleted(address indexed user, uint256 addressId);
    event DefaultAddressChanged(address indexed user, uint256 addressId);
    event ContractAuthorized(address indexed contractAddr, bool authorized);

    /// @notice 初始化合约
    /// @dev 调用此函数来初始化合约，只能调用一次
    /// @param _timelock Timelock合约地址
    function initialize(address _timelock) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    /// @notice 设置Timelock地址（仅所有者）
    /// @param _timelock 新的Timelock地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    /// @notice 设置授权合约（仅所有者）
    /// @param contractAddr 合约地址
    /// @param authorized 是否授权
    function setAuthorizedContract(address contractAddr, bool authorized) external onlyOwner {
        require(contractAddr != address(0), "Invalid contract address");
        require(contractAddr.isContract(), "Address must be a contract");

        authorizedContracts[contractAddr] = authorized;
        emit ContractAuthorized(contractAddr, authorized);
    }

    /// @notice 批量设置授权合约（仅所有者）
    /// @param contractAddrs 合约地址数组
    /// @param authorized 是否授权
    function setAuthorizedContracts(address[] calldata contractAddrs, bool authorized) external onlyOwner {
        for (uint256 i = 0; i < contractAddrs.length; i++) {
            require(contractAddrs[i] != address(0), "Invalid contract address");
            require(contractAddrs[i].isContract(), "Address must be a contract");

            authorizedContracts[contractAddrs[i]] = authorized;
            emit ContractAuthorized(contractAddrs[i], authorized);
        }
    }

    /// @notice 检查合约是否被授权
    /// @param contractAddr 合约地址
    /// @return 是否被授权
    function isAuthorizedContract(address contractAddr) external view returns (bool) {
        return authorizedContracts[contractAddr];
    }

    /// @notice 添加一个新地址
    /// @param name 收货人姓名
    /// @param phone 联系电话
    /// @param province 省份
    /// @param city 城市
    /// @param district 区县
    /// @param detail 详细地址
    /// @param isDefault 是否设置为默认地址
    function addAddress(
        string memory name,
        string memory phone,
        string memory province,
        string memory city,
        string memory district,
        string memory detail,
        bool isDefault
    ) external whenNotPaused nonReentrant {
        require(!msg.sender.isContract(), "No contracts");
        require(bytes(name).length     > 0 && bytes(name).length     <= MAX_NAME_LENGTH,   "Invalid name length");
        require(bytes(phone).length    > 0 && bytes(phone).length    <= MAX_PHONE_LENGTH,  "Invalid phone length");
        require(bytes(province).length > 0 && bytes(province).length <= MAX_REGION_LENGTH, "Invalid province length");
        require(bytes(city).length     > 0 && bytes(city).length     <= MAX_REGION_LENGTH, "Invalid city length");
        require(bytes(district).length > 0 && bytes(district).length <= MAX_REGION_LENGTH, "Invalid district length");
        require(bytes(detail).length   > 0 && bytes(detail).length   <= MAX_DETAIL_LENGTH, "Invalid detail length");

        AddressInfo[] storage arr = _userAddresses[msg.sender];
        require(arr.length < MAX_ADDRESSES_PER_USER, "Address limit reached");

        uint256 addressId = arr.length;

        if (isDefault) {
            _clearDefault(msg.sender);
            _defaultAddressIds[msg.sender] = addressId;
        }

        arr.push(AddressInfo({
            addressId:   addressId,
            user:        msg.sender,
            name:        name,
            phone:       phone,
            province:    province,
            city:        city,
            district:    district,
            detail:      detail,
            isDefault:   isDefault,
            createTime:  block.timestamp
        }));

        // 第一次添加且未主动设默认，自动设为默认
        if (arr.length == 1 && !isDefault) {
            arr[0].isDefault = true;
            _defaultAddressIds[msg.sender] = 0;
            emit DefaultAddressChanged(msg.sender, 0);
        }

        emit AddressAdded(msg.sender, addressId);
    }

    /// @notice 更新指定地址的信息
    /// @param addressId 要更新的地址ID
    /// @param name 收货人姓名
    /// @param phone 联系电话
    /// @param province 省份
    /// @param city 城市
    /// @param district 区县
    /// @param detail 详细地址
    /// @param isDefault 是否设置为默认地址
    function updateAddress(
        uint256 addressId,
        string memory name,
        string memory phone,
        string memory province,
        string memory city,
        string memory district,
        string memory detail,
        bool isDefault
    ) external whenNotPaused nonReentrant {
        require(!msg.sender.isContract(), "No contracts");
        AddressInfo[] storage arr = _userAddresses[msg.sender];
        require(addressId < arr.length, "Invalid address ID");
        require(bytes(name).length     > 0 && bytes(name).length     <= MAX_NAME_LENGTH,   "Invalid name length");
        require(bytes(phone).length    > 0 && bytes(phone).length    <= MAX_PHONE_LENGTH,  "Invalid phone length");
        require(bytes(province).length > 0 && bytes(province).length <= MAX_REGION_LENGTH, "Invalid province length");
        require(bytes(city).length     > 0 && bytes(city).length     <= MAX_REGION_LENGTH, "Invalid city length");
        require(bytes(district).length > 0 && bytes(district).length <= MAX_REGION_LENGTH, "Invalid district length");
        require(bytes(detail).length   > 0 && bytes(detail).length   <= MAX_DETAIL_LENGTH, "Invalid detail length");

        if (isDefault) {
            _clearDefault(msg.sender);
            _defaultAddressIds[msg.sender] = addressId;
        }

        AddressInfo storage info = arr[addressId];
        bool wasDefault = info.isDefault;
        info.name      = name;
        info.phone     = phone;
        info.province  = province;
        info.city      = city;
        info.district  = district;
        info.detail    = detail;
        info.isDefault = isDefault;

        if (isDefault && !wasDefault) {
            emit DefaultAddressChanged(msg.sender, addressId);
        }

        emit AddressUpdated(msg.sender, addressId);
    }

    /// @notice 删除指定地址
    /// @param addressId 要删除的地址ID
    function deleteAddress(uint256 addressId) external whenNotPaused nonReentrant {
        require(!msg.sender.isContract(), "No contracts");
        AddressInfo[] storage arr = _userAddresses[msg.sender];
        uint256 len = arr.length;
        require(addressId < len, "Invalid address ID");

        // 如果删除的是默认地址，清除映射
        if (_defaultAddressIds[msg.sender] == addressId) {
            _defaultAddressIds[msg.sender] = NO_DEFAULT;
            emit DefaultAddressChanged(msg.sender, NO_DEFAULT);
        }

        // 用最后一条替换
        if (addressId != len - 1) {
            arr[addressId] = arr[len - 1];
            arr[addressId].addressId = addressId;
        }
        arr.pop();

        emit AddressDeleted(msg.sender, addressId);
    }

    /// @notice 获取用户的所有地址
    /// @param user 用户地址
    /// @return 返回用户的所有地址信息
    function getUserAddresses(address user) external view returns (AddressInfo[] memory) {
        require(msg.sender == user || msg.sender == owner(), "Unauthorized");
        return _userAddresses[user];
    }

    /// @notice 获取调用者自己的所有地址
    /// @return 返回调用者的所有地址信息
    function getMyAddresses() external view returns (AddressInfo[] memory) {
        return _userAddresses[msg.sender];
    }

    /// @notice 获取用户的默认地址
    /// @param user 用户地址
    /// @return 返回用户的默认地址信息
    function getDefaultAddress(address user) external view onlyAuthorized(user) returns (AddressInfo memory) {
        uint256 id = _defaultAddressIds[user];
        require(id != NO_DEFAULT && id < _userAddresses[user].length, "No default address");
        return _userAddresses[user][id];
    }

    /// @notice 获取调用者自己的默认地址
    /// @return 返回调用者的默认地址信息
    function getMyDefaultAddress() external view returns (AddressInfo memory) {
        uint256 id = _defaultAddressIds[msg.sender];
        require(id != NO_DEFAULT && id < _userAddresses[msg.sender].length, "No default address");
        return _userAddresses[msg.sender][id];
    }

    /// @notice 设置默认地址（快速切换功能）
    /// @param addressId 要设置为默认的地址ID
    function setDefaultAddress(uint256 addressId) external whenNotPaused nonReentrant {
        require(!msg.sender.isContract(), "No contracts");
        AddressInfo[] storage arr = _userAddresses[msg.sender];
        require(addressId < arr.length, "Invalid address ID");
        require(arr[addressId].user == msg.sender, "Address user mismatch");

        // 清除之前的默认地址标记
        _clearDefault(msg.sender);

        // 设置新的默认地址
        arr[addressId].isDefault = true;
        _defaultAddressIds[msg.sender] = addressId;

        emit DefaultAddressChanged(msg.sender, addressId);
    }

    /// @notice 获取用户的指定地址
    /// @param user 用户地址
    /// @param addressId 地址ID
    /// @return 返回用户的指定地址信息
    function getAddress(address user, uint256 addressId) external view onlyAuthorized(user) returns (AddressInfo memory) {
        require(addressId < _userAddresses[user].length, "Invalid address ID");
        return _userAddresses[user][addressId];
    }

    // —— 内部 —— //
    function _clearDefault(address user) internal {
        AddressInfo[] storage arr = _userAddresses[user];
        for (uint256 i = 0; i < arr.length; i++) {
            arr[i].isDefault = false;
        }
        _defaultAddressIds[user] = NO_DEFAULT;
    }

    // —— 管理 —— //
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证地址管理合约基本状态
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // AddressManagement通常不需要复杂的存储修复
        emit StorageFixed(address(this), "AddressManagement storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // AddressManagement 的升级前状态保存
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // AddressManagement 的紧急修复模式设置
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // AddressManagement 的升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }
}
