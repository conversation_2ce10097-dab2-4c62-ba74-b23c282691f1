import { Routes, Route, Navigate } from 'react-router-dom';
import PriceFixTest from './PriceFixTest';
import AddressDebug from '@/components/Debug/AddressDebug';
import ContractAddressDebug from '@/components/Debug/ContractAddressDebug';

export default function DebugRoutes() {
  return (
    <Routes>
      <Route path="price-fix" element={<PriceFixTest />} />
      <Route path="address-debug" element={<AddressDebug />} />
      <Route path="contract-debug" element={<ContractAddressDebug />} />
      <Route path="*" element={<Navigate to="price-fix" replace />} />
    </Routes>
  );
}
