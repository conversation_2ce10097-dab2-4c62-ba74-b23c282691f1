const { ethers } = require("hardhat");

async function main() {
  console.log("🔧 开始授权 ProductManagement 合约访问 AddressManagement...");

  // 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log("📝 部署者地址:", deployer.address);

  // 获取合约地址
  const addressManagementAddress = "******************************************";
  const productManagementAddress = "******************************************";

  console.log("📍 AddressManagement 地址:", addressManagementAddress);
  console.log("📍 ProductManagement 地址:", productManagementAddress);

  // 获取 AddressManagement 合约实例
  const AddressManagement = await ethers.getContractFactory("AddressManagement");
  const addressManagement = AddressManagement.attach(addressManagementAddress);

  try {
    // 检查当前授权状态
    console.log("🔍 检查当前授权状态...");
    const isAuthorized = await addressManagement.authorizedContracts(productManagementAddress);
    console.log("📊 当前授权状态:", isAuthorized);

    if (isAuthorized) {
      console.log("✅ ProductManagement 已经被授权，无需重复操作");
      return;
    }

    // 授权 ProductManagement 合约
    console.log("🚀 正在授权 ProductManagement 合约...");
    const tx = await addressManagement.setAuthorizedContract(productManagementAddress, true);
    console.log("📝 交易哈希:", tx.hash);

    // 等待交易确认
    console.log("⏳ 等待交易确认...");
    const receipt = await tx.wait();
    console.log("✅ 交易确认成功，区块号:", receipt.blockNumber);

    // 验证授权结果
    console.log("🔍 验证授权结果...");
    const newAuthStatus = await addressManagement.authorizedContracts(productManagementAddress);
    console.log("📊 新的授权状态:", newAuthStatus);

    if (newAuthStatus) {
      console.log("🎉 ProductManagement 合约授权成功！");
      console.log("💡 现在用户可以正常购买商品了");
    } else {
      console.log("❌ 授权失败，请检查交易状态");
    }

  } catch (error) {
    console.error("❌ 授权过程中发生错误:", error);
    
    if (error.message.includes("Ownable: caller is not the owner")) {
      console.log("💡 提示：只有合约所有者才能执行授权操作");
      console.log("📝 请确保使用正确的部署者账户");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 脚本执行失败:", error);
    process.exit(1);
  });
