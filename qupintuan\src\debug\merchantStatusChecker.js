// 商家状态调试工具
import { readContract } from 'wagmi/actions';
import { config } from '@/wagmi.config';
import { getContractAddress } from '@/contracts/addresses';
import { ABIS } from '@/contracts/index';
import { isMerchant } from '@/apis/mallApi';

/**
 * 检查商家状态的调试工具
 * @param {string} userAddress - 用户地址
 * @returns {Object} 详细的商家状态信息
 */
export async function debugMerchantStatus(userAddress) {
  console.log('🔍 [调试] 开始检查商家状态:', userAddress);
  
  const result = {
    userAddress,
    timestamp: new Date().toISOString(),
    api: null,
    contract: null,
    diagnosis: [],
    solutions: []
  };

  try {
    // 1. 检查 API 返回的商家状态
    console.log('📡 [调试] 检查 API 商家状态...');
    result.api = await isMerchant({ userAddress });
    console.log('📡 [调试] API 结果:', result.api);

    // 2. 检查合约中的商家状态
    console.log('⛓️ [调试] 检查合约商家状态...');
    const mmAddress = getContractAddress(97, 'MerchantManagement');
    const mmABI = ABIS.MerchantManagement;

    // 检查 isMerchant 函数
    const contractIsMerchant = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'isMerchant',
      args: [userAddress],
    });

    // 检查原始商家信息
    const merchantInfo = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'merchants',
      args: [userAddress],
    });

    // 检查认证信息
    const verificationInfo = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'verifications',
      args: [userAddress],
    });

    result.contract = {
      isMerchant: contractIsMerchant,
      merchantInfo: {
        name: merchantInfo[0],
        description: merchantInfo[1],
        logo: merchantInfo[2],
        isActive: merchantInfo[3],
        createTime: Number(merchantInfo[4]),
        totalSales: Number(merchantInfo[5]),
        totalOrders: Number(merchantInfo[6]),
        totalPoints: Number(merchantInfo[7]),
        exchangedPoints: Number(merchantInfo[8])
      },
      verificationInfo: {
        isVerified: verificationInfo[0],
        verifyTime: Number(verificationInfo[1])
      }
    };

    console.log('⛓️ [调试] 合约结果:', result.contract);

    // 3. 诊断问题
    console.log('🔍 [调试] 开始诊断...');
    
    // 检查注册状态
    if (!result.api.isRegistered) {
      result.diagnosis.push('❌ 用户未注册商家');
      result.solutions.push('前往个人中心 → 商家入驻 → 注册商家身份');
    } else {
      result.diagnosis.push('✅ 用户已注册商家');
    }

    // 检查认证状态
    if (!result.api.isVerified) {
      result.diagnosis.push('❌ 用户未通过商家认证');
      result.solutions.push('提交商家认证申请，等待管理员审核');
    } else {
      result.diagnosis.push('✅ 用户已通过商家认证');
    }

    // 检查合约状态
    if (!result.contract.isMerchant) {
      result.diagnosis.push('❌ 合约中 isMerchant() 返回 false');
      result.solutions.push('合约状态异常，需要管理员重新认证或检查合约');
    } else {
      result.diagnosis.push('✅ 合约中 isMerchant() 返回 true');
    }

    // 检查状态一致性
    if (result.api.isVerified !== result.contract.isMerchant) {
      result.diagnosis.push('⚠️ API 和合约状态不一致');
      result.solutions.push('状态不一致，可能需要重新同步或检查合约升级');
    } else {
      result.diagnosis.push('✅ API 和合约状态一致');
    }

    // 检查页面访问权限
    const pageAccessAllowed = result.api.isVerified && result.contract.isMerchant;
    if (!pageAccessAllowed) {
      result.diagnosis.push('❌ 不满足商家管理页面访问条件');
      result.solutions.push('页面要求: API.isVerified && Contract.isMerchant 都为 true');
    } else {
      result.diagnosis.push('✅ 满足商家管理页面访问条件');
    }

    // 检查创建商品权限
    const createProductAllowed = result.contract.isMerchant;
    if (!createProductAllowed) {
      result.diagnosis.push('❌ 不满足创建商品的合约权限');
      result.solutions.push('合约要求: isMerchant() 返回 true');
    } else {
      result.diagnosis.push('✅ 满足创建商品的合约权限');
    }

    console.log('🎯 [调试] 诊断完成:', result.diagnosis);
    console.log('💡 [调试] 解决方案:', result.solutions);

  } catch (error) {
    console.error('❌ [调试] 检查商家状态失败:', error);
    result.error = error.message;
    result.diagnosis.push(`❌ 检查失败: ${error.message}`);
    result.solutions.push('检查网络连接和合约地址配置');
  }

  return result;
}

/**
 * 在控制台中运行调试
 * @param {string} userAddress - 用户地址（可选，默认使用当前连接的账户）
 */
export async function runMerchantStatusDebug(userAddress = null) {
  try {
    // 如果没有提供地址，尝试获取当前连接的账户
    if (!userAddress) {
      const { getAccount } = await import('wagmi/actions');
      const account = getAccount(config);
      userAddress = account.address;
    }

    if (!userAddress) {
      console.error('❌ [调试] 无法获取用户地址，请先连接钱包');
      return;
    }

    console.log('🚀 [调试] 开始商家状态调试...');
    const result = await debugMerchantStatus(userAddress);
    
    console.log('📊 [调试] 完整结果:');
    console.table({
      '用户地址': result.userAddress,
      'API-已注册': result.api?.isRegistered,
      'API-已认证': result.api?.isVerified,
      '合约-isMerchant': result.contract?.isMerchant,
      '页面访问权限': result.api?.isVerified && result.contract?.isMerchant,
      '创建商品权限': result.contract?.isMerchant
    });

    console.log('🔍 [调试] 诊断结果:');
    result.diagnosis.forEach(d => console.log(d));

    console.log('💡 [调试] 解决方案:');
    result.solutions.forEach(s => console.log(s));

    return result;

  } catch (error) {
    console.error('❌ [调试] 运行调试失败:', error);
  }
}

// 导出到全局作用域，方便在控制台中使用
if (typeof window !== 'undefined') {
  window.debugMerchantStatus = runMerchantStatusDebug;
}
