// scripts/deploy-OrderManagement.js
// 部署 OrderManagement 合约

const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 部署 OrderManagement 合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 获取部署者信息
  const [deployer] = await ethers.getSigners();
  console.log("📝 部署者地址:", deployer.address);

  const balance = await deployer.provider.getBalance(deployer.address);
  console.log("💰 部署者余额:", ethers.formatEther(balance), "BNB");

  try {
    // 从环境变量获取 Timelock 地址
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;

    console.log("📋 配置信息:");
    console.log("   • Timelock地址:", timelockAddr);

    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
    }

    // 获取合约工厂
    console.log("⏳ 获取 OrderManagement 合约工厂...");
    const OrderManagement = await ethers.getContractFactory("OrderManagement");

    // 部署代理合约
    console.log("⏳ 部署 OrderManagement 代理合约...");
    const orderManagement = await upgrades.deployProxy(
      OrderManagement,
      [timelockAddr], // 传递 timelock 地址
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    await orderManagement.waitForDeployment();
    const proxyAddress = await orderManagement.getAddress();

    console.log("✅ OrderManagement 合约部署成功！");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("📋 合约信息:");
    console.log("   • 代理合约地址:", proxyAddress);
    console.log("   • 部署者地址:", deployer.address);
    console.log("   • 网络:", network.name);
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    // 验证合约功能
    console.log("🔍 验证合约功能...");
    
    try {
      const orderCount = await orderManagement.orderCount();
      console.log("📊 初始订单数量:", orderCount.toString());
      
      const owner = await orderManagement.owner();
      console.log("👑 合约所有者:", owner);
      
      console.log("✅ 合约功能验证成功");
    } catch (error) {
      console.error("❌ 合约功能验证失败:", error.message);
    }

    // 输出部署总结
    console.log("\n🎉 部署完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("📦 合约名称: OrderManagement");
    console.log("🔗 代理地址:", proxyAddress);
    console.log("🌐 网络:", network.name);
    const finalBalance = await deployer.provider.getBalance(deployer.address);
    console.log("💰 部署成本: 约", ethers.formatEther(balance - finalBalance), "BNB");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    console.log("\n📋 下一步操作:");
    console.log("1. 将 OrderManagement 地址添加到 .env 文件: ORDER_MANAGEMENT_ADDRESS=" + proxyAddress);
    console.log("2. 部署 ProductManagement 合约时会自动配置 OrderManagement 地址");
    console.log("3. 部署完成后需要在 OrderManagement 中授权 ProductManagement 合约");
    console.log("4. 授权完成后，将 OrderManagement 所有权转移给 Timelock");
    console.log("5. 更新前端配置文件中的合约地址");

    console.log("\n⚠️  重要提醒:");
    console.log("• 当前所有者: " + deployer.address + " (部署者)");
    console.log("• 最终所有者: " + timelockAddr + " (Timelock)");
    console.log("• 完成初始配置后，请务必转移所有权给 Timelock！");
    
    // 保存部署信息到文件
    const deploymentInfo = {
      contractName: "OrderManagement",
      proxyAddress: proxyAddress,
      deployer: deployer.address,
      network: network.name,
      deployTime: new Date().toISOString(),
      blockNumber: await ethers.provider.getBlockNumber()
    };

    const fs = require('fs');
    const path = require('path');
    
    // 确保目录存在
    const deploymentsDir = path.join(__dirname, '../deployments');
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    // 保存部署信息
    const deploymentFile = path.join(deploymentsDir, `OrderManagement-${network.name}.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("💾 部署信息已保存到:", deploymentFile);

  } catch (error) {
    console.error("❌ 部署失败:", error);
    process.exit(1);
  }
}

// 错误处理
main()
  .then(() => {
    console.log("🎯 部署脚本执行完成");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 部署脚本执行失败:", error);
    process.exit(1);
  });
