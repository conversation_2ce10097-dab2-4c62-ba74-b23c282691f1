// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

import "./interfaces/IFeeSplitManager.sol";
import "./interfaces/IGroupBuyRoom.sol";
import "./interfaces/INodeStaking.sol";
import "./StorageValidator.sol";

/**
 * @title FeeSplitManager
 * @dev 管理系统费率的提案与执行，并计算/拆分系统费给积分、节点质押、回购和平台
 */
contract FeeSplitManager is
    Initializable,
    StorageValidator,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable,
    IFeeSplitManager
{
    using SafeERC20Upgradeable for IERC20Upgradeable;

    /// @notice 费率基数，10000 表示 100%
    uint16 public constant RATE_BASE = 10000;
    /// @notice 管理操作需延迟时间
    uint64 public constant ADMIN_DELAY = 24 hours;

    /// @notice Timelock 合约地址，只有它能执行费率更新
    address public timelock;

    // 移除百分比费率变量，只使用固定金额分配

    /// @notice 支持的拼团档位 (USDT, 6位小数)
    uint256[6] public supportedTiers;

    /// @notice 积分固定分配金额 (对应各档位)
    uint256[6] public pointsAmounts;

    /// @notice 节点质押分配金额 (对应各档位)
    uint256[6] public nodeAmounts;

    /// @notice 回购分配金额 (对应各档位)
    uint256[6] public buybackAmounts;

    /// @notice 平台收益配置：[档位索引][代理等级] => 平台收益金额
    /// 档位索引: 0=30U, 1=50U, 2=100U, 3=200U, 4=500U, 5=1000U
    /// 代理等级: 0-4级
    mapping(uint256 => mapping(uint256 => uint256)) public platformAmounts;

    // 移除百分比费率相关的状态变量和事件

    // -- Event for fee splitting --
    event FeesDistributed(
        uint256 indexed roomId,
        uint256 pointsAmt,
        uint256 nodeAmt,
        uint256 buybackAmt,
        uint256 platformAmt,
        address indexed sender,
        address token,
        uint256 systemFee,
        address pointsCollector,
        address nodeStaking,
        address buyback,
        address platform
    );

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }
    modifier onlyGroupBuyRoom() {
        require(IGroupBuyRoom(msg.sender).isGroupBuyRoom(), "Caller is not GroupBuyRoom");
        _;
    }

    /// @notice 初始化合约
    /// @param _timelock Timelock合约地址
    function initialize(address _timelock) public initializer {
        __Ownable_init();
        __ReentrancyGuard_init();
        __Pausable_init();
        __UUPSUpgradeable_init();

        require(_timelock != address(0), "Zero address");
        timelock = _timelock;

        // 初始化支持的拼团档位 (USDT, 6位小数)
        supportedTiers[0] = 30000000;   // 30 USDT
        supportedTiers[1] = 50000000;   // 50 USDT
        supportedTiers[2] = 100000000;  // 100 USDT
        supportedTiers[3] = 200000000;  // 200 USDT
        supportedTiers[4] = 500000000;  // 500 USDT
        supportedTiers[5] = 1000000000; // 1000 USDT

        // 初始化积分固定分配金额 (30%)
        pointsAmounts[0] = 9000000;    // 30档: 9 USDT
        pointsAmounts[1] = 15000000;   // 50档: 15 USDT
        pointsAmounts[2] = 30000000;   // 100档: 30 USDT
        pointsAmounts[3] = 60000000;   // 200档: 60 USDT
        pointsAmounts[4] = 150000000;  // 500档: 150 USDT
        pointsAmounts[5] = 300000000;  // 1000档: 300 USDT

        // 初始化节点质押分配金额 (默认组合5: 1%)
        nodeAmounts[0] = 300000;     // 30档: 0.3 USDT
        nodeAmounts[1] = 500000;     // 50档: 0.5 USDT
        nodeAmounts[2] = 1000000;    // 100档: 1 USDT
        nodeAmounts[3] = 2000000;    // 200档: 2 USDT
        nodeAmounts[4] = 5000000;    // 500档: 5 USDT
        nodeAmounts[5] = 10000000;   // 1000档: 10 USDT

        // 初始化回购分配金额 (默认组合5: 5%)
        buybackAmounts[0] = 1500000;    // 30档: 1.5 USDT
        buybackAmounts[1] = 2500000;    // 50档: 2.5 USDT
        buybackAmounts[2] = 5000000;    // 100档: 5 USDT
        buybackAmounts[3] = 10000000;   // 200档: 10 USDT
        buybackAmounts[4] = 25000000;   // 500档: 25 USDT
        buybackAmounts[5] = 50000000;   // 1000档: 50 USDT

        // 初始化平台收益配置
        _initializePlatformAmounts();
    }

    /// @notice 初始化平台收益配置
    function _initializePlatformAmounts() private {
        // 30 USDT档位 (索引0)
        platformAmounts[0][0] = 11400000;  // 0级: 11.4 USDT
        platformAmounts[0][1] = 9900000;   // 1级: 9.9 USDT
        platformAmounts[0][2] = 8400000;   // 2级: 8.4 USDT
        platformAmounts[0][3] = 6900000;   // 3级: 6.9 USDT
        platformAmounts[0][4] = 5400000;   // 4级: 5.4 USDT

        // 50 USDT档位 (索引1)
        platformAmounts[1][0] = 19000000;  // 0级: 19 USDT
        platformAmounts[1][1] = 16500000;  // 1级: 16.5 USDT
        platformAmounts[1][2] = 14000000;  // 2级: 14 USDT
        platformAmounts[1][3] = 11500000;  // 3级: 11.5 USDT
        platformAmounts[1][4] = 9000000;   // 4级: 9 USDT

        // 100 USDT档位 (索引2)
        platformAmounts[2][0] = 31000000;  // 0级: 31 USDT
        platformAmounts[2][1] = 26000000;  // 1级: 26 USDT
        platformAmounts[2][2] = 21000000;  // 2级: 21 USDT
        platformAmounts[2][3] = 16000000;  // 3级: 16 USDT
        platformAmounts[2][4] = 11000000;  // 4级: 11 USDT

        // 200 USDT档位 (索引3)
        platformAmounts[3][0] = 62000000;  // 0级: 62 USDT
        platformAmounts[3][1] = 52000000;  // 1级: 52 USDT
        platformAmounts[3][2] = 42000000;  // 2级: 42 USDT
        platformAmounts[3][3] = 32000000;  // 3级: 32 USDT
        platformAmounts[3][4] = 22000000;  // 4级: 22 USDT

        // 500 USDT档位 (索引4)
        platformAmounts[4][0] = 120000000; // 0级: 120 USDT
        platformAmounts[4][1] = 95000000;  // 1级: 95 USDT
        platformAmounts[4][2] = 70000000;  // 2级: 70 USDT
        platformAmounts[4][3] = 45000000;  // 3级: 45 USDT
        platformAmounts[4][4] = 20000000;  // 4级: 20 USDT

        // 1000 USDT档位 (索引5)
        platformAmounts[5][0] = 240000000; // 0级: 240 USDT
        platformAmounts[5][1] = 190000000; // 1级: 190 USDT
        platformAmounts[5][2] = 140000000; // 2级: 140 USDT
        platformAmounts[5][3] = 90000000;  // 3级: 90 USDT
        platformAmounts[5][4] = 40000000;  // 4级: 40 USDT
    }

    /// @notice 手动初始化平台收益配置（升级后调用）
    function initializePlatformAmounts() external onlyOwner {
        _initializePlatformAmounts();
    }

    /// @notice 设置 Timelock 合约地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Zero address");
        timelock = _timelock;
    }

    // 移除所有百分比费率相关函数，只使用固定金额分配

    // --- 固定金额分配管理函数 ---
    /// @notice 设置节点质押和回购的分配组合（所有者可调用）
    /// @param combination 组合编号 (1-5)
    /// 1: 质押5%+回购1%, 2: 质押4%+回购2%, 3: 质押3%+回购3%, 4: 质押2%+回购4%, 5: 质押1%+回购5%
    function setNodeBuybackCombination(uint8 combination) external onlyOwner nonReentrant whenNotPaused {
        require(combination >= 1 && combination <= 5, "Invalid combination");

        if (combination == 1) {
            // 质押5% + 回购1%
            nodeAmounts = [1500000, 2500000, 5000000, 10000000, 25000000, 50000000];
            buybackAmounts = [300000, 500000, 1000000, 2000000, 5000000, 10000000];
        } else if (combination == 2) {
            // 质押4% + 回购2%
            nodeAmounts = [1200000, 2000000, 4000000, 8000000, 20000000, 40000000];
            buybackAmounts = [600000, 1000000, 2000000, 4000000, 10000000, 20000000];
        } else if (combination == 3) {
            // 质押3% + 回购3%
            nodeAmounts = [900000, 1500000, 3000000, 6000000, 15000000, 30000000];
            buybackAmounts = [900000, 1500000, 3000000, 6000000, 15000000, 30000000];
        } else if (combination == 4) {
            // 质押2% + 回购4%
            nodeAmounts = [600000, 1000000, 2000000, 4000000, 10000000, 20000000];
            buybackAmounts = [1200000, 2000000, 4000000, 8000000, 20000000, 40000000];
        } else if (combination == 5) {
            // 质押1% + 回购5%
            nodeAmounts = [300000, 500000, 1000000, 2000000, 5000000, 10000000];
            buybackAmounts = [1500000, 2500000, 5000000, 10000000, 25000000, 50000000];
        }

        emit NodeBuybackCombinationUpdated(combination);
    }

    /// @notice 获取当前分配组合信息
    /// @return combination 当前组合编号
    /// @return nodePercentage 节点质押百分比
    /// @return buybackPercentage 回购百分比
    function getCurrentCombination() external view returns (uint8 combination, uint8 nodePercentage, uint8 buybackPercentage) {
        // 通过检查第一个档位的金额来确定当前组合
        uint256 nodeAmt = nodeAmounts[0]; // 30档的节点分配

        if (nodeAmt == 1500000) return (1, 5, 1);      // 1.5 USDT = 5%
        else if (nodeAmt == 1200000) return (2, 4, 2); // 1.2 USDT = 4%
        else if (nodeAmt == 900000) return (3, 3, 3);  // 0.9 USDT = 3%
        else if (nodeAmt == 600000) return (4, 2, 4);  // 0.6 USDT = 2%
        else if (nodeAmt == 300000) return (5, 1, 5);  // 0.3 USDT = 1%
        else return (0, 0, 0); // 未知组合
    }

    // 新增事件
    event NodeBuybackCombinationUpdated(uint8 indexed combination);
    event PlatformAmountUpdated(uint256 indexed tierIndex, uint256 indexed agentLevel, uint256 amount);

    /// @notice 根据拼团档位获取固定分配金额
    /// @dev 这是 FeeSplitManager 的核心功能：告诉拼团合约应该分配多少给各个合约
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return pointsAmt 积分合约应该获得的固定金额
    /// @return nodeAmt 节点质押合约应该获得的固定金额
    /// @return buybackAmt 回购合约应该获得的固定金额
    function getFixedAllocations(uint256 tier)
        external
        view
        returns (
            uint256 pointsAmt,
            uint256 nodeAmt,
            uint256 buybackAmt
        )
    {
        // 查找档位索引
        uint256 tierIndex = type(uint256).max;
        for (uint256 i = 0; i < supportedTiers.length; i++) {
            if (supportedTiers[i] == tier) {
                tierIndex = i;
                break;
            }
        }
        require(tierIndex != type(uint256).max, "Unsupported tier");

        // 返回固定分配金额
        pointsAmt = pointsAmounts[tierIndex];
        nodeAmt = nodeAmounts[tierIndex];
        buybackAmt = buybackAmounts[tierIndex];
    }

    /// @notice 检查档位是否支持
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return supported 是否支持该档位
    function isTierSupported(uint256 tier) external view returns (bool supported) {
        for (uint256 i = 0; i < supportedTiers.length; i++) {
            if (supportedTiers[i] == tier) {
                return true;
            }
        }
        return false;
    }

    /// @notice 获取所有支持的档位
    /// @return tiers 支持的档位数组
    function getSupportedTiers() external view returns (uint256[6] memory tiers) {
        return supportedTiers;
    }

    /// @notice 获取指定档位的积分分配金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return pointsAmt 积分合约应该获得的金额
    function getPointsAllocation(uint256 tier) external view returns (uint256 pointsAmt) {
        (pointsAmt,,) = this.getFixedAllocations(tier);
    }

    /// @notice 获取指定档位的节点质押分配金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return nodeAmt 节点质押合约应该获得的金额
    function getNodeStakingAllocation(uint256 tier) external view returns (uint256 nodeAmt) {
        (,nodeAmt,) = this.getFixedAllocations(tier);
    }

    /// @notice 获取指定档位的回购分配金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return buybackAmt 回购合约应该获得的金额
    function getBuybackAllocation(uint256 tier) external view returns (uint256 buybackAmt) {
        (,,buybackAmt) = this.getFixedAllocations(tier);
    }

    /// @notice 获取指定档位的总固定分配金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return totalFixed 三个合约的总分配金额
    function getTotalFixedAllocation(uint256 tier) external view returns (uint256 totalFixed) {
        (uint256 pointsAmt, uint256 nodeAmt, uint256 buybackAmt) = this.getFixedAllocations(tier);
        totalFixed = pointsAmt + nodeAmt + buybackAmt;
    }

    /// @notice 获取平台收益金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @param agentLevel 发起人代理等级 (0-4)
    /// @return platformAmt 平台收益金额
    function getPlatformAllocation(uint256 tier, uint256 agentLevel) external view returns (uint256 platformAmt) {
        // 查找档位索引
        uint256 tierIndex = type(uint256).max;
        for (uint256 i = 0; i < supportedTiers.length; i++) {
            if (supportedTiers[i] == tier) {
                tierIndex = i;
                break;
            }
        }
        require(tierIndex != type(uint256).max, "Unsupported tier");
        require(agentLevel <= 4, "Invalid agent level");

        return platformAmounts[tierIndex][agentLevel];
    }

    /// @notice 根据拼团档位计算固定分配金额 (保留用于兼容)
    /// @dev 保留此函数以兼容现有接口，但建议使用 getFixedAllocations
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @return pointsAmt 积分分配金额
    /// @return nodeAmt 节点质押分配金额
    /// @return buybackAmt 回购分配金额
    /// @return platformAmt 平台分配金额 (始终为0，平台收益由拼团合约计算)
    function calcSplitAmountsByTier(uint256 tier)
        external
        view
        returns (
            uint256 pointsAmt,
            uint256 nodeAmt,
            uint256 buybackAmt,
            uint256 platformAmt
        )
    {
        (pointsAmt, nodeAmt, buybackAmt) = this.getFixedAllocations(tier);
        platformAmt = 0; // 平台收益由拼团合约计算，FeeSplitManager 不管理
    }

    /// @notice 根据拼团档位和代理等级计算完整分配金额
    /// @param tier 拼团档位金额 (USDT, 6位小数)
    /// @param agentLevel 发起人代理等级 (0-4)
    /// @return pointsAmt 积分分配金额
    /// @return nodeAmt 节点质押分配金额
    /// @return buybackAmt 回购分配金额
    /// @return platformAmt 平台分配金额
    function calcSplitAmountsByTierAndLevel(uint256 tier, uint256 agentLevel)
        external
        view
        returns (
            uint256 pointsAmt,
            uint256 nodeAmt,
            uint256 buybackAmt,
            uint256 platformAmt
        )
    {
        (pointsAmt, nodeAmt, buybackAmt) = this.getFixedAllocations(tier);
        platformAmt = this.getPlatformAllocation(tier, agentLevel);
    }

    /// @notice 设置平台收益金额 (只有所有者可调用)
    /// @param tierIndex 档位索引 (0-5)
    /// @param agentLevel 代理等级 (0-4)
    /// @param amount 平台收益金额 (USDT, 6位小数)
    function setPlatformAmount(uint256 tierIndex, uint256 agentLevel, uint256 amount) external onlyOwner {
        require(tierIndex < supportedTiers.length, "Invalid tier index");
        require(agentLevel <= 4, "Invalid agent level");

        platformAmounts[tierIndex][agentLevel] = amount;
        emit PlatformAmountUpdated(tierIndex, agentLevel, amount);
    }

    /// @notice 批量设置某个档位的所有代理等级平台收益
    /// @param tierIndex 档位索引 (0-5)
    /// @param amounts 5个代理等级的平台收益金额数组
    function setPlatformAmountsForTier(uint256 tierIndex, uint256[5] calldata amounts) external onlyOwner {
        require(tierIndex < supportedTiers.length, "Invalid tier index");

        for (uint256 i = 0; i < 5; i++) {
            platformAmounts[tierIndex][i] = amounts[i];
            emit PlatformAmountUpdated(tierIndex, i, amounts[i]);
        }
    }

    // 移除百分比分配函数，只使用固定金额分配

    /// @inheritdoc IFeeSplitManager
    function distribute(
        address token,
        uint256 roomId,
        uint256 systemFee,
        address pointsCollector,
        address nodeStaking,
        address buyback,
        address platform
    ) external override onlyGroupBuyRoom nonReentrant {
        // distribute 函数已废弃，不再使用
        // GroupBuyRoom 应该直接调用 calcSplitAmountsByTier 并自行分配
        revert("Use calcSplitAmountsByTier in GroupBuyRoom instead");
    }

    /// @notice 清算合约残余余额
    function clearResidualBalance(address token, address platform) external onlyOwner {
        uint256 balance = IERC20Upgradeable(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20Upgradeable(token).safeTransfer(platform, balance);
        }
    }

    /// @notice 接收BNB转账，用于支付Gas费
    /// @dev 合约需要BNB来执行主动转账操作
    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 备用接收函数
    fallback() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 查询合约BNB余额
    /// @return 合约当前BNB余额（wei）
    function getBNBBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice 管理员提取BNB（紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量（wei）
    function withdrawBNB(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount <= address(this).balance, "Insufficient balance");

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");

        emit BNBWithdrawn(to, amount);
    }

    /// @notice 暂停合约
    function pause() external onlyOwner {
        _pause();
    }

    /// @notice 恢复合约
    function unpause() external onlyOwner {
        _unpause();
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证Timelock地址
        if (address(timelock) == address(0)) return false;

        // 验证支持的档位数量
        if (supportedTiers.length != 6) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            address(timelock),
            supportedTiers[0],
            supportedTiers[1],
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // FeeSplitManager的存储修复逻辑
        emit StorageFixed(address(this), "FeeSplitManager storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    /// @notice 升级授权（只有Timelock可以升级）
    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    // BNB相关事件
    event BNBReceived(address indexed sender, uint256 amount);
    event BNBWithdrawn(address indexed to, uint256 amount);

    // Reserved storage space to allow for layout changes in the future.
    uint256[50] private __gap;
}
