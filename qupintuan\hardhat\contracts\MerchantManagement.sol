// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol"; // 新增:导入 ContextUpgradeable
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "./StorageValidator.sol";

// 创建接口来避免payable转换问题
interface IPointsManagement {
    function exchangeGoods(address user, address merchant, uint256 points) external;
}

// 修改:继承StorageValidator以获得存储验证功能
contract MerchantManagement is
    Initializable,
    ContextUpgradeable,
    StorageValidator, // 新增:继承存储验证基类
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    // Timelock 控制
    address public timelock;

    // 系统管理员配置
    address public systemAdmin;

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    modifier onlySystemAdmin() {
        require(msg.sender == systemAdmin, "Only System Admin");
        _;
    }

    modifier onlyAdminOrTimelock() {
        require(msg.sender == systemAdmin || msg.sender == timelock, "Only Admin or Timelock");
        _;
    }
    // —— 商家信息 —— //
    struct MerchantInfo {
        string name;                // 商家名称
        string description;         // 商家描述
        string logo;                // 商家logo IPFS hash
        bool isActive;              // 是否激活
        uint256 createTime;         // 创建时间
        uint256 totalSales;         // 总销售额
        uint256 totalOrders;        // 总订单数
        uint256 totalPoints;        // 总积分
        uint256 exchangedPoints;    // 已兑换积分
    }

    struct MerchantVerification {
        bool isVerified;            // 是否已认证
        uint256 verifyTime;         // 认证时间
    }

    // —— 状态变量 —— //
    mapping(address => MerchantInfo) public merchants;
    mapping(address => MerchantVerification) public verifications;

    // 新增：商家计数器和地址列表
    uint256 public merchantCounter;
    address[] public merchantAddresses;
    mapping(address => bool) public isMerchantRegistered;

    IPointsManagement public pointsManagement;

    /// @custom:badge 黑名单
    mapping(address => bool) public blacklist;

    // 新增：商城管理合约地址（升级时添加到末尾）
    address public productManagementAddress;

    // —— 事件 —— //
    event MerchantRegistered(address indexed merchant, string name);
    event MerchantVerified(address indexed merchant);
    event MerchantInfoUpdated(address indexed merchant);
    event MerchantSalesPointsUpdated(address indexed merchant, uint256 points);
    event MerchantVerificationSubmitted(address indexed merchant);
    event MerchantVerificationRejected(address indexed merchant);
    event MerchantVerificationReset(address indexed merchant);
    event MerchantExchangedPoints(address indexed merchant, uint256 points);
    event MerchantOrdersUpdated(address indexed merchant, uint256 orders);

    /// @custom:badge 黑名单
    event BlacklistUpdated(address indexed merchant, bool isBlacklisted);

    // 管理员相关事件
    event SystemAdminUpdated(address indexed oldAdmin, address indexed newAdmin);
    event TimelockUpdated(address indexed oldTimelock, address indexed newTimelock);
    event ProductManagementUpdated(address indexed oldAddress, address indexed newAddress);

    // —— 初始化 —— //
    /// @notice 初始化合约，设置积分管理合约地址
    /// @param _pointsManagement 积分管理合约地址
    /// @param _timelock Timelock合约地址
    /// @param _systemAdmin 系统管理员地址
    function initialize(address _pointsManagement, address _timelock, address _systemAdmin) public initializer {
        __Context_init();                  // 如果你后面用到了 _msgSender()，记得先 init Context
        __Ownable_init();      // 修改:将 __Ownable_init() 改为 __Ownable_init(_msgSender())
        __ReentrancyGuard_init();
        __Pausable_init();
        __UUPSUpgradeable_init();
        require(_pointsManagement != address(0), "Zero address");
        require(_timelock != address(0), "Invalid timelock");
        require(_systemAdmin != address(0), "Invalid system admin");
        pointsManagement = IPointsManagement(_pointsManagement);
        timelock = _timelock;
        systemAdmin = _systemAdmin;
    }

    /// @notice 设置Timelock地址（仅所有者）
    /// @param _timelock 新的Timelock地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        address oldTimelock = timelock;
        timelock = _timelock;
        emit TimelockUpdated(oldTimelock, _timelock);
    }

    /// @notice 设置系统管理员地址（仅所有者）
    /// @param _systemAdmin 新的系统管理员地址
    function setSystemAdmin(address _systemAdmin) external onlyOwner {
        require(_systemAdmin != address(0), "Invalid system admin");
        address oldAdmin = systemAdmin;
        systemAdmin = _systemAdmin;
        emit SystemAdminUpdated(oldAdmin, _systemAdmin);
    }

    /// @notice 设置商城管理合约地址（仅所有者）
    /// @param _productManagement 新的商城管理合约地址
    function setProductManagementAddress(address _productManagement) external onlyOwner {
        require(_productManagement != address(0), "Invalid product management address");
        address oldAddress = productManagementAddress;
        productManagementAddress = _productManagement;
        emit ProductManagementUpdated(oldAddress, _productManagement);
    }

    /// @notice 获取系统管理员地址
    /// @return 系统管理员地址
    function getSystemAdmin() external view returns (address) {
        return systemAdmin;
    }

    /// @notice 获取商城管理合约地址
    /// @return 商城管理合约地址
    function getProductManagementAddress() external view returns (address) {
        return productManagementAddress;
    }

    // —— 黑名单 —— ///
    /// @custom:badge 黑名单
    modifier notBlacklisted(address merchant) {
        require(!blacklist[merchant], "Merchant is blacklisted");
        _;
    }

    /// @notice 将商家地址加入黑名单
    /// @param merchant 商家地址
    function addToBlacklist(address merchant) external onlyAdminOrTimelock {
        blacklist[merchant] = true;
        if (merchants[merchant].createTime != 0) {
            merchants[merchant].isActive = false;
            emit MerchantInfoUpdated(merchant);
        }
        emit BlacklistUpdated(merchant, true);
    }

    /// @notice 将商家地址从黑名单中移除
    /// @param merchant 商家地址
    function removeFromBlacklist(address merchant) external onlyAdminOrTimelock {
        blacklist[merchant] = false;
        emit BlacklistUpdated(merchant, false);
    }

    // 新增内部函数，用于校验字符串长度
    function _checkLength(bytes memory data, uint256 max, string memory err) internal pure {
        require(data.length <= max, err);
    }

    // —— 核心接口 —— //
    /// @notice 商家注册
    /// @param name 商家名称
    /// @param description 商家描述
    /// @param logo 商家logo IPFS hash
    function registerMerchant(
        string memory name,
        string memory description,
        string memory logo
    ) external whenNotPaused notBlacklisted(msg.sender) {
        require(merchants[msg.sender].createTime == 0, "Already registered"); // 修改: 使用 createTime 判断是否已注册
        _checkLength(bytes(name), 100, "Name too long");
        _checkLength(bytes(description), 500, "Description too long");
        _checkLength(bytes(logo), 64, "Logo too long");

        merchants[msg.sender] = MerchantInfo({
            name: name,
            description: description,
            logo: logo,
            isActive: false,
            createTime: block.timestamp,
            totalSales: 0,
            totalOrders: 0,
            totalPoints: 0,
            exchangedPoints: 0
        });

        // 新增：更新计数器和地址列表
        if (!isMerchantRegistered[msg.sender]) {
            // 修复异常的merchantCounter
            if (merchantCounter == 0 || merchantCounter > 1000000) {
                merchantCounter = merchantAddresses.length;
            }

            merchantCounter++;
            merchantAddresses.push(msg.sender);
            isMerchantRegistered[msg.sender] = true;
        }

        emit MerchantRegistered(msg.sender, name);
    }

    /// @notice 提交商家认证申请
    function submitVerification() external whenNotPaused notBlacklisted(msg.sender) {
        require(merchants[msg.sender].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(!verifications[msg.sender].isVerified, "Already verified");

        // 新增：检查是否已经提交过申请（防止重复提交）
        require(!hasSubmittedVerification(msg.sender), "Verification already submitted");

        verifications[msg.sender] = MerchantVerification({
            isVerified: false,
            verifyTime: block.timestamp // 修改：记录提交时间而不是0
        });
        emit MerchantVerificationSubmitted(msg.sender);
    }

    /// @notice 认证商家
    /// @param merchant 商家地址
    function verifyMerchant(address merchant) external onlyAdminOrTimelock whenNotPaused notBlacklisted(merchant) {
        require(merchants[merchant].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(!verifications[merchant].isVerified, "Already verified");
        // 临时注释：允许管理员直接审核，无需提交申请
        // require(hasSubmittedVerification(merchant), "No verification submitted"); // 新增：必须先提交申请

        // 如果没有 verification 记录，创建一个
        if (verifications[merchant].verifyTime == 0) {
            verifications[merchant] = MerchantVerification({
                isVerified: false,
                verifyTime: block.timestamp
            });
        }

        verifications[merchant].isVerified = true;
        verifications[merchant].verifyTime = block.timestamp;
        merchants[merchant].isActive = true;
        emit MerchantVerified(merchant);
    }

    /// @notice 拒绝商家认证申请
    /// @param merchant 商家地址
    function rejectMerchantVerification(address merchant) external onlyAdminOrTimelock whenNotPaused {
        require(merchants[merchant].createTime != 0, "Not a merchant");
        require(hasSubmittedVerification(merchant), "No verification submitted");
        require(!verifications[merchant].isVerified, "Already verified");

        // 重置申请状态，允许重新提交
        delete verifications[merchant];
        emit MerchantVerificationRejected(merchant);
    }

    /// @notice 重置商家认证申请状态（管理员工具）
    /// @param merchant 商家地址
    function resetMerchantVerification(address merchant) external onlyAdminOrTimelock {
        require(merchants[merchant].createTime != 0, "Not a merchant");

        // 完全重置认证状态
        delete verifications[merchant];
        merchants[merchant].isActive = false;
        emit MerchantVerificationReset(merchant);
    }

    /// @notice 更新商家信息
    /// @param name 商家名称
    /// @param description 商家描述
    /// @param logo 商家logo IPFS hash
    function updateMerchantInfo(
        string memory name,
        string memory description,
        string memory logo
    ) external whenNotPaused notBlacklisted(msg.sender) {
        require(merchants[msg.sender].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(merchants[msg.sender].isActive, "Not active");
        _checkLength(bytes(name), 100, "Name too long");
        _checkLength(bytes(description), 500, "Desc too long");
        _checkLength(bytes(logo), 64, "Logo too long");

        merchants[msg.sender].name = name;
        merchants[msg.sender].description = description;
        merchants[msg.sender].logo = logo;
        emit MerchantInfoUpdated(msg.sender);
    }

    /// @notice 更新商家订单数量
    /// @param merchant 商家地址
    /// @param orders 新增订单数量
    function updateMerchantOrders(address merchant, uint256 orders)
        external
        whenNotPaused
        nonReentrant
        notBlacklisted(merchant)
    {
        require(msg.sender == address(pointsManagement), "Not authorized");
        require(merchants[merchant].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(merchants[merchant].isActive, "Not active");

        merchants[merchant].totalOrders += orders;
        emit MerchantOrdersUpdated(merchant, orders);
    }

    /// @notice 更新商家销售积分
    /// @param merchant 商家地址
    /// @param points 新增销售积分
    function updateMerchantSalesPoints(address merchant, uint256 points)
        external
        whenNotPaused
        nonReentrant
        notBlacklisted(merchant)
    {
        require(
            msg.sender == address(pointsManagement) ||
            msg.sender == productManagementAddress,
            "Not authorized"
        );
        require(merchants[merchant].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(merchants[merchant].isActive, "Not active");

        merchants[merchant].totalPoints += points;
        merchants[merchant].totalSales += points;
        emit MerchantSalesPointsUpdated(merchant, points);
    }

    /// @notice 更新商家已兑换积分
    /// @param merchant 商家地址
    /// @param points 新增已兑换积分
    function updateMerchantExchangedPoints(address merchant, uint256 points)
        external
        whenNotPaused
        nonReentrant
        notBlacklisted(merchant)
    {
        require(msg.sender == address(pointsManagement), "Not authorized");
        require(merchants[merchant].createTime != 0, "Not a merchant"); // 修改: 使用 createTime 判断是否是商家
        require(merchants[merchant].isActive, "Not active");

        merchants[merchant].exchangedPoints += points;
        emit MerchantExchangedPoints(merchant, points);
    }

    // —— 查询接口 —— //
    /// @notice 获取商家基本信息
    /// @param merchant 商家地址
    /// @return name 商家名称
    /// @return description 商家描述
    /// @return logo 商家logo IPFS hash
    /// @return isActive 是否激活
    function getMerchantInfo(address merchant)
        external
        view
        returns (
            string memory name,
            string memory description,
            string memory logo,
            bool isActive
        )
    {
        MerchantInfo memory info = merchants[merchant];
        return (info.name, info.description, info.logo, info.isActive);
    }

    /// @notice 获取商家积分统计信息
    /// @param merchant 商家地址
    /// @return totalPoints 总积分
    /// @return exchangedPoints 已兑换积分
    function getMerchantPointsStats(address merchant)
        external
        view
        returns (uint256 totalPoints, uint256 exchangedPoints)
    {
        MerchantInfo memory info = merchants[merchant];
        return (info.totalPoints, info.exchangedPoints);
    }

    // 新增查询接口，返回商家的总订单数
    /// @notice 获取商家总订单数
    /// @param merchant 商家地址
    /// @return totalOrders 总订单数
    function getMerchantOrders(address merchant) external view returns (uint256 totalOrders) {
        MerchantInfo memory info = merchants[merchant];
        return info.totalOrders;
    }

    // 新增查询接口，返回商家的认证状态和认证时间
    /// @notice 获取商家认证信息
    /// @param merchant 商家地址
    /// @return isVerified 是否已认证
    /// @return verifyTime 认证时间
    function getMerchantVerification(address merchant) external view returns (bool isVerified, uint256 verifyTime) {
        MerchantVerification memory verification = verifications[merchant];
        return (verification.isVerified, verification.verifyTime);
    }

    /// @notice 检查商家是否已提交认证申请
    /// @param merchant 商家地址
    /// @return 是否已提交认证申请
    function hasSubmittedVerification(address merchant) public view returns (bool) {
        // 如果 verifyTime > 0，说明已经调用过 submitVerification
        return verifications[merchant].verifyTime > 0;
    }

    /// @notice 获取商家完整状态信息
    /// @param merchant 商家地址
    /// @return isRegistered 是否已注册
    /// @return isVerified 是否已认证
    /// @return hasSubmitted 是否已提交申请
    /// @return verifyTime 认证/提交时间
    function getMerchantStatus(address merchant) external view returns (
        bool isRegistered,
        bool isVerified,
        bool hasSubmitted,
        uint256 verifyTime
    ) {
        isRegistered = merchants[merchant].createTime > 0;
        MerchantVerification memory verification = verifications[merchant];
        isVerified = verification.isVerified;
        hasSubmitted = verification.verifyTime > 0;
        verifyTime = verification.verifyTime;
    }

    /// @notice 检查地址是否为商家（兼容 ProductManagement 接口）
    /// @param merchant 商家地址
    /// @return 是否为已认证的商家
    function isMerchant(address merchant) external view returns (bool) {
        // 检查是否已注册且已认证
        return merchants[merchant].createTime > 0 && verifications[merchant].isVerified;
    }

    /// @notice 获取商家总数
    /// @return 注册商家总数
    function getMerchantCount() external view returns (uint256) {
        return merchantCounter;
    }

    /// @notice 获取商家地址列表
    /// @param start 起始索引
    /// @param limit 返回数量限制
    /// @return 商家地址数组
    function getMerchantAddresses(uint256 start, uint256 limit) external view returns (address[] memory) {
        require(start < merchantAddresses.length, "Start index out of bounds");

        uint256 end = start + limit;
        if (end > merchantAddresses.length) {
            end = merchantAddresses.length;
        }

        address[] memory result = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            result[i - start] = merchantAddresses[i];
        }

        return result;
    }

    /// @notice 获取所有商家地址
    /// @return 所有商家地址数组
    function getAllMerchantAddresses() external view returns (address[] memory) {
        return merchantAddresses;
    }

    // —— 存储修复 —— //
    /// @notice 修复存储布局问题（仅限管理员）
    function fixStorageLayout() external onlyOwner {
        // 重置异常的merchantCounter
        if (merchantCounter == 0 || merchantCounter > 1000000) {
            merchantCounter = 0;
        }

        // 清空并重建merchantAddresses数组
        delete merchantAddresses;

        // 重新统计有效商家
        // 注意：这里我们无法直接遍历mapping，所以只能重置计数器
        // 实际的地址会在新商家注册时重新添加

        emit StorageLayoutFixed(merchantCounter, merchantAddresses.length);
    }

    /// @notice 存储布局修复事件
    event StorageLayoutFixed(uint256 newCounter, uint256 arrayLength);

    // —— 存储验证实现 —— //

    /**
     * @dev 验证存储布局完整性
     * @return bool 存储布局是否有效
     */
    function validateStorageLayout() public view override returns (bool) {
        // 验证merchantCounter范围合理性
        if (merchantCounter > 1000000) {
            return false;
        }

        // 验证数组长度合理性
        if (merchantAddresses.length > merchantCounter + 100) {
            return false;
        }

        // 验证映射一致性：活跃商家数量不应超过总计数
        uint256 activeCount = 0;
        uint256 maxCheck = merchantAddresses.length > 1000 ? 1000 : merchantAddresses.length;

        for (uint256 i = 0; i < maxCheck; i++) {
            if (merchants[merchantAddresses[i]].isActive) {
                activeCount++;
            }
        }

        if (activeCount > merchantCounter) {
            return false;
        }

        return true;
    }

    /**
     * @dev 计算存储状态校验和
     * @return bytes32 存储状态的校验和
     */
    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            merchantCounter,
            merchantAddresses.length,
            STORAGE_LAYOUT_VERSION,
            address(this),
            block.chainid
        ));
    }

    /**
     * @dev 紧急修复存储问题
     */
    function emergencyStorageFix() external override onlyOwner whenPaused {
        // 修复merchantCounter
        if (merchantCounter == 0 || merchantCounter > 1000000) {
            uint256 actualCount = 0;
            for (uint256 i = 0; i < merchantAddresses.length; i++) {
                if (merchants[merchantAddresses[i]].isActive) {
                    actualCount++;
                }
            }
            merchantCounter = actualCount;
        }

        // 清理无效地址
        for (uint256 i = merchantAddresses.length; i > 0; i--) {
            address addr = merchantAddresses[i - 1];
            if (!merchants[addr].isActive) {
                merchantAddresses[i - 1] = merchantAddresses[merchantAddresses.length - 1];
                merchantAddresses.pop();
            }
        }

        emit StorageFixed(address(this), "MerchantManagement storage fixed");
        emit StorageLayoutFixed(merchantCounter, merchantAddresses.length);
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    // —— 升级控制 —— //
    function _authorizeUpgrade(address newImplementation) internal override {
        // 使用增强的存储验证升级授权
        _authorizeUpgradeWithValidation(newImplementation);
    }

    function pause() external onlyOwner { // 修改:将 onlyOwnerUpgradeable 改为 onlyOwner
        _pause();
    }

    function unpause() external onlyOwner { // 修改:将 onlyOwnerUpgradeable 改为 onlyOwner
        _unpause();
    }
}
