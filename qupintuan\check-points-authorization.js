// 检查 PointsManagement 是否授权 ProductManagement
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const POINTS_MANAGEMENT = "0xC3486bf571914BC5c83f89c69fA23fD3C75Aa5f4";
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";
const USER_ADDRESS = "0xA2dD965E6AAE7Bea5cd24CfC05653B8c72c2F9EF";

// PointsManagement ABI
const POINTS_MANAGEMENT_ABI = [
  {
    "inputs": [{"internalType": "address", "name": "", "type": "address"}],
    "name": "authorizedContracts",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "user", "type": "address"}],
    "name": "groupBuyPointsNonExchangeable",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
];

async function checkPointsAuthorization() {
  console.log("🔍 检查 PointsManagement 授权状态...");
  console.log("📍 PointsManagement:", POINTS_MANAGEMENT);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT);
  console.log("👤 用户地址:", USER_ADDRESS);

  try {
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 检查 ProductManagement 是否被 PointsManagement 授权
    console.log("\n🔍 步骤1：检查 ProductManagement 授权状态...");
    const isAuthorized = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'authorizedContracts',
      args: [PRODUCT_MANAGEMENT]
    });

    console.log("ProductManagement 授权状态:", isAuthorized ? "✅ 已授权" : "❌ 未授权");

    if (!isAuthorized) {
      console.log("\n❌ 发现问题！ProductManagement 未被 PointsManagement 授权");
      console.log("💡 这就是购买失败的根本原因！");
      console.log("🔧 解决方案：需要授权 ProductManagement 访问 PointsManagement");
      return;
    }

    // 2. 检查用户积分余额
    console.log("\n🔍 步骤2：检查用户积分余额...");
    const userPoints = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'groupBuyPointsNonExchangeable',
      args: [USER_ADDRESS]
    });

    console.log("用户积分余额:", userPoints.toString());
    
    // 商品价格是 1880000（6位精度，实际是1.88积分）
    const productPrice = 1880000n;
    const hasEnoughPoints = userPoints >= productPrice;
    
    console.log("商品价格:", productPrice.toString(), "(1.88 积分)");
    console.log("积分是否充足:", hasEnoughPoints ? "✅ 是" : "❌ 否");

    if (!hasEnoughPoints) {
      console.log("\n❌ 发现问题！用户积分不足");
      console.log("💡 这可能是购买失败的原因之一");
      return;
    }

    // 3. 总结
    console.log("\n📊 PointsManagement 检查总结:");
    console.log("1. ProductManagement 授权:", isAuthorized ? "✅" : "❌");
    console.log("2. 用户积分充足:", hasEnoughPoints ? "✅" : "❌");

    if (isAuthorized && hasEnoughPoints) {
      console.log("\n🤔 PointsManagement 相关检查都通过了...");
      console.log("💡 问题可能在其他地方，需要进一步调试");
    }

  } catch (error) {
    console.error("❌ 检查过程失败:", error.message);
  }
}

// 运行检查
checkPointsAuthorization().catch(console.error);
