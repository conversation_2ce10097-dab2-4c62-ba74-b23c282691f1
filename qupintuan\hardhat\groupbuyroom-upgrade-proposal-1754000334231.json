{"timestamp": "2025-07-31T22:18:54.231Z", "stage": "4-propose-upgrade", "proposalTxId": "1", "transactionHash": "0x23079e94d8170a899dec2ecbcba42c849cdad20a6dea35773441ecf9017812b7", "proxyAddress": "0xfC0b4aD3c5eb6AFA4Ee13d16492fc0D75eff272F", "newImplementationAddress": "0xA5522a3617E0238a72D93451fe2E612cEB679963", "timelockAddress": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "multiSigAddress": "0x85D2a947B0dA2c73a4342A2656a142B16CD71CFb", "salt": "0x85d017538131f3f883c4b8f185fb21fdcc0cd9bb9c73fa22176a977f7f250411", "delay": 600, "upgradeCalldata": "0x4f1ef286000000000000000000000000a5522a3617e0238a72d93451fe2e612ceb67996300000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000000", "scheduleCalldata": "0x01d5062a000000000000000000000000fc0b4ad3c5eb6afa4ee13d16492fc0d75eff272f000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000085d017538131f3f883c4b8f185fb21fdcc0cd9bb9c73fa22176a977f7f250411000000000000000000000000000000000000000000000000000000000000025800000000000000000000000000000000000000000000000000000000000000644f1ef286000000000000000000000000a5522a3617e0238a72d93451fe2e612ceb6799630000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "proposer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "status": "SUBMITTED", "description": "升级 GroupBuyRoom 合约以修复重复支付问题", "changes": ["使用 else if 结构避免重复支付", "添加 winnerIndex 有效性验证", "优化身份判断逻辑"]}