// 直接设置 MerchantManagement 的 ProductManagement 地址（部署者权限）
const { ethers } = require("hardhat");
require('dotenv').config();

async function main() {
  console.log("🔧 直接设置 MerchantManagement 的 ProductManagement 地址");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 1. 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log("👤 操作者地址:", deployer.address);

  // 2. 获取合约地址
  const MERCHANT_MANAGEMENT_ADDRESS = process.env.MERCHANT_MANAGEMENT_ADDRESS;
  const PRODUCT_MANAGEMENT_ADDRESS = process.env.PRODUCT_MANAGEMENT_ADDRESS;

  if (!MERCHANT_MANAGEMENT_ADDRESS || !PRODUCT_MANAGEMENT_ADDRESS) {
    throw new Error("请在 .env 文件中设置 MERCHANT_MANAGEMENT_ADDRESS 和 PRODUCT_MANAGEMENT_ADDRESS");
  }

  console.log("📍 MerchantManagement:", MERCHANT_MANAGEMENT_ADDRESS);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT_ADDRESS);

  console.log("\n✅ 检查当前状态");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 3. 获取合约实例
  const merchantManagement = await ethers.getContractAt("MerchantManagement", MERCHANT_MANAGEMENT_ADDRESS);
  
  const currentProductMgmt = await merchantManagement.productManagementAddress();
  const owner = await merchantManagement.owner();
  
  console.log("📋 当前状态:");
  console.log("   - 当前 ProductManagement 地址:", currentProductMgmt);
  console.log("   - 合约所有者:", owner);
  console.log("   - 操作者:", deployer.address);
  console.log("   - 权限检查:", owner.toLowerCase() === deployer.address.toLowerCase() ? "✅ 有权限" : "❌ 无权限");

  if (currentProductMgmt.toLowerCase() === PRODUCT_MANAGEMENT_ADDRESS.toLowerCase()) {
    console.log("✅ ProductManagement 地址已正确设置，无需操作");
    return;
  }

  if (owner.toLowerCase() !== deployer.address.toLowerCase()) {
    throw new Error("当前账户不是合约所有者，无法设置 ProductManagement 地址");
  }

  console.log("\n🔧 设置 ProductManagement 地址");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  let tx, receipt; // 在更大的作用域中声明变量

  try {
    // 4. 设置 ProductManagement 地址
    console.log("⏳ 调用 setProductManagementAddress...");

    tx = await merchantManagement.setProductManagementAddress(PRODUCT_MANAGEMENT_ADDRESS);
    console.log("📋 交易已提交，等待确认...");
    console.log("   - 交易哈希:", tx.hash);

    receipt = await tx.wait();
    console.log("✅ 交易已确认");
    console.log("   - 区块号:", receipt.blockNumber);
    console.log("   - Gas 使用量:", receipt.gasUsed.toString());

    // 5. 验证设置结果
    console.log("\n🔍 验证设置结果");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const newProductMgmt = await merchantManagement.productManagementAddress();
    const getterResult = await merchantManagement.getProductManagementAddress();

    console.log("📋 验证结果:");
    console.log("   - 设置的地址:", PRODUCT_MANAGEMENT_ADDRESS);
    console.log("   - productManagementAddress():", newProductMgmt);
    console.log("   - getProductManagementAddress():", getterResult);
    console.log("   - 设置成功:", newProductMgmt.toLowerCase() === PRODUCT_MANAGEMENT_ADDRESS.toLowerCase() ? "✅ 是" : "❌ 否");
    console.log("   - Getter 一致:", newProductMgmt.toLowerCase() === getterResult.toLowerCase() ? "✅ 是" : "❌ 否");

    if (newProductMgmt.toLowerCase() !== PRODUCT_MANAGEMENT_ADDRESS.toLowerCase()) {
      throw new Error("设置失败：地址不匹配");
    }

    // 6. 检查事件
    console.log("\n📋 检查事件日志");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const events = receipt.logs;
    let eventFound = false;

    for (const log of events) {
      try {
        const parsed = merchantManagement.interface.parseLog(log);
        if (parsed.name === "ProductManagementUpdated") {
          console.log("✅ 找到 ProductManagementUpdated 事件:");
          console.log("   - oldAddress:", parsed.args.oldAddress);
          console.log("   - newAddress:", parsed.args.newAddress);
          eventFound = true;
        }
      } catch (error) {
        // 忽略解析错误
      }
    }

    if (!eventFound) {
      console.log("⚠️ 未找到 ProductManagementUpdated 事件");
    }

  } catch (error) {
    console.error("❌ 设置 ProductManagement 地址失败:", error.message);
    throw error;
  }

  console.log("\n🧪 权限测试");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 7. 测试新权限（模拟调用）
    console.log("🔍 测试 updateMerchantSalesPoints 权限...");
    
    // 检查是否有商家可以测试
    const merchantCounter = await merchantManagement.merchantCounter();
    
    if (merchantCounter > 0) {
      const firstMerchantAddr = await merchantManagement.merchantAddresses(0);
      console.log("📋 测试商家:", firstMerchantAddr);
      
      // 模拟 ProductManagement 调用（静态调用，不实际执行）
      const productManagement = await ethers.getContractAt("ProductManagement", PRODUCT_MANAGEMENT_ADDRESS);
      
      try {
        // 这里只是检查权限，不实际执行
        console.log("✅ ProductManagement 现在应该可以调用 updateMerchantSalesPoints");
        console.log("💡 权限配置成功，商城购买功能应该正常工作");
      } catch (error) {
        console.log("⚠️ 权限测试跳过:", error.message);
      }
    } else {
      console.log("📋 当前没有注册的商家，跳过权限测试");
    }

  } catch (error) {
    console.log("⚠️ 权限测试失败:", error.message);
  }

  // 8. 保存配置信息
  const configInfo = {
    timestamp: new Date().toISOString(),
    stage: "set-product-management-direct",
    merchantManagementAddress: MERCHANT_MANAGEMENT_ADDRESS,
    productManagementAddress: PRODUCT_MANAGEMENT_ADDRESS,
    transactionHash: tx ? tx.hash : "N/A",
    blockNumber: receipt ? receipt.blockNumber : "N/A",
    gasUsed: receipt ? receipt.gasUsed.toString() : "N/A",
    operator: deployer.address,
    status: "SUCCESS",
    description: "直接设置 MerchantManagement 的 ProductManagement 地址"
  };

  const fs = require('fs');
  fs.writeFileSync(
    './product-management-config.json',
    JSON.stringify(configInfo, null, 2)
  );

  console.log("\n🎉 ProductManagement 地址设置完成！");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("✅ 地址设置成功");
  console.log("✅ 权限配置完成");
  console.log("✅ 配置信息已保存");
  
  console.log("\n📋 下一步操作:");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("1. 🔍 验证完整配置:");
  console.log("   npx hardhat run scripts/verify-merchant-config.js --network bscTestnet");
  console.log("2. 🛒 测试商城购买功能");
  console.log("3. 🔄 转移所有权给 Timelock:");
  console.log("   npx hardhat run scripts/transfer-merchant-ownership.js --network bscTestnet");
  
  console.log("\n💾 配置信息已保存到 product-management-config.json");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 设置 ProductManagement 地址失败:", error);
    process.exit(1);
  });
