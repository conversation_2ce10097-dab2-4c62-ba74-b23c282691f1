// scripts/add-user-points.js
// 为用户充值积分以解决购买问题

require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  try {
    console.log("💰 为用户充值积分");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    // 1. 获取签名者
    const [signer] = await ethers.getSigners();
    console.log("📝 操作者地址:", signer.address);

    // 2. 合约地址
    const productMgmtAddr = "******************************************";

    // 3. 连接到合约
    const productMgmt = await ethers.getContractAt("ProductManagement", productMgmtAddr);

    // 4. 获取 PointsManagement 地址
    const pointsMgmtAddr = await productMgmt.pointsManagement();
    console.log("📋 PointsManagement 地址:", pointsMgmtAddr);

    const pointsMgmt = await ethers.getContractAt("PointsManagement", pointsMgmtAddr);

    // 5. 获取积分精度
    const pointsDecimals = await pointsMgmt.POINTS_DECIMALS();
    console.log("📋 积分精度:", pointsDecimals.toString(), "位小数");

    // 6. 目标用户和充值金额（使用正确的精度）
    const targetUser = "******************************************";
    const rechargeAmount = BigInt(100) * BigInt(10 ** Number(pointsDecimals)); // 100积分，足够购买商品

    console.log("\n🧪 充值信息:");
    console.log("   • 目标用户:", targetUser);
    console.log("   • 充值金额:", Number(rechargeAmount) / Math.pow(10, Number(pointsDecimals)), "积分");
    console.log("   • 原始数值:", rechargeAmount.toString());

    // 7. 检查当前积分
    console.log("\n📋 步骤1: 检查当前积分...");
    const currentPoints = await pointsMgmt.groupBuyPointsNonExchangeable(targetUser);
    const actualCurrentPoints = Number(currentPoints) / Math.pow(10, Number(pointsDecimals));
    console.log("✅ 用户当前积分:", actualCurrentPoints, "积分");
    console.log("✅ 原始数值:", currentPoints.toString());

    // 8. 执行充值
    console.log("\n📋 步骤2: 执行充值...");
    try {
      const tx = await pointsMgmt.connect(signer).addGroupBuyPointsNonExchangeable(
        targetUser,
        rechargeAmount
      );
      
      console.log("📤 充值交易已发送:", tx.hash);
      console.log("⏳ 等待交易确认...");
      
      await tx.wait();
      console.log("✅ 充值交易已确认");

    } catch (error) {
      console.log("❌ 充值失败:", error.message);
      
      if (error.message.includes("AccessControl")) {
        console.log("\n💡 权限问题:");
        console.log("   • 当前账户可能没有充值权限");
        console.log("   • 需要使用有权限的账户");
        
        // 检查权限
        try {
          const hasRole = await pointsMgmt.hasRole(
            await pointsMgmt.POINTS_MANAGER_ROLE(),
            signer.address
          );
          console.log("   • 当前账户是否有 POINTS_MANAGER_ROLE:", hasRole);
        } catch (roleError) {
          console.log("   • 无法检查权限:", roleError.message);
        }
      }
      return;
    }

    // 9. 验证充值结果
    console.log("\n📋 步骤3: 验证充值结果...");
    const newPoints = await pointsMgmt.groupBuyPointsNonExchangeable(targetUser);
    const actualNewPoints = Number(newPoints) / Math.pow(10, Number(pointsDecimals));
    const addedPoints = Number(newPoints - currentPoints) / Math.pow(10, Number(pointsDecimals));

    console.log("✅ 用户新积分:", actualNewPoints, "积分");
    console.log("✅ 增加积分:", addedPoints, "积分");
    console.log("✅ 新积分原始值:", newPoints.toString());

    // 10. 检查是否足够购买商品
    const productInfo = await productMgmt.products(1);
    const productPrice = productInfo[4];
    const actualProductPrice = Number(productPrice) / Math.pow(10, Number(pointsDecimals));

    console.log("\n📋 步骤4: 检查购买能力...");
    console.log("✅ 产品价格:", actualProductPrice, "积分");
    console.log("✅ 用户积分:", actualNewPoints, "积分");
    console.log("✅ 是否足够:", actualNewPoints >= actualProductPrice ? "✅ 足够" : "❌ 不足");

    if (actualNewPoints >= actualProductPrice) {
      console.log("\n🎉 充值成功！");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("✅ 用户现在有足够积分购买商品");
      console.log("\n🧪 建议测试:");
      console.log("   1. 刷新前端页面");
      console.log("   2. 重新尝试购买商品");
      console.log("   3. 检查是否还有其他错误");
    } else {
      console.log("\n⚠️ 积分仍然不足");
      console.log("💡 可能需要充值更多积分");
    }

    console.log("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  } catch (error) {
    console.error("❌ 充值过程中发生错误:", error);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
