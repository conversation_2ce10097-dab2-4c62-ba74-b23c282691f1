// scripts/authorize-ProductManagement.js
// 授权 ProductManagement 合约访问 AddressManagement

require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("🔐 授权 ProductManagement 合约访问 AddressManagement");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取签名者
    const [signer] = await ethers.getSigners();
    console.log("📝 操作者地址:", signer.address);

    // 2. 从环境变量获取合约地址
    const addressMgmtAddr = process.env.ADDRESS_MANAGEMENT_ADDRESS;
    const productMgmtAddr = process.env.PRODUCT_MANAGEMENT_ADDRESS;

    console.log("\n📋 合约地址:");
    console.log("   • AddressManagement:", addressMgmtAddr);
    console.log("   • ProductManagement:", productMgmtAddr);

    if (!addressMgmtAddr) {
      throw new Error("请在 .env 文件中设置 ADDRESS_MANAGEMENT_ADDRESS");
    }
    if (!productMgmtAddr) {
      throw new Error("请在 .env 文件中设置 PRODUCT_MANAGEMENT_ADDRESS");
    }

    if (!addressMgmtAddr || addressMgmtAddr === "******************************************") {
      throw new Error("AddressManagement 合约地址无效");
    }

    if (!productMgmtAddr || productMgmtAddr === "******************************************") {
      throw new Error("ProductManagement 合约地址无效");
    }

    // 3. 连接到 AddressManagement 合约
    console.log("\n🔗 连接到 AddressManagement 合约...");
    const addressMgmt = await ethers.getContractAt("AddressManagement", addressMgmtAddr);

    // 4. 检查当前所有者
    const currentOwner = await addressMgmt.owner();
    console.log("\n🔍 权限检查:");
    console.log("   • AddressManagement 当前所有者:", currentOwner);
    console.log("   • 当前操作者:", signer.address);
    console.log("   • 权限匹配:", currentOwner === signer.address ? "✅" : "❌");

    if (currentOwner !== signer.address) {
      console.log("❌ 错误: 当前操作者不是合约所有者");
      console.log("💡 提示: 请使用合约所有者账户进行授权");
      return;
    }

    // 5. 检查当前授权状态
    console.log("\n🔍 检查当前授权状态:");
    const isCurrentlyAuthorized = await addressMgmt.isAuthorizedContract(productMgmtAddr);
    console.log("   • ProductManagement 当前授权状态:", isCurrentlyAuthorized ? "✅ 已授权" : "❌ 未授权");

    // 6. 检查 authorizedContracts 映射（直接访问）
    console.log("\n🔍 检查 authorizedContracts 映射:");
    try {
      const directAuthStatus = await addressMgmt.authorizedContracts(productMgmtAddr);
      console.log("   • 直接映射查询结果:", directAuthStatus ? "✅ 已授权" : "❌ 未授权");
    } catch (error) {
      console.log("   • 直接映射查询失败:", error.message);
    }

    // 7. 授权 ProductManagement 合约
    if (!isCurrentlyAuthorized) {
      console.log("\n⚡ 授权 ProductManagement 合约...");
      const tx = await addressMgmt.setAuthorizedContract(productMgmtAddr, true);
      console.log("   📤 交易已发送:", tx.hash);
      const receipt = await tx.wait();
      console.log("   ✅ ProductManagement 合约授权成功，区块号:", receipt.blockNumber);

      // 检查交易事件
      console.log("   🔍 检查交易事件:");
      const events = receipt.logs.filter(log => {
        try {
          const parsed = addressMgmt.interface.parseLog(log);
          return parsed.name === 'ContractAuthorized';
        } catch {
          return false;
        }
      });

      if (events.length > 0) {
        const event = addressMgmt.interface.parseLog(events[0]);
        console.log("   📝 ContractAuthorized 事件:", {
          contractAddr: event.args.contractAddr,
          authorized: event.args.authorized
        });
      }
    } else {
      console.log("\n✅ ProductManagement 合约已经被授权");
    }

    // 8. 验证授权结果
    console.log("\n🔍 验证授权结果:");
    const finalAuthStatus = await addressMgmt.isAuthorizedContract(productMgmtAddr);
    console.log("   • ProductManagement 最终授权状态:", finalAuthStatus ? "✅ 已授权" : "❌ 未授权");

    // 9. 再次检查直接映射
    const finalDirectAuthStatus = await addressMgmt.authorizedContracts(productMgmtAddr);
    console.log("   • 直接映射最终状态:", finalDirectAuthStatus ? "✅ 已授权" : "❌ 未授权");

    if (finalAuthStatus && finalDirectAuthStatus) {
      console.log("\n🎉 授权配置完成！");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("✅ ProductManagement 现在可以访问用户地址信息");
      console.log("✅ 商城购买功能应该可以正常使用了");
      
      console.log("\n🧪 建议测试:");
      console.log("   1. 尝试购买商城商品");
      console.log("   2. 检查是否还有 'Unauthorized' 错误");
      console.log("   3. 验证地址信息获取正常");
    } else {
      console.log("\n❌ 授权配置失败，请检查错误信息");
    }

    // 8. 显示其他授权的合约（如果有）
    console.log("\n📊 授权状态总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("   • ProductManagement:", finalAuthStatus ? "✅ 已授权" : "❌ 未授权");
    
    // 可以在这里添加其他需要检查的合约
    // 例如：MerchantManagement, PointsManagement 等

  } catch (error) {
    console.error("❌ 授权过程中发生错误:", error);
    
    if (error.message.includes("Only owner")) {
      console.log("\n💡 解决建议:");
      console.log("   • 确保使用合约所有者账户执行此脚本");
      console.log("   • 检查 hardhat.config.js 中的账户配置");
    } else if (error.message.includes("Address must be a contract")) {
      console.log("\n💡 解决建议:");
      console.log("   • 检查 ProductManagement 合约地址是否正确");
      console.log("   • 确认合约已正确部署");
    } else if (error.message.includes("execution reverted")) {
      console.log("\n💡 解决建议:");
      console.log("   • 检查合约地址是否正确");
      console.log("   • 确认网络连接正常");
      console.log("   • 验证合约版本是否支持授权功能");
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
