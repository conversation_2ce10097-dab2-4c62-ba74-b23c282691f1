// scripts/transfer-QPTBuyback-ownership.js
// 转移 QPTBuyback 合约所有权给 Timelock
require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 转移 QPTBuyback 所有权给 Timelock");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取当前账户
    const [signer] = await ethers.getSigners();
    console.log("📝 操作者地址:", signer.address);
    console.log("💰 操作者余额:", ethers.formatEther(await ethers.provider.getBalance(signer.address)), "BNB");

    // 2. 读取合约地址
    const qptBuybackAddr = process.env.QPT_BUYBACK_ADDRESS || "******************************************";
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS;

    console.log("\n📋 合约地址:");
    console.log("   • QPTBuyback:", qptBuybackAddr);
    console.log("   • Timelock:", timelockAddr);

    // 3. 验证地址
    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS");
    }

    // 4. 连接到合约
    const QPTBuyback = await ethers.getContractFactory("QPTBuyback");
    const qptBuyback = QPTBuyback.attach(qptBuybackAddr);

    // 5. 检查当前状态
    console.log("\n🔍 检查当前状态:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const currentOwner = await qptBuyback.owner();
    console.log("   • 当前所有者:", currentOwner);
    console.log("   • 目标所有者:", timelockAddr);
    console.log("   • 操作者:", signer.address);

    // 6. 验证权限
    if (currentOwner.toLowerCase() !== signer.address.toLowerCase()) {
      throw new Error(`当前账户不是合约所有者。当前所有者: ${currentOwner}`);
    }

    if (currentOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("\n✅ 所有权已经是 Timelock，无需转移");
      
      // 更新 .env 文件
      console.log("\n📝 更新 .env 文件:");
      console.log(`QPT_BUYBACK_ADDRESS=${qptBuybackAddr}`);
      return;
    }

    // 7. 执行所有权转移
    console.log("\n🔄 执行所有权转移:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    console.log("   ⏳ 正在转移所有权...");
    const transferTx = await qptBuyback.transferOwnership(timelockAddr);
    
    console.log("   📤 交易已提交:", transferTx.hash);
    console.log("   ⏳ 等待交易确认...");
    
    const receipt = await transferTx.wait();
    console.log("   ✅ 交易已确认，区块:", receipt.blockNumber);

    // 8. 验证转移结果
    console.log("\n🔍 验证转移结果:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const newOwner = await qptBuyback.owner();
    console.log("   • 新所有者:", newOwner);
    
    if (newOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("   ✅ 所有权转移成功!");
    } else {
      console.log("   ❌ 所有权转移失败");
      throw new Error("所有权转移验证失败");
    }

    // 9. 保存部署信息
    const deploymentInfo = {
      proxyAddress: qptBuybackAddr,
      owner: timelockAddr,
      deployer: signer.address,
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      status: "OWNERSHIP_TRANSFERRED",
      transactionHash: transferTx.hash,
      blockNumber: receipt.blockNumber
    };

    const fs = require('fs');
    fs.writeFileSync(
      './qpt-buyback-ownership-transfer.json',
      JSON.stringify(deploymentInfo, null, 2)
    );

    // 10. 总结
    console.log("\n🎉 QPTBuyback 所有权转移完成!");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("✅ QPTBuyback 现在由 Timelock 控制");
    console.log("✅ 所有后续操作需要通过多签+Timelock流程");
    console.log("🔒 合约安全性已提升到最高级别");

    console.log("\n📝 更新 .env 文件:");
    console.log(`QPT_BUYBACK_ADDRESS=${qptBuybackAddr}`);

    console.log("\n📋 合约信息总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("   • 代理地址:", qptBuybackAddr);
    console.log("   • 所有者:", timelockAddr);
    console.log("   • 状态: 完全部署并配置");
    console.log("   • 安全级别: 最高");

  } catch (error) {
    console.error("❌ 所有权转移失败:", error);

    if (error.code === 'ECONNRESET' || error.code === 'NETWORK_ERROR') {
      console.log("\n💡 网络连接问题解决建议:");
      console.log("   • 检查网络连接");
      console.log("   • 稍后重试");
      console.log("   • 使用不同的RPC端点");
    } else if (error.message.includes("Ownable: caller is not the owner")) {
      console.log("💡 提示: 当前账户不是合约所有者");
    } else if (error.message.includes("Ownable: new owner is the zero address")) {
      console.log("💡 提示: 目标地址不能是零地址");
    }

    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 脚本执行失败:", error);
    process.exit(1);
  });
