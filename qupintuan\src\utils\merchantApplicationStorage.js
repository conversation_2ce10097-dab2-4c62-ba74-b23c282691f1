// 商家申请信息存储管理
class MerchantApplicationStorage {
  constructor() {
    this.storageKey = 'merchant_applications';
  }

  /**
   * 保存商家申请信息
   * @param {string} address - 商家地址
   * @param {Object} applicationData - 申请数据
   */
  saveApplication(address, applicationData) {
    if (!address || !applicationData) {
      console.warn('⚠️ [MerchantApplicationStorage] 保存失败: 地址或数据为空');
      return false;
    }

    try {
      const normalizedAddress = address.toLowerCase();
      const applications = this.getAllApplications();
      
      // 添加时间戳和状态
      const applicationWithMeta = {
        ...applicationData,
        merchantAddress: normalizedAddress, // 使用 merchantAddress 而不是 address，避免覆盖经营地址
        submitTime: new Date().toISOString(),
        status: 'pending',
        lastUpdate: new Date().toISOString()
      };

      applications[normalizedAddress] = applicationWithMeta;
      localStorage.setItem(this.storageKey, JSON.stringify(applications));
      
      // 申请信息已保存
      return true;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 保存失败:', error);
      return false;
    }
  }

  /**
   * 获取商家申请信息
   * @param {string} address - 商家地址
   * @returns {Object|null} 申请信息
   */
  getApplication(address) {
    if (!address) return null;

    try {
      const normalizedAddress = address.toLowerCase();
      const applications = this.getAllApplications();
      return applications[normalizedAddress] || null;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 获取失败:', error);
      return null;
    }
  }

  /**
   * 获取所有申请信息
   * @returns {Object} 所有申请信息
   */
  getAllApplications() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 获取所有申请失败:', error);
      return {};
    }
  }

  /**
   * 更新申请状态
   * @param {string} address - 商家地址
   * @param {string} status - 新状态 (pending, approved, rejected)
   */
  updateStatus(address, status) {
    if (!address || !status) return false;

    try {
      const normalizedAddress = address.toLowerCase();
      const applications = this.getAllApplications();
      
      if (applications[normalizedAddress]) {
        applications[normalizedAddress].status = status;
        applications[normalizedAddress].lastUpdate = new Date().toISOString();
        localStorage.setItem(this.storageKey, JSON.stringify(applications));
        
        console.log(`✅ [MerchantApplicationStorage] 状态已更新: ${normalizedAddress} -> ${status}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 状态更新失败:', error);
      return false;
    }
  }

  /**
   * 删除申请信息
   * @param {string} address - 商家地址
   */
  removeApplication(address) {
    if (!address) return false;

    try {
      const normalizedAddress = address.toLowerCase();
      const applications = this.getAllApplications();
      
      if (applications[normalizedAddress]) {
        delete applications[normalizedAddress];
        localStorage.setItem(this.storageKey, JSON.stringify(applications));
        
        console.log('✅ [MerchantApplicationStorage] 申请信息已删除:', normalizedAddress);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 删除失败:', error);
      return false;
    }
  }

  /**
   * 获取待审核申请列表
   * @returns {Array} 待审核申请列表
   */
  getPendingApplications() {
    try {
      const applications = this.getAllApplications();
      return Object.values(applications).filter(app => app.status === 'pending');
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 获取待审核列表失败:', error);
      return [];
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    try {
      const applications = this.getAllApplications();
      const allApps = Object.values(applications);
      
      return {
        total: allApps.length,
        pending: allApps.filter(app => app.status === 'pending').length,
        approved: allApps.filter(app => app.status === 'approved').length,
        rejected: allApps.filter(app => app.status === 'rejected').length
      };
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 获取统计失败:', error);
      return { total: 0, pending: 0, approved: 0, rejected: 0 };
    }
  }

  /**
   * 清理过期的申请信息
   * @param {string} address - 商家地址
   * @param {number} merchantCreateTime - 商家在合约中的创建时间（秒级时间戳）
   * @returns {boolean} 是否清理成功
   */
  cleanExpiredApplication(address, merchantCreateTime) {
    if (!address || !merchantCreateTime) return false;

    try {
      const normalizedAddress = address.toLowerCase();
      const applications = this.getAllApplications();
      const application = applications[normalizedAddress];

      if (application) {
        // 检查申请信息的提交时间是否在商家创建时间之前
        const applicationSubmitTime = application.submitTime ? new Date(application.submitTime).getTime() / 1000 : 0;

        if (applicationSubmitTime < merchantCreateTime) {
          // 申请信息过期，删除它
          delete applications[normalizedAddress];
          localStorage.setItem(this.storageKey, JSON.stringify(applications));

          console.log(`✅ [MerchantApplicationStorage] 已清理过期申请信息: ${normalizedAddress}`);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 清理过期申请失败:', error);
      return false;
    }
  }

  /**
   * 清空所有申请信息
   */
  clear() {
    try {
      localStorage.removeItem(this.storageKey);
      console.log('✅ [MerchantApplicationStorage] 所有申请信息已清空');
      return true;
    } catch (error) {
      console.error('❌ [MerchantApplicationStorage] 清空失败:', error);
      return false;
    }
  }
}

// 创建单例实例
export const merchantApplicationStorage = new MerchantApplicationStorage();

export default merchantApplicationStorage;
