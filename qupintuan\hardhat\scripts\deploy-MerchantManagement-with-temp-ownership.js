// 部署 MerchantManagement 合约（暂时保留部署者所有权）
const { ethers, upgrades } = require("hardhat");
require('dotenv').config();

async function main() {
  console.log("🚀 部署 MerchantManagement 合约（暂时保留部署者所有权）");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 1. 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log("👤 部署者地址:", deployer.address);

  // 2. 获取合约地址
  const pointsManagementAddr = process.env.POINTS_MANAGEMENT_ADDRESS;
  const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;
  const systemAdminAddr = process.env.SYSTEM_ADMIN_ADDRESS;

  console.log("\n📍 合约地址:");
  console.log("   PointsManagement:", pointsManagementAddr);
  console.log("   Timelock:", timelockAddr);
  console.log("   SystemAdmin:", systemAdminAddr);

  // 验证地址
  if (!pointsManagementAddr) {
    throw new Error("请在 .env 文件中设置 POINTS_MANAGEMENT_ADDRESS");
  }
  if (!timelockAddr) {
    throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
  }
  if (!systemAdminAddr) {
    throw new Error("请在 .env 文件中设置 SYSTEM_ADMIN_ADDRESS");
  }

  console.log("\n⏳ 编译合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 3. 获取合约工厂
  const MerchantManagement = await ethers.getContractFactory("MerchantManagement");
  console.log("✅ 合约编译完成");

  console.log("\n🚀 部署代理合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 4. 部署可升级合约
  console.log("⏳ 部署 MerchantManagement 代理合约...");
  const merchantManagement = await upgrades.deployProxy(
    MerchantManagement,
    [pointsManagementAddr, timelockAddr, systemAdminAddr], // 传递正确的参数：PointsManagement地址、Timelock地址和系统管理员地址
    {
      initializer: "initialize",
      kind: "uups"
    }
  );

  // 5. 等待部署完成
  await merchantManagement.waitForDeployment();
  const proxyAddress = await merchantManagement.getAddress();

  console.log("✅ MerchantManagement 部署成功!");
  console.log("📍 代理地址:", proxyAddress);

  // 6. 获取实现地址
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
  console.log("🔗 实现地址:", implementationAddress);

  console.log("\n🔍 验证部署结果");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 7. 验证部署结果
  const owner = await merchantManagement.owner();
  const pointsManagement = await merchantManagement.pointsManagement();
  const timelock = await merchantManagement.timelock();
  const systemAdmin = await merchantManagement.systemAdmin();

  console.log("👤 合约所有者:", owner);
  console.log("🎯 PointsManagement:", pointsManagement);
  console.log("🔒 Timelock合约:", timelock);
  console.log("🛡️ 系统管理员:", systemAdmin);
  console.log("✅ PointsManagement配置:", pointsManagement === pointsManagementAddr ? "正确" : "错误");
  console.log("✅ Timelock配置:", timelock === timelockAddr ? "正确" : "错误");
  console.log("✅ SystemAdmin配置:", systemAdmin === systemAdminAddr ? "正确" : "错误");

  console.log("\n⚠️ 所有权策略");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 8. 暂时保留所有权给部署者（用于后续配置操作）
  console.log("📋 当前所有权状态:");
  console.log("   - 当前所有者:", owner);
  console.log("   - 部署者:", deployer.address);
  console.log("   - 目标所有者:", timelockAddr);
  console.log("   - 所有权匹配:", owner === deployer.address ? "✅ 部署者" : "❌ 其他");

  if (owner !== deployer.address) {
    console.log("⚠️ 警告：所有权不是部署者，可能无法进行配置操作");
  }

  console.log("\n💡 重要提醒:");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("🔸 所有权暂时保留给部署者");
  console.log("🔸 这样可以直接进行配置操作，无需通过 Timelock");
  console.log("🔸 完成所有配置后需要手动转移所有权给 Timelock");
  console.log("🔸 运行以下脚本完成所有权转移:");
  console.log("   npx hardhat run scripts/transfer-merchant-ownership.js --network bscTestnet");

  console.log("\n🔧 推荐的配置流程");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("1. 🔧 设置 ProductManagement 地址:");
  console.log("   npx hardhat run scripts/set-product-management-direct.js --network bscTestnet");
  console.log("2. 🔍 验证配置:");
  console.log("   npx hardhat run scripts/verify-merchant-config.js --network bscTestnet");
  console.log("3. 🛒 测试商城购买功能");
  console.log("4. 🔄 转移所有权给 Timelock:");
  console.log("   npx hardhat run scripts/transfer-merchant-ownership.js --network bscTestnet");

  // 9. 保存部署信息
  const deploymentInfo = {
    proxyAddress,
    implementationAddress,
    deployer: deployer.address,
    currentOwner: owner,
    targetOwner: timelockAddr,
    systemAdmin: systemAdminAddr,
    pointsManagement: pointsManagementAddr,
    timelock: timelockAddr,
    timestamp: new Date().toISOString(),
    network: "bscTestnet",
    features: ["UUPS_UPGRADEABLE", "TEMP_DEPLOYER_OWNERSHIP", "MERCHANT_MANAGEMENT", "SYSTEM_ADMIN"],
    securityLevel: "MEDIUM", // 降低安全级别，因为暂时不是 Timelock 所有
    ownershipStatus: "TEMPORARY_DEPLOYER",
    configurationRequired: [
      "SET_PRODUCT_MANAGEMENT_ADDRESS",
      "VERIFY_CONFIGURATION",
      "TRANSFER_OWNERSHIP_TO_TIMELOCK"
    ]
  };

  const fs = require('fs');
  fs.writeFileSync(
    './merchantmanagement-temp-deployment.json',
    JSON.stringify(deploymentInfo, null, 2)
  );
  console.log("\n💾 部署信息已保存到 merchantmanagement-temp-deployment.json");

  // 10. 更新环境变量文件提示
  console.log("\n📝 环境变量更新");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("请在 .env 文件中更新以下地址:");
  console.log(`MERCHANT_MANAGEMENT_ADDRESS=${proxyAddress}`);

  console.log("\n🎉 部署完成！");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("✅ 合约部署成功");
  console.log("✅ 初始化完成");
  console.log("⚠️ 所有权暂时保留给部署者");
  console.log("🔧 可以直接进行配置操作");
  console.log("🔄 配置完成后记得转移所有权");

  return {
    proxyAddress,
    implementationAddress,
    owner,
    deployer: deployer.address
  };
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error("❌ 部署失败:", error);
      process.exit(1);
    });
}

module.exports = main;
