// scripts/deploy-AddressManagement.js
// 部署可升级的 AddressManagement 合约
require("dotenv").config();
const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🔄 部署可升级的 AddressManagement 合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 1. 获取部署者
  const [deployer] = await ethers.getSigners();
  console.log("📝 部署者地址:", deployer.address);
  console.log("💰 部署者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

  // 2. 从环境变量获取 Timelock 地址
  const TIMELOCK_ADDRESS = process.env.SECURE_TIMELOCK_ADDRESS;
  if (!TIMELOCK_ADDRESS) {
    throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS");
  }
  console.log("🔒 Timelock 地址:", TIMELOCK_ADDRESS);

  try {
    // 3. 获取合约工厂
    const AddressManagement = await ethers.getContractFactory("AddressManagement");

    // 4. 部署可升级合约（传递 timelock 参数）
    console.log("⏳ 部署 AddressManagement 代理合约...");
    const addressManagement = await upgrades.deployProxy(
      AddressManagement,
      [TIMELOCK_ADDRESS], // 传递 timelock 参数
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    // 5. 等待部署完成
    await addressManagement.waitForDeployment();
    const proxyAddress = await addressManagement.getAddress();

    console.log("✅ AddressManagement 部署成功!");
    console.log("📍 代理地址:", proxyAddress);

    // 6. 获取实现地址
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);

    // 7. 验证部署结果
    console.log("\n🔍 验证部署结果:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const owner = await addressManagement.owner();
    const timelock = await addressManagement.timelock();

    console.log("👤 合约所有者:", owner);
    console.log("🔒 Timelock 地址:", timelock);
    console.log("✅ Timelock 配置:", timelock === TIMELOCK_ADDRESS ? "正确" : "错误");

    // 8. 暂时保留所有权给部署者（用于后续授权操作）
    console.log("\n⚠️ 暂时保留所有权给部署者...");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("• 当前所有者:", owner);
    console.log("• 目标所有者:", TIMELOCK_ADDRESS);
    console.log("• 状态: 等待授权操作完成后转移");

    console.log("\n💡 重要提醒:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("• 所有权暂时保留给部署者");
    console.log("• 完成所有授权操作后需要手动转移所有权");
    console.log("• 运行以下脚本完成所有权转移:");
    console.log("  npx hardhat run scripts/transfer-AddressManagement-ownership.js --network bscTestnet");

    console.log("\n📋 部署完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("✅ 可升级的 AddressManagement 部署成功!");
    console.log("📍 代理地址:", proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);
    console.log("👤 所有者:", TIMELOCK_ADDRESS);
    console.log("🔒 升级控制: 只有 Timelock 可以升级");
    console.log("");
    console.log("🆕 功能特性:");
    console.log("   • ✅ 支持 UUPS 升级模式");
    console.log("   • ✅ Timelock 控制升级");
    console.log("   • ✅ 地址管理功能");
    console.log("   • ✅ 暂停/恢复功能");
    console.log("");
    console.log("🔧 环境变量更新:");
    console.log(`ADDRESS_MANAGEMENT_ADDRESS=${proxyAddress}`);

    // 9. 保存部署信息
    const deploymentInfo = {
      proxyAddress,
      implementationAddress,
      deployer: deployer.address,
      owner: TIMELOCK_ADDRESS,
      timelock: TIMELOCK_ADDRESS,
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      features: ["UUPS_UPGRADEABLE", "TIMELOCK_PROTECTED"],
      securityLevel: "HIGH"
    };

    const fs = require('fs');
    fs.writeFileSync(
      './addressmanagement-deployment.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("💾 部署信息已保存到 addressmanagement-deployment.json");

  } catch (error) {
    console.error("❌ 部署失败:", error);

    if (error.message.includes("insufficient funds")) {
      console.log("💡 提示: 部署者账户BNB余额不足");
    } else if (error.message.includes("timelock")) {
      console.log("💡 提示: 请检查 Timelock 地址配置");
    }

    process.exit(1);
  }
}

main().catch((e) => {
  console.error(e);
  process.exitCode = 1;
});
