// src/utils/optimizedProductQuery.js
// 优化的商品查询服务 - 解决价格精度问题和RPC限制

import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import { getContractAddress } from '@/contracts/addresses';
import { ABIS } from '@/contracts';
import { formatPoints, POINTS_DECIMALS } from '@/utils/pointsFormatter';
import { fixUpgradedPrice } from '@/utils/priceFixForUpgrade';

// 创建优化的公共客户端
const publicClient = createPublicClient({
  chain: bscTestnet,
  transport: http(
    import.meta.env.VITE_RPC_URL_TESTNET || 'https://bsc-testnet.public.blastapi.io',
    {
      batch: true,
      fetchOptions: { timeout: 10000 },
      retryCount: 2,
      retryDelay: 1000,
    }
  )
});

// 缓存机制
const cache = new Map();
const CACHE_DURATION = 30000; // 30秒缓存

function getCacheKey(functionName, ...args) {
  return `${functionName}_${JSON.stringify(args)}`;
}

function getFromCache(key) {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  cache.delete(key);
  return null;
}

function setCache(key, data) {
  cache.set(key, { data, timestamp: Date.now() });
}

/**
 * 智能价格精度修复（升级版）
 * 使用新的价格修复逻辑处理升级后的合约
 */
function smartPriceFix(rawPrice) {
  return fixUpgradedPrice(rawPrice);
}

/**
 * 优化的单个商品查询
 */
export async function getOptimizedProductInfo(productId) {
  const cacheKey = getCacheKey('productInfo', productId);
  const cached = getFromCache(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    const productManagementAddress = getContractAddress(97, 'ProductManagement');
    
    const productInfo = await publicClient.readContract({
      address: productManagementAddress,
      abi: ABIS.ProductManagement,
      functionName: 'products',
      args: [BigInt(productId)]
    });

    const result = {
      productId: Number(productInfo[0]),
      merchant: productInfo[1],
      name: productInfo[2],
      description: productInfo[3],
      price: smartPriceFix(productInfo[4]), // 使用智能价格修复
      stock: Number(productInfo[5]),
      isActive: productInfo[6],
      sales: Number(productInfo[7]),
      priceFixed: true, // 标记价格已修复
      originalPrice: productInfo[4].toString() // 保留原始价格用于调试
    };

    setCache(cacheKey, result);
    return result;

  } catch (error) {
    console.error('🚨 [getOptimizedProductInfo] 获取商品信息失败:', error);
    throw new Error(`获取商品信息失败: ${error.message}`);
  }
}

/**
 * 批量优化商品查询
 * 减少RPC调用次数
 */
export async function batchGetOptimizedProducts(productIds) {
  if (!Array.isArray(productIds) || productIds.length === 0) {
    return [];
  }

  const cacheKey = getCacheKey('batchProducts', productIds.join(','));
  const cached = getFromCache(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    // 首先尝试使用 ProductManagement 的 getProductsBatch（包含图片信息）
    try {
      const { getContractAddress } = await import('@/contracts/addresses');
      const { ABIS } = await import('@/contracts/index');
      const { createPublicClient, http } = await import('viem');
      const { bscTestnet } = await import('viem/chains');

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const productManagementAddress = getContractAddress(97, 'ProductManagement');

      const productsBatch = await publicClient.readContract({
        address: productManagementAddress,
        abi: ABIS.ProductManagement,
        functionName: 'getProductsBatch',
        args: [productIds.map(id => BigInt(id))]
      });

      if (productsBatch && productsBatch.length > 0) {
        const products = productsBatch.map(product => ({
          productId: Number(product.productId),
          merchant: product.merchant,
          name: product.name,
          description: product.description,
          images: product.images || [], // 包含图片信息！
          price: fixUpgradedPrice(product.price),
          stock: Number(product.stock),
          isActive: product.isActive,
          sales: Number(product.sales),
          tags: product.tags || [],
          rating: Number(product.rating || 0),
          reviewCount: Number(product.reviewCount || 0),
          priceFixed: true,
          source: 'getProductsBatch'
        }));


        setCache(cacheKey, products);
        return products;
      }
    } catch (batchError) {
      // 静默处理批量获取失败，回退到查询合约
    }

    // 回退到查询合约
    try {
      const { batchGetProductsFromQueryContract } = await import('./queryContractService.js');
      const products = await batchGetProductsFromQueryContract(productIds);


      setCache(cacheKey, products);
      return products;

    } catch (queryError) {
      // 静默处理查询合约失败，回退到传统方法
    }

    // 回退到传统方法：并行查询
    const productPromises = productIds.map(async (productId) => {
      try {
        return await getOptimizedProductInfo(productId);
      } catch (error) {
        // 静默处理单个商品查询失败
        return {
          productId: Number(productId),
          merchant: '0x0000000000000000000000000000000000000000',
          name: '商品信息获取失败',
          description: '',
          price: 0,
          stock: 0,
          isActive: false,
          sales: 0,
          error: error.message
        };
      }
    });

    const products = await Promise.all(productPromises);
    setCache(cacheKey, products);
    return products;

  } catch (error) {
    console.error('🚨 [batchGetOptimizedProducts] 批量获取商品失败:', error);
    throw new Error(`批量获取商品失败: ${error.message}`);
  }
}

/**
 * 获取商品列表（分页）
 */
export async function getOptimizedProductList(page = 1, pageSize = 20) {
  const cacheKey = getCacheKey('productList', page, pageSize);
  const cached = getFromCache(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    const productManagementAddress = getContractAddress(97, 'ProductManagement');
    
    // 获取商品总数
    const totalProducts = await publicClient.readContract({
      address: productManagementAddress,
      abi: ABIS.ProductManagement,
      functionName: 'productCount'
    });

    const totalCount = Number(totalProducts);
    const startIndex = (page - 1) * pageSize + 1;
    const endIndex = Math.min(startIndex + pageSize - 1, totalCount);
    
    if (startIndex > totalCount) {
      return {
        products: [],
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
          hasMore: false
        }
      };
    }

    // 生成商品ID数组
    const productIds = [];
    for (let i = startIndex; i <= endIndex; i++) {
      productIds.push(i);
    }

    // 批量获取商品信息
    const products = await batchGetOptimizedProducts(productIds);
    
    // 过滤掉无效商品
    const validProducts = products.filter(product => 
      product.name && product.name !== '商品信息获取失败'
    );

    const result = {
      products: validProducts,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
        hasMore: endIndex < totalCount
      },
      priceOptimized: true
    };

    setCache(cacheKey, result);
    return result;

  } catch (error) {
    console.error('🚨 [getOptimizedProductList] 获取商品列表失败:', error);
    throw new Error(`获取商品列表失败: ${error.message}`);
  }
}

/**
 * 获取商品统计信息
 */
export async function getProductStats() {
  const cacheKey = getCacheKey('productStats');
  const cached = getFromCache(cacheKey);
  if (cached) {
    return cached;
  }

  try {
    // 优先使用查询合约
    try {
      const { getProductStatsFromQueryContract } = await import('./queryContractService.js');
      const result = await getProductStatsFromQueryContract(100);


      setCache(cacheKey, result);
      return result;

    } catch (queryError) {
      // 静默处理查询合约失败，回退到传统方法
    }

    // 回退到传统方法
    const productManagementAddress = getContractAddress(97, 'ProductManagement');
    const totalProducts = await publicClient.readContract({
      address: productManagementAddress,
      abi: ABIS.ProductManagement,
      functionName: 'productCount'
    });

    const result = {
      totalProducts: Number(totalProducts),
      activeProducts: 0, // 需要遍历计算
      totalSales: 0,
      averagePrice: 0,
      source: 'Traditional',
      note: '部分统计数据需要遍历计算，受RPC限制'
    };

    setCache(cacheKey, result);
    return result;

  } catch (error) {
    console.error('🚨 [getProductStats] 获取商品统计失败:', error);
    return {
      totalProducts: 0,
      activeProducts: 0,
      totalSales: 0,
      averagePrice: 0,
      error: error.message
    };
  }
}

/**
 * 清理过期缓存
 */
export function clearExpiredCache() {
  const now = Date.now();
  let cleared = 0;

  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      cache.delete(key);
      cleared++;
    }
  }

  // 静默清理过期缓存
}

// 定期清理缓存
setInterval(clearExpiredCache, 60000);

export default {
  getOptimizedProductInfo,
  batchGetOptimizedProducts,
  getOptimizedProductList,
  getProductStats,
  smartPriceFix,
  clearExpiredCache
};
