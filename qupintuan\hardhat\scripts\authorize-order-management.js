// scripts/authorize-order-management.js
// 授权 ProductManagement 合约访问 OrderManagement 合约

require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("🔐 授权 ProductManagement 合约访问 OrderManagement...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取部署者
    const [deployer] = await ethers.getSigners();
    console.log("📝 操作者地址:", deployer.address);
    console.log("💰 操作者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

    // 2. 从环境变量获取合约地址
    const orderMgmtAddr = process.env.ORDER_MANAGEMENT_ADDRESS;
    const productMgmtAddr = process.env.PRODUCT_MANAGEMENT_ADDRESS;

    console.log("📋 合约地址:");
    console.log("   • OrderManagement地址:", orderMgmtAddr);
    console.log("   • ProductManagement地址:", productMgmtAddr);

    if (!orderMgmtAddr) {
      throw new Error("请在 .env 文件中设置 ORDER_MANAGEMENT_ADDRESS");
    }
    if (!productMgmtAddr) {
      throw new Error("请在 .env 文件中设置 PRODUCT_MANAGEMENT_ADDRESS");
    }

    // 3. 连接到 OrderManagement 合约
    console.log("⏳ 连接到 OrderManagement 合约...");
    const OrderManagement = await ethers.getContractFactory("OrderManagement");
    const orderManagement = OrderManagement.attach(orderMgmtAddr);

    // 4. 检查当前授权状态
    console.log("🔍 检查当前授权状态...");
    const isAuthorized = await orderManagement.authorizedContracts(productMgmtAddr);
    console.log("📊 ProductManagement 当前授权状态:", isAuthorized);

    if (isAuthorized) {
      console.log("✅ ProductManagement 合约已经被授权，无需重复操作");
      return;
    }

    // 5. 授权 ProductManagement 合约
    console.log("⏳ 授权 ProductManagement 合约...");
    const tx = await orderManagement.setAuthorizedContract(productMgmtAddr, true);
    console.log("📋 交易哈希:", tx.hash);

    // 6. 等待交易确认
    console.log("⏳ 等待交易确认...");
    const receipt = await tx.wait();
    console.log("✅ 交易已确认，区块号:", receipt.blockNumber);

    // 7. 验证授权结果
    console.log("🔍 验证授权结果...");
    const newAuthStatus = await orderManagement.authorizedContracts(productMgmtAddr);
    console.log("📊 ProductManagement 新授权状态:", newAuthStatus);

    if (newAuthStatus) {
      console.log("✅ 授权成功！");
    } else {
      console.log("❌ 授权失败！");
      process.exit(1);
    }

    // 8. 输出授权总结
    console.log("\n🎉 授权完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("🔐 授权操作: 成功");
    console.log("📦 OrderManagement地址:", orderMgmtAddr);
    console.log("🏪 ProductManagement地址:", productMgmtAddr);
    console.log("🌐 网络:", network.name);
    console.log("📋 交易哈希:", tx.hash);
    console.log("🔢 区块号:", receipt.blockNumber);
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    console.log("\n📋 下一步操作:");
    console.log("1. 现在 ProductManagement 合约可以创建订单了");
    console.log("2. 可以开始测试完整的购买和订单管理流程");
    console.log("3. 建议转移 OrderManagement 所有权给 Timelock");

  } catch (error) {
    console.error("❌ 授权失败:", error);
    
    // 提供详细的错误信息
    if (error.message.includes("Ownable: caller is not the owner")) {
      console.error("💡 提示: 只有 OrderManagement 合约的所有者才能进行授权操作");
    } else if (error.message.includes("Invalid address")) {
      console.error("💡 提示: 请检查 .env 文件中的合约地址是否正确");
    }
    
    process.exit(1);
  }
}

// 错误处理
main()
  .then(() => {
    console.log("🎯 授权脚本执行完成");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 授权脚本执行失败:", error);
    process.exit(1);
  });
