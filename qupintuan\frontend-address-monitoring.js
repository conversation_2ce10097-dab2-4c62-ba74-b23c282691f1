// 前端地址添加实时监控代码
// 将此代码添加到 AddressManagement/index.jsx 中

// 1. 添加详细的交易监控函数
const monitorAddressTransaction = async (hash) => {
  console.log('🔍 开始监控地址添加交易:', hash)
  
  try {
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    })

    // 等待交易确认
    console.log('⏳ 等待交易确认...')
    const receipt = await publicClient.waitForTransactionReceipt({ 
      hash,
      timeout: 120000 // 2分钟超时
    })

    console.log('📋 交易收据:', receipt)

    if (receipt.status === 'success') {
      console.log('✅ 交易执行成功')
      
      // 检查是否有 AddressAdded 事件
      const addressAddedTopic = '0x9cc987676e7d63379f176ea50df0ae8d2d9d1141d1231d4ce15b5965f73c9430'
      const addressAddedLogs = receipt.logs.filter(log => 
        log.topics[0] === addressAddedTopic
      )

      if (addressAddedLogs.length > 0) {
        console.log('✅ 找到 AddressAdded 事件:', addressAddedLogs)
        
        // 解码事件数据
        addressAddedLogs.forEach((log, index) => {
          try {
            const addressId = parseInt(log.data, 16)
            console.log(`📋 AddressAdded 事件 ${index + 1}:`)
            console.log(`   - 用户: ${log.topics[1]}`)
            console.log(`   - 地址ID: ${addressId}`)
          } catch (decodeError) {
            console.log('❌ 解码事件失败:', decodeError)
          }
        })

        return { success: true, hasEvent: true, receipt }
      } else {
        console.log('⚠️ 交易成功但没有 AddressAdded 事件')
        return { success: true, hasEvent: false, receipt }
      }
    } else {
      console.log('❌ 交易执行失败')
      return { success: false, receipt }
    }

  } catch (error) {
    console.error('❌ 监控交易失败:', error)
    return { success: false, error: error.message }
  }
}

// 2. 验证合约状态变化
const verifyAddressInContract = async (account) => {
  try {
    console.log('🔍 验证合约中的地址状态...')
    
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    })

    const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

    // 等待一小段时间确保状态同步
    await new Promise(resolve => setTimeout(resolve, 3000))

    const addresses = await publicClient.readContract({
      address: addressAddress,
      abi: ABIS.AddressManagement,
      functionName: 'getMyAddresses',
      args: [],
      account
    })

    console.log('📋 合约中的地址列表:', addresses)
    return addresses

  } catch (error) {
    console.error('❌ 验证合约状态失败:', error)
    throw error
  }
}

// 3. 改进的添加地址函数
const handleAddAddressWithMonitoring = async () => {
  if (!validateForm()) return

  setIsSubmitting(true)
  
  try {
    console.log('🚀 开始添加地址流程...')
    console.log('📋 表单数据:', formData)

    // 记录添加前的地址数量
    const beforeAddresses = await verifyAddressInContract(account)
    console.log('📋 添加前地址数量:', beforeAddresses.length)

    const walletClient = createWalletClient({
      chain: bscTestnet,
      transport: custom(window.ethereum)
    })

    const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

    console.log('📋 准备提交交易...')
    console.log('   - 合约地址:', addressAddress)
    console.log('   - 用户账户:', account)

    // 提交交易
    const hash = await walletClient.writeContract({
      address: addressAddress,
      abi: ABIS.AddressManagement,
      functionName: 'addAddress',
      args: [
        formData.name,
        formData.phone,
        formData.province,
        formData.city,
        formData.district,
        formData.detail,
        formData.isDefault
      ],
      account
    })

    console.log('✅ 交易已提交:', hash)
    toast.success(`交易已提交: ${hash}`)

    // 监控交易状态
    const monitorResult = await monitorAddressTransaction(hash)
    
    if (monitorResult.success) {
      if (monitorResult.hasEvent) {
        console.log('✅ 交易成功且有 AddressAdded 事件')
        
        // 验证合约状态
        const afterAddresses = await verifyAddressInContract(account)
        console.log('📋 添加后地址数量:', afterAddresses.length)
        
        if (afterAddresses.length > beforeAddresses.length) {
          console.log('✅ 地址确实已添加到合约中')
          toast.success('地址添加成功！')
          resetForm()
          await loadAddresses()
        } else {
          console.log('❌ 有事件但合约状态未改变')
          toast.error('地址添加异常：请刷新页面重试')
        }
      } else {
        console.log('⚠️ 交易成功但没有事件')
        toast.warning('交易成功但可能未生效，请刷新页面检查')
      }
    } else {
      console.log('❌ 交易失败')
      toast.error(`交易失败: ${monitorResult.error || '未知错误'}`)
    }

  } catch (error) {
    console.error('❌ 添加地址失败:', error)
    
    // 详细的错误分析
    if (error.message.includes('User rejected')) {
      console.log('💡 用户取消了交易')
      toast.error('您取消了交易')
    } else if (error.message.includes('insufficient funds')) {
      console.log('💡 余额不足')
      toast.error('余额不足，无法支付Gas费用')
    } else if (error.message.includes('execution reverted')) {
      console.log('💡 合约执行失败')
      toast.error('合约执行失败，请检查参数或联系管理员')
    } else {
      console.log('💡 其他错误:', error.message)
      toast.error(`添加地址失败: ${error.message}`)
    }
  } finally {
    setIsSubmitting(false)
  }
}

// 4. 添加调试按钮组件
const AddressDebugPanel = () => {
  const [debugInfo, setDebugInfo] = useState(null)
  const [isDebugging, setIsDebugging] = useState(false)

  const runDebug = async () => {
    setIsDebugging(true)
    try {
      const addresses = await verifyAddressInContract(account)
      setDebugInfo({
        timestamp: new Date().toISOString(),
        userAccount: account,
        addressCount: addresses.length,
        addresses: addresses,
        contractAddress: CONTRACT_ADDRESSES[97].AddressManagement
      })
    } catch (error) {
      setDebugInfo({
        timestamp: new Date().toISOString(),
        error: error.message
      })
    } finally {
      setIsDebugging(false)
    }
  }

  return (
    <div className="mt-4 p-4 border rounded bg-gray-50">
      <h4 className="font-semibold mb-2">调试面板</h4>
      <button 
        onClick={runDebug}
        disabled={isDebugging}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {isDebugging ? '检查中...' : '检查合约状态'}
      </button>
      
      {debugInfo && (
        <div className="mt-4">
          <h5 className="font-medium mb-2">调试结果:</h5>
          <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-40">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}

// 使用说明：
// 1. 将 handleAddAddress 替换为 handleAddAddressWithMonitoring
// 2. 在组件中添加 <AddressDebugPanel /> 
// 3. 让用户重新尝试添加地址，观察详细日志
