{"timestamp": "2025-08-01T01:51:41.867Z", "stage": "04-submit-proposal", "proposalTxId": "6", "transactionHash": "0x33df2d0621de698af468be6fa88b907cd89d2372462c1809ec987c3ef8f8e9e4", "proxyAddress": "0x13a311f1d52376861207ae641278f6e5de55F4B5", "newImplementationAddress": "0xC789526111F33C722895Cc81AAB5D5E3e9c37f3D", "timelockAddress": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "multiSigAddress": "0x85D2a947B0dA2c73a4342A2656a142B16CD71CFb", "salt": "0x36cbefe3c57902cf8a9d7902aff26ff6a3ab1ec75f22134f55bce0b7372324a0", "delay": 600, "executeTime": 1754013701, "proposalId": "0x0b0a7d20c4219715e24f8b4270776e14c6d766c7021103fe60fd68b15a40ae4b", "upgradeCalldata": "0x3659cfe6000000000000000000000000c789526111f33c722895cc81aab5d5e3e9c37f3d", "scheduleCalldata": "0x01d5062a00000000000000000000000013a311f1d52376861207ae641278f6e5de55f4b5000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c0000000000000000000000000000000000000000000000000000000000000000036cbefe3c57902cf8a9d7902aff26ff6a3ab1ec75f22134f55bce0b7372324a0000000000000000000000000000000000000000000000000000000000000025800000000000000000000000000000000000000000000000000000000000000243659cfe6000000000000000000000000c789526111f33c722895cc81aab5d5e3e9c37f3d00000000000000000000000000000000000000000000000000000000", "proposer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "status": "SUBMITTED", "description": "升级 MerchantManagement 合约以添加 ProductManagement 授权支持", "changes": ["新增 productManagementAddress 状态变量", "修改 updateMerchantSalesPoints 权限检查", "新增 setProductManagementAddress 管理函数", "新增 getProductManagementAddress 查询函数"]}