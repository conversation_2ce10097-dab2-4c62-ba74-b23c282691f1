// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/structs/EnumerableSetUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

import "./interfaces/IQPTLock.sol";
import "./interfaces/INodeStaking.sol";

/**
 * @title NodeStaking - 优化版
 * @notice 简化的节点质押合约，移除了复杂的分红池逻辑
 * @dev 基于简化分红逻辑：总余额 ÷ 有效节点数 = 每节点分红
 */
contract NodeStaking is
    Initializable,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    // Timelock 控制
    address public timelock;

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    using SafeERC20Upgradeable for IERC20Upgradeable;
    using EnumerableSetUpgradeable for EnumerableSetUpgradeable.AddressSet;

    IQPTLock public qptLocker;
    IERC20Upgradeable public usdtToken;

    uint256 public startTime;
    uint256 public constant MAX_NODES = 1000;
    uint256 public initialStakeAmount;

    // ===== 节点管理 =====
    uint256 public totalEffectiveNodes;

    // ===== 简化分红管理 =====
    uint256 public currentDay;              // 当前日期（天数）
    uint256 public dailyRewardPerNode;      // 当日每节点可领取分红

    // ===== 节点状态映射 =====
    mapping(address => bool) public isNodeActive;
    mapping(address => uint256) public lastClaimDay;      // 每个节点最后领取的日期
    mapping(address => uint256) public nodeActivationTime; // 节点激活时间记录

    EnumerableSetUpgradeable.AddressSet private nodeAddresses;

    event NodeStaked(address indexed user, uint256 activationTime);
    event NodeUnstaked(address indexed user);
    event DailyRewardCalculated(uint256 indexed day, uint256 totalBalance, uint256 effectiveNodes, uint256 rewardPerNode);
    event RewardClaimed(address indexed user, uint256 amount, uint256 day);
    event DividendReceived(address indexed sender, uint256 amount);

    // BNB相关事件
    event BNBReceived(address indexed sender, uint256 amount);
    event BNBWithdrawn(address indexed to, uint256 amount);
    event EmergencyUSDTWithdrawn(address indexed to, uint256 amount);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @dev Initializer for UUPS proxy
     * @param _qptLocker Address of QPT lock contract
     * @param _usdtToken Address of USDT token contract
     */
    /// @notice 设置Timelock地址（仅所有者）
    /// @param _timelock 新的Timelock地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    /**
     * @notice 初始化合约
     * @param _qptLocker QPT锁仓合约地址
     * @param _usdtToken USDT代币合约地址
     * @param _timelock 时间锁合约地址
     */
    function initialize(
        address _qptLocker,
        address _usdtToken,
        address _timelock
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(_qptLocker != address(0), "Invalid QPT locker");
        require(_usdtToken != address(0), "Invalid USDT token");
        require(_timelock != address(0), "Invalid timelock");

        qptLocker = IQPTLock(_qptLocker);
        usdtToken = IERC20Upgradeable(_usdtToken);
        timelock = _timelock;

        // 设置为部署当天的00:00:00 UTC时间
        startTime = (block.timestamp / 1 days) * 1 days;
        initialStakeAmount = 1000 * 1e18; // 1000 QPT

        // 初始化分红系统
        currentDay = 0; // 等待首次计算
        dailyRewardPerNode = 0;
    }

    /**
     * @notice 接收分红资金
     * @param amount 分红金额
     */
    function receiveDividendFund(uint256 amount) external nonReentrant whenNotPaused {
        require(amount > 0, "Zero amount");
        usdtToken.safeTransferFrom(msg.sender, address(this), amount);

        emit DividendReceived(msg.sender, amount);
    }

    /**
     * @notice 计算并更新当日分红（简化版本）
     * @dev 直接基于合约当前USDT余额计算分红，简单直接
     */
    function calculateDailyReward() external whenNotPaused {
        uint256 today = block.timestamp / 1 days;
        require(today > currentDay, "Already calculated for today");

        // 获取合约当前USDT余额
        uint256 totalBalance = usdtToken.balanceOf(address(this));

        // 计算每节点分红：总余额 ÷ 有效节点数
        if (totalEffectiveNodes > 0 && totalBalance > 0) {
            dailyRewardPerNode = totalBalance / totalEffectiveNodes;
        } else {
            dailyRewardPerNode = 0;
        }

        // 更新当前日期
        currentDay = today;

        emit DailyRewardCalculated(today, totalBalance, totalEffectiveNodes, dailyRewardPerNode);
    }

    /**
     * @notice Calculate current required stake amount based on days since launch
     * @dev Days are calculated from 00:00:00 UTC of launch day
     * Each day increases stake requirement by 10 QPT
     */
    function getCurrentRequiredStake() public view returns (uint256) {
        uint256 daysSinceLaunch = (block.timestamp - startTime) / 1 days;
        return initialStakeAmount + daysSinceLaunch * 10 * 1e18;
    }

    /**
     * @notice Stake node by locking required QPT
     */
    function stakeNode() external nonReentrant whenNotPaused {
        require(!isNodeActive[msg.sender], "Already a node");
        require(totalEffectiveNodes < MAX_NODES, "All nodes taken");

        uint256 required = getCurrentRequiredStake();
        qptLocker.lockNodeQPT(msg.sender, required);

        isNodeActive[msg.sender] = true;
        nodeAddresses.add(msg.sender);
        totalEffectiveNodes++;

        // 记录节点激活时间
        nodeActivationTime[msg.sender] = block.timestamp;

        emit NodeStaked(msg.sender, block.timestamp);
    }

    /**
     * @notice Unstake node and unlock QPT
     */
    function unstakeNode() external nonReentrant whenNotPaused {
        require(isNodeActive[msg.sender], "Not a node");

        isNodeActive[msg.sender] = false;
        nodeAddresses.remove(msg.sender);
        totalEffectiveNodes--;

        // 清除节点激活时间记录
        delete nodeActivationTime[msg.sender];

        qptLocker.unlockNodeQPT(msg.sender);

        emit NodeUnstaked(msg.sender);
    }







    /**
     * @notice 领取当日分红
     */
    function claimReward() external nonReentrant whenNotPaused {
        uint256 today = block.timestamp / 1 days;

        // 验证节点状态
        require(isNodeActive[msg.sender], "Not an active node");
        require(lastClaimDay[msg.sender] < today, "Already claimed today");
        require(today == currentDay, "Daily reward not calculated yet");
        require(dailyRewardPerNode > 0, "No reward available");

        // 验证节点激活时间：刚激活的节点需要等到次日00:00后才可以参与领取分红
        uint256 activationDay = nodeActivationTime[msg.sender] / 1 days;
        require(today > activationDay, "Node must wait until next day to claim rewards");

        // 验证QPT质押状态 - 已激活节点只需要验证有质押即可
        require(_verifyQPTStakeForActivatedNode(msg.sender), "QPT stake verification failed");

        // 检查合约余额是否足够
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        require(contractBalance >= dailyRewardPerNode, "Insufficient contract balance");

        // 更新领取记录
        lastClaimDay[msg.sender] = today;

        // 转账分红
        usdtToken.safeTransfer(msg.sender, dailyRewardPerNode);

        emit RewardClaimed(msg.sender, dailyRewardPerNode, today);
    }

    /**
     * @notice 验证用户的QPT质押状态（用于新节点激活）
     */
    function _verifyQPTStake(address user, uint256 requiredAmount) internal view returns (bool) {
        try qptLocker.getUserLockedAmount(user) returns (uint256 lockedAmount) {
            return lockedAmount >= requiredAmount;
        } catch {
            return false;
        }
    }

    /**
     * @notice 验证已激活节点的QPT质押状态（只需要验证有质押即可）
     * @dev 已激活节点在激活时已经满足了当时的质押要求，不需要满足当前的质押要求
     */
    function _verifyQPTStakeForActivatedNode(address user) internal view returns (bool) {
        try qptLocker.getUserLockedAmount(user) returns (uint256 lockedAmount) {
            return lockedAmount > 0;
        } catch {
            return false;
        }
    }

    /**
     * @notice Get paginated list of node addresses
     */
    function getNodeAddressesPaginated(uint256 page, uint256 pageSize) external view returns (address[] memory) {
        address[] memory allNodes = nodeAddresses.values();
        if (allNodes.length == 0) {
            return new address[](0);
        }

        uint256 start = page * pageSize;
        require(start < allNodes.length, "Invalid page");

        uint256 end = start + pageSize;
        if (end > allNodes.length) {
            end = allNodes.length;
        }

        address[] memory result = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            result[i - start] = allNodes[i];
        }
        return result;
    }

    /**
     * @notice Get all active node addresses
     */
    function getNodeAddresses() external view returns (address[] memory) {
        return nodeAddresses.values();
    }

    /**
     * @notice View daily reward per node without snapshot
     */
    function getDailyRewardPerNode() external view returns (uint256) {
        return dailyRewardPerNode;
    }

    /**
     * @notice Check if a user has claimed today's reward
     */
    function hasClaimedTodayReward(address user) external view returns (bool) {
        return lastClaimDay[user] == block.timestamp / 1 days;
    }

    /**
     * @notice 获取合约USDT总余额
     */
    function getTotalDividends() external view returns (uint256) {
        return usdtToken.balanceOf(address(this));
    }

    /** Only owner can pause/unpause and upgrade **/
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    /// @notice 接收BNB转账，用于支付Gas费
    /// @dev 合约需要BNB来执行主动转账操作
    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 备用接收函数
    fallback() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 查询合约BNB余额
    /// @return 合约当前BNB余额（wei）
    function getBNBBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice 管理员提取BNB（紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量（wei）
    function withdrawBNB(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount <= address(this).balance, "Insufficient balance");

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");

        emit BNBWithdrawn(to, amount);
    }

    /// @notice 紧急提取 USDT（仅所有者，用于合约迁移或紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量
    function withdrawUSDT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= usdtToken.balanceOf(address(this)), "Insufficient USDT balance");

        usdtToken.safeTransfer(to, amount);
        emit EmergencyUSDTWithdrawn(to, amount);
    }





    /**
     * @notice 授权升级（仅时间锁）
     * @param newImplementation 新实现合约地址
     */
    function _authorizeUpgrade(address newImplementation) internal override onlyTimelock {
        // 时间锁控制的升级授权
    }

    /// @notice 获取分红详细信息（重新设计版本）
    /// @return dividendDetails 分红详细信息结构体
    function getDividendDetails() external view returns (DividendDetails memory dividendDetails) {
        // 合约总USDT余额
        uint256 totalBalance = usdtToken.balanceOf(address(this));
        dividendDetails.totalDividends = totalBalance;

        // 简化逻辑：不再区分分红池，直接使用总余额
        dividendDetails.availableDividendPool = totalBalance;
        dividendDetails.todayNewDividends = 0; // 不再计算今日新增

        dividendDetails.totalEffectiveNodes = totalEffectiveNodes;
        dividendDetails.rewardPerNode = dailyRewardPerNode;
        dividendDetails.lastCalculateTime = currentDay;

        // 检查今日是否可以计算分红
        uint256 today = block.timestamp / 1 days;
        dividendDetails.canCalculateToday = (currentDay == 0 || today > currentDay);
    }

    /// @notice 获取节点激活时间
    /// @param user 用户地址
    /// @return activationTime 节点激活时间戳，如果未激活则返回0
    function getNodeActivationTime(address user) external view returns (uint256 activationTime) {
        return nodeActivationTime[user];
    }



    /// @notice 获取用户节点完整状态（避免 RPC 不同步问题）
    /// @param user 用户地址
    /// @return nodeStatus 用户节点状态结构体
    function getUserNodeStatus(address user) external view returns (UserNodeStatus memory nodeStatus) {
        nodeStatus.user = user;
        nodeStatus.isActive = isNodeActive[user];
        nodeStatus.lastClaimDate = lastClaimDay[user];
        nodeStatus.requiredStakeAmount = getCurrentRequiredStake();

        // 计算今天的日期
        uint256 today = block.timestamp / 1 days;
        nodeStatus.hasClaimedToday = (lastClaimDay[user] == today);
        nodeStatus.canClaimToday = nodeStatus.isActive && !nodeStatus.hasClaimedToday &&
                                   today == currentDay && dailyRewardPerNode > 0;

        // 计算奖励信息
        nodeStatus.dailyReward = dailyRewardPerNode;

        // 系统信息
        nodeStatus.totalActiveNodes = totalEffectiveNodes;
        nodeStatus.maxNodes = MAX_NODES;
        nodeStatus.availableSlots = MAX_NODES - totalEffectiveNodes;
        nodeStatus.canStakeNode = !nodeStatus.isActive && nodeStatus.availableSlots > 0;

        // 时间信息
        nodeStatus.currentTime = block.timestamp;
        nodeStatus.startTime = startTime;
        nodeStatus.daysSinceLaunch = (block.timestamp - startTime) / 1 days;

        // 分红信息
        nodeStatus.totalDividends = usdtToken.balanceOf(address(this));

        // 激活时间
        nodeStatus.activationTime = nodeActivationTime[user];
    }

    /// @notice 批量获取用户节点状态
    /// @param userList 用户地址列表
    /// @return nodeStatuses 用户节点状态列表
    function getBatchUserNodeStatus(address[] calldata userList)
        external
        view
        returns (UserNodeStatus[] memory nodeStatuses)
    {
        nodeStatuses = new UserNodeStatus[](userList.length);
        for (uint256 i = 0; i < userList.length; i++) {
            nodeStatuses[i] = this.getUserNodeStatus(userList[i]);
        }
    }

    /// @notice 检查用户节点操作权限
    /// @param user 用户地址
    /// @param operation 操作类型 (0: 质押, 1: 取消质押, 2: 领取奖励)
    /// @return canOperate 是否可以操作
    /// @return reason 不能操作的原因
    function checkNodeOperationEligibility(address user, uint8 operation)
        external
        view
        returns (bool canOperate, string memory reason)
    {
        if (operation == 0) {
            // 质押节点检查
            if (isNodeActive[user]) {
                return (false, "Already a node");
            }
            if (totalEffectiveNodes >= MAX_NODES) {
                return (false, "All nodes taken");
            }
            return (true, "");

        } else if (operation == 1) {
            // 取消质押检查
            if (!isNodeActive[user]) {
                return (false, "Not a node");
            }
            return (true, "");

        } else if (operation == 2) {
            // 领取奖励检查
            if (!isNodeActive[user]) {
                return (false, "Not a node");
            }

            uint256 today = block.timestamp / 1 days;
            if (lastClaimDay[user] == today) {
                return (false, "Already claimed today");
            }

            if (totalEffectiveNodes == 0) {
                return (false, "No active nodes");
            }

            return (true, "");

        } else {
            return (false, "Invalid operation type");
        }
    }

    /// @notice 获取系统节点统计信息
    /// @return systemStats 系统节点统计结构体
    function getSystemNodeStats() external view returns (SystemNodeStats memory systemStats) {
        systemStats.totalActiveNodes = totalEffectiveNodes;
        systemStats.maxNodes = MAX_NODES;
        systemStats.availableSlots = MAX_NODES - totalEffectiveNodes;
        systemStats.totalDividends = usdtToken.balanceOf(address(this)); // 合约总USDT余额
        systemStats.dailyRewardPerNode = dailyRewardPerNode;
        systemStats.currentRequiredStake = getCurrentRequiredStake();
        systemStats.initialStakeAmount = initialStakeAmount;
        systemStats.startTime = startTime;
        systemStats.daysSinceLaunch = (block.timestamp - startTime) / 1 days;
        systemStats.currentTime = block.timestamp;
    }

    // 分红详细信息结构体（重新设计版本）
    struct DividendDetails {
        uint256 totalDividends;          // 合约总USDT余额
        uint256 todayNewDividends;       // 保留字段（兼容性，但不再使用）
        uint256 availableDividendPool;   // 等同于totalDividends（简化逻辑）
        uint256 totalEffectiveNodes;     // 总活跃节点数
        uint256 rewardPerNode;           // 每节点可领取分红
        uint256 lastCalculateTime;       // 最后计算时间
        bool canCalculateToday;          // 今日是否可以计算分红
    }

    // 用户节点状态结构体
    struct UserNodeStatus {
        address user;                    // 用户地址
        bool isActive;                   // 是否为活跃节点
        uint256 lastClaimDate;           // 最后领取日期
        uint256 requiredStakeAmount;     // 当前需要质押的数量
        bool hasClaimedToday;            // 今天是否已领取
        bool canClaimToday;              // 今天是否可以领取
        uint256 dailyReward;             // 每日奖励
        uint256 totalActiveNodes;        // 总活跃节点数
        uint256 maxNodes;                // 最大节点数
        uint256 availableSlots;          // 可用节点槽位
        bool canStakeNode;               // 是否可以质押节点
        uint256 currentTime;             // 当前时间
        uint256 startTime;               // 开始时间
        uint256 daysSinceLaunch;         // 启动以来的天数
        uint256 totalDividends;          // 合约总USDT余额
        uint256 activationTime;          // 节点激活时间
    }

    // 系统节点统计结构体
    struct SystemNodeStats {
        uint256 totalActiveNodes;        // 总活跃节点数
        uint256 maxNodes;                // 最大节点数
        uint256 availableSlots;          // 可用槽位
        uint256 totalDividends;          // 合约总USDT余额

        uint256 currentRequiredStake;    // 当前需要质押数量
        uint256 initialStakeAmount;      // 初始质押数量
        uint256 startTime;               // 开始时间
        uint256 daysSinceLaunch;         // 启动天数
        uint256 currentTime;             // 当前时间
        uint256 dailyRewardPerNode;      // 每节点每日奖励
    }





    /**
     * @notice 获取合约版本
     * @return version 合约版本号
     */
    /**
     * @notice 获取合约版本
     * @return version 合约版本号
     */
    function getVersion() external pure returns (string memory) {
        return "4.0.0-simplified-dividend";
    }



    // ===== 存储槽预留 =====
    /**
     * @dev 为未来升级预留存储空间
     * 优化版本使用更少的存储槽，为未来扩展留出空间
     */
    uint256[48] private __gap;
}
