{"timestamp": "2025-07-31T23:05:58.160Z", "stage": "4-propose-upgrade", "proposalTxId": "3", "transactionHash": "0x2e6cc5121b1967f6069a6d081e666262295551b8219672eb786d2efb317f02d0", "proxyAddress": "0xfC0b4aD3c5eb6AFA4Ee13d16492fc0D75eff272F", "newImplementationAddress": "0xCF37BA4cF9c86fe110f0041671A24e94F4D73fd6", "timelockAddress": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "multiSigAddress": "0x85D2a947B0dA2c73a4342A2656a142B16CD71CFb", "salt": "0xb9584dbfaaba7ba23a7f6a5bf6b8cb4d1e8769b42abf0ea9e75dcba7a9ee1025", "delay": 600, "upgradeCalldata": "0x4f1ef286000000000000000000000000cf37ba4cf9c86fe110f0041671a24e94f4d73fd6000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000045cd8a76b00000000000000000000000000000000000000000000000000000000", "scheduleCalldata": "0x01d5062a000000000000000000000000fc0b4ad3c5eb6afa4ee13d16492fc0d75eff272f000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000000b9584dbfaaba7ba23a7f6a5bf6b8cb4d1e8769b42abf0ea9e75dcba7a9ee1025000000000000000000000000000000000000000000000000000000000000025800000000000000000000000000000000000000000000000000000000000000844f1ef286000000000000000000000000cf37ba4cf9c86fe110f0041671a24e94f4d73fd6000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000045cd8a76b0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "proposer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "status": "SUBMITTED", "description": "升级 GroupBuyRoom 合约以修复重复支付问题", "changes": ["使用 else if 结构避免重复支付", "添加 winnerIndex 有效性验证", "优化身份判断逻辑"]}