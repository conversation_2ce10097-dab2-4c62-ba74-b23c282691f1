// src/apis/mallApi.js
import { readContract, writeContract, waitForTransactionReceipt } from 'wagmi/actions';

/**
 * 获取合约信息
 */
async function getContractInfo(contractName) {
  const { getContractAddress } = await import('@/contracts/addresses');
  const { ABIS } = await import('@/contracts/index');

  const contractAddress = getContractAddress(97, contractName); // BSC 测试网
  const contractABI = ABIS[contractName];

  return { contractAddress, contractABI };
}

/**
 * 获取 wagmi 配置
 */
async function getWagmiConfig() {
  const { config } = await import('@/wagmi.config');
  return config;
}

// ==================== 商家管理 API ====================

/**
 * 检查用户是否为商家
 * @param {Object} params - 参数对象
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<Object>} - 返回商家状态对象 {isRegistered, isVerified}
 */
export async function isMerchant({ userAddress }) {
  try {
    const { contractAddress, contractABI } = await getContractInfo('MerchantManagement');
    const config = await getWagmiConfig();

    // 尝试使用新的 getMerchantStatus 函数（如果合约已升级）
    try {
      const statusResult = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getMerchantStatus',
        args: [userAddress],
      });

      const [isRegistered, isVerified, hasSubmitted, verifyTime] = statusResult;

      return {
        isRegistered,
        isVerified,
        hasSubmittedVerification: hasSubmitted,
        verifyTime: Number(verifyTime)
      };

    } catch (error) {
      // 如果新函数不存在，使用旧的逻辑
    }

    // 旧版本合约的兼容逻辑
    const [merchantInfo, verificationInfo] = await Promise.all([
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'merchants',
        args: [userAddress],
      }),
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'verifications',
        args: [userAddress],
      })
    ]);

    // merchants 返回: (name, description, logo, isActive, createTime, totalSales, totalOrders, totalPoints, exchangedPoints)
    // verifications 返回: (isVerified, verifyTime)
    const createTime = Number(merchantInfo[4]);
    const isRegistered = createTime > 0;
    const isVerified = verificationInfo[0];
    const verifyTime = Number(verificationInfo[1]);

    // 检查是否已提交审核申请：通过合约状态判断
    let hasSubmittedVerification = isVerified; // 如果已认证，肯定已提交

    if (!isVerified && isRegistered) {
      // 尝试使用新的 hasSubmittedVerification 函数
      try {
        hasSubmittedVerification = await readContract(config, {
          address: contractAddress,
          abi: contractABI,
          functionName: 'hasSubmittedVerification',
          args: [userAddress],
        });
      } catch (error) {
        // 如果新函数不存在，使用旧逻辑
        // 旧合约问题：submitVerification 设置 verifyTime = 0，无法通过时间判断
        // 解决方案：检查 verification 记录是否存在
        try {
          const verificationResult = await readContract(config, {
            address: contractAddress,
            abi: contractABI,
            functionName: 'getMerchantVerification',
            args: [userAddress],
          });

          // 如果能成功调用 getMerchantVerification，说明有 verification 记录
          // 这表明用户已经调用过 submitVerification
          hasSubmittedVerification = true;

        } catch (getMerchantError) {
          // 如果 getMerchantVerification 也失败，说明没有记录
          hasSubmittedVerification = false;
        }
      }
    }

    const result = {
      isRegistered,
      isVerified,
      hasSubmittedVerification,
      verifyTime
    };

    return result;
  } catch (error) {
    console.error('🚨 [isMerchant] 检查商家状态失败:', error);
    throw new Error(`检查商家状态失败: ${error.message}`);
  }
}

/**
 * 获取商家信息
 * @param {Object} params - 参数对象
 * @param {string} params.merchantAddress - 商家地址
 * @returns {Promise<Object>} - 返回商家信息
 */
export async function getMerchantInfo({ merchantAddress }) {
  try {
    const { contractAddress, contractABI } = await getContractInfo('MerchantManagement');
    const config = await getWagmiConfig();

    const [merchantInfo, verification] = await Promise.all([
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'merchants',
        args: [merchantAddress],
      }),
      readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'verifications',
        args: [merchantAddress],
      })
    ]);

    // merchants 返回: (name, description, logo, isActive, createTime, totalSales, totalOrders, totalPoints, exchangedPoints)
    // verifications 返回: (isVerified, verifyTime)
    return {
      name: merchantInfo[0],
      description: merchantInfo[1],
      logo: merchantInfo[2],
      isActive: merchantInfo[3],
      createTime: Number(merchantInfo[4]),
      totalSales: Number(merchantInfo[5]),
      totalOrders: Number(merchantInfo[6]),
      totalPoints: Number(merchantInfo[7]),
      exchangedPoints: Number(merchantInfo[8]),
      isVerified: verification[0],
      verifyTime: Number(verification[1])
    };
  } catch (error) {
    console.error('🚨 [getMerchantInfo] 获取商家信息失败:', error);
    throw new Error(`获取商家信息失败: ${error.message}`);
  }
}

/**
 * 商家注册
 * @param {Object} params - 参数对象
 * @param {string} params.name - 商家名称
 * @param {string} params.description - 商家描述
 * @param {string} params.logo - 商家logo链接
 * @param {string} params.contactPhone - 联系电话
 * @param {string} params.contactEmail - 联系邮箱
 * @param {string} params.businessLicense - 营业执照号
 * @param {string} params.address - 经营地址
 * @param {string} params.category - 经营类目
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function registerMerchant({
  name,
  description,
  logo,
  contactPhone,
  contactEmail,
  businessLicense,
  address,
  category,
  signer
}) {
  try {
    // 将额外信息合并到描述中，以便存储到合约
    const enhancedDescription = `${description}

📞 联系方式：
电话：${contactPhone}
邮箱：${contactEmail}

📍 经营信息：
地址：${address}
类目：${category}
执照：${businessLicense}`;



    const { contractAddress, contractABI } = await getContractInfo('MerchantManagement');
    const config = await getWagmiConfig();

    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }

    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'registerMerchant',
      args: [name, enhancedDescription, logo],
      account: signerAddress,
    });



    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });



    // 注释掉自动提交审核申请，让用户手动选择何时提交
    // 这样符合业务逻辑：注册商家身份 ≠ 提交审核申请

    // 保存商家地址到本地存储，供管理后台使用
    try {
      const existingAddresses = JSON.parse(localStorage.getItem('registeredMerchants') || '[]');
      if (!existingAddresses.includes(signerAddress)) {
        existingAddresses.push(signerAddress);
        localStorage.setItem('registeredMerchants', JSON.stringify(existingAddresses));

      }
    } catch (error) {
      // 静默处理本地存储失败
    }

    return { receipt, txHash };

  } catch (error) {
    console.error('🚨 [registerMerchant] 商家注册失败:', error);
    throw new Error(`商家注册失败: ${error.message}`);
  }
}

/**
 * 手动提交商家审核申请
 * @param {Object} params - 参数对象
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function submitMerchantVerification({ signer }) {
  try {


    const { contractAddress, contractABI } = await getContractInfo('MerchantManagement');
    const config = await getWagmiConfig();

    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }

    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'submitVerification',
      args: [],
      account: signerAddress,
    });



    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });

    // 不再需要本地存储，直接通过合约状态判断
    return { receipt, txHash };

  } catch (error) {
    console.error('🚨 [submitMerchantVerification] 提交审核申请失败:', error);
    throw new Error(`提交审核申请失败: ${error.message}`);
  }
}

// ==================== 商品管理 API ====================

/**
 * 获取商品信息
 * @param {Object} params - 参数对象
 * @param {number} params.productId - 商品ID
 * @returns {Promise<Object>} - 返回商品信息
 */
export async function getProductInfo({ productId }) {
  try {
    // 首先尝试使用 getProductsBatch 获取完整信息（包括图片）
    try {
      const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
      const config = await getWagmiConfig();

      const productsBatch = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'getProductsBatch',
        args: [[BigInt(productId)]],
      });

      if (productsBatch && productsBatch.length > 0) {
        const product = productsBatch[0];

        // 价格格式化处理
        const { fixUpgradedPrice } = await import('@/utils/priceFixForUpgrade');
        const formattedPrice = fixUpgradedPrice(product.price);

        return {
          productId: Number(product.productId),
          merchant: product.merchant,
          name: product.name,
          description: product.description,
          images: product.images || [], // 使用合约返回的图片数组
          price: formattedPrice,
          stock: Number(product.stock),
          isActive: product.isActive,
          sales: Number(product.sales),
          tags: product.tags || [],
          rating: Number(product.rating || 0),
          reviewCount: Number(product.reviewCount || 0),
          priceFixed: true,
          source: 'getProductsBatch'
        };
      }
    } catch (batchError) {
      // 静默处理批量查询失败，回退到优化查询
    }

    // 回退到优化的商品查询服务（但没有图片信息）
    try {
      const { getOptimizedProductInfo } = await import('@/utils/optimizedProductQuery');
      const optimizedResult = await getOptimizedProductInfo(productId);

      return {
        ...optimizedResult,
        images: [] // 优化查询无法获取图片信息
      };
    } catch (optimizedError) {
      // 静默处理优化查询失败，回退到传统方法
    }

    // 回退到传统方法
    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();

    const productInfo = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'products',
      args: [productId],
    });

    // products 返回: (productId, merchant, name, description, price, stock, isActive, sales)
    // 注意：images 数组不在公开访问器的返回值中，需要单独获取

    // 导入升级后的价格修复工具
    const { fixUpgradedPrice } = await import('@/utils/priceFixForUpgrade');

    // 安全地处理价格数据
    let formattedPrice = 0;
    try {
      const rawPrice = productInfo[4];
      if (rawPrice && rawPrice !== '0') {
        // 使用升级后的价格修复逻辑
        formattedPrice = fixUpgradedPrice(rawPrice);

        // 验证格式化结果
        if (isNaN(formattedPrice) || formattedPrice < 0) {
          console.warn('商品价格格式化异常:', { rawPrice, formattedPrice });
          formattedPrice = 0;
        }
      }
    } catch (error) {
      console.error('商品价格格式化失败:', error, '原始价格:', productInfo[4]);
      formattedPrice = 0;
    }

    return {
      productId: Number(productInfo[0]),
      merchant: productInfo[1],
      name: productInfo[2],
      description: productInfo[3],
      images: [], // 传统方法无法获取图片信息
      price: formattedPrice, // 安全的价格格式化
      stock: Number(productInfo[5]),
      isActive: productInfo[6],
      sales: Number(productInfo[7]),
      source: 'traditional'
    };
  } catch (error) {
    console.error('🚨 [getProductInfo] 获取商品信息失败:', error);
    throw new Error(`获取商品信息失败: ${error.message}`);
  }
}

/**
 * 获取商品信息（别名函数，为了兼容性）
 * @param {Object} params - 参数对象
 * @param {number} params.productId - 商品ID
 * @returns {Promise<Object>} - 返回商品信息
 */
export async function getProduct({ productId }) {
  return getProductInfo({ productId });
}

/**
 * 获取商品总数
 * @returns {Promise<number>} - 返回商品总数
 */
export async function getProductCount() {
  try {
    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();

    const count = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'productCount',
      args: [],
    });

    const productCount = Number(count);
    return productCount;
  } catch (error) {
    console.error('🚨 [getProductCount] 获取商品总数失败:', error);
    throw new Error(`获取商品总数失败: ${error.message}`);
  }
}

/**
 * 获取商家的商品列表
 * @param {Object} params - 参数对象
 * @param {string} params.merchantAddress - 商家地址
 * @returns {Promise<Array>} - 返回商品ID列表
 */
export async function getMerchantProducts({ merchantAddress }) {
  try {
    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();

    const productIds = await readContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'getMerchantProducts',
      args: [merchantAddress],
    });

    return productIds.map(id => Number(id));
  } catch (error) {
    console.error('🚨 [getMerchantProducts] 获取商家商品失败:', error);
    throw new Error(`获取商家商品失败: ${error.message}`);
  }
}

/**
 * 创建商品
 * @param {Object} params - 参数对象
 * @param {string} params.name - 商品名称
 * @param {string} params.description - 商品描述
 * @param {Array} params.images - 商品图片哈希数组
 * @param {number} params.price - 商品价格
 * @param {number} params.stock - 商品库存
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
/**
 * 检查商品是否应该在商城中显示（排除黑名单商家的商品）
 * @param {Object} product - 商品信息
 * @returns {Promise<boolean>} - 是否应该显示
 */
export async function shouldDisplayProduct(product) {
  try {
    if (!product || !product.merchant) {
      return false;
    }

    // 检查商家是否在黑名单中
    const { isMerchantBlacklisted } = await import('@/apis/adminApi');
    const isBlacklisted = await isMerchantBlacklisted({ merchantAddress: product.merchant });

    // 只有非黑名单商家的激活商品才显示
    return product.isActive && !isBlacklisted;
  } catch (error) {
    console.error('❌ [shouldDisplayProduct] 检查商品显示状态失败:', error);
    // 出错时默认显示，避免影响正常商品
    return product.isActive;
  }
}

export async function createProduct({ name, description, images, price, stock, signer }) {
  try {


    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();

    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }

    // 获取当前连接的账户地址
    const { getAccount } = await import('wagmi/actions');
    const currentAccount = getAccount(config);

    // 检查用户是否已注册代理系统
    const { checkUserRegistered } = await import('./agentSystemApi');
    const isRegistered = await checkUserRegistered({ userAddress: signerAddress });

    if (!isRegistered) {
      throw new Error('请先注册代理系统才能创建商品！');
    }

    // 检查商家状态（调用 MerchantManagement 合约）
    const { contractAddress: mmAddress, contractABI: mmABI } = await getContractInfo('MerchantManagement');

    const isMerchantResult = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'isMerchant',
      args: [signerAddress],
    });

    const currentAccountMerchantStatus = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'isMerchant',
      args: [currentAccount.address],
    });

    // 检查ProductManagement合约中配置的MerchantManagement地址
    const { contractAddress: pmAddress, contractABI: pmABI } = await getContractInfo('ProductManagement');
    const configuredMerchantMgmt = await readContract(config, {
      address: pmAddress,
      abi: pmABI,
      functionName: 'merchantManagement',
    });

    // 检查合约是否暂停
    try {
      const isPaused = await readContract(config, {
        address: pmAddress,
        abi: pmABI,
        functionName: 'paused',
      });
      if (isPaused) {
        throw new Error('合约当前已暂停，无法创建商品');
      }
    } catch (error) {
      // 如果无法检查暂停状态，继续执行
    }

    if (!isMerchantResult) {
      // 提供更详细的错误信息
      const errorMsg = `您不是认证商家，无法创建商品！
      商家状态: 已注册=${merchantStatus.isRegistered}, 已认证=${merchantStatus.isVerified}
      请先申请并通过商家认证。`;
      throw new Error(errorMsg);
    }

    // 参数验证和预处理
    const encodedName = name.trim();
    const encodedDescription = description.trim();

    // 验证参数
    if (!encodedName || encodedName.length === 0) {
      throw new Error('商品名称不能为空');
    }

    if (!encodedDescription || encodedDescription.length === 0) {
      throw new Error('商品描述不能为空');
    }

    if (!Array.isArray(images)) {
      throw new Error('图片必须是数组格式');
    }

    // 验证和优化图片URL长度
    const { validateAndOptimizeForContract } = await import('@/utils/imageUrlOptimizer');
    const optimizationResult = await validateAndOptimizeForContract(images);

    if (!optimizationResult.success) {
      throw new Error(`图片URL验证失败: ${optimizationResult.error}`);
    }

    // 使用优化后的图片URL
    const processedImages = optimizationResult.optimizedUrls;

    // 价格已经在前端转换过了，这里直接使用
    // 注意：price 参数已经是经过 parsePointsInput 处理的 BigInt 值
    const processedPrice = BigInt(price.toString());
    const processedStock = Number(stock);

    if (processedPrice <= 0n) {
      throw new Error('商品价格必须大于0');
    }

    // 验证价格是否超过合约限制
    // 合约限制：最大价格 1,000,000 积分 = 1,000,000 * 10^6
    const MAX_PRICE = 1000000n * (10n ** 6n); // 1万亿
    if (processedPrice > MAX_PRICE) {
      const { formatPoints } = await import('@/utils/pointsFormatter');
      const currentPriceFormatted = formatPoints(processedPrice);
      const maxPriceFormatted = formatPoints(MAX_PRICE);
      throw new Error(`商品价格过高！当前价格：${currentPriceFormatted} 积分，最大允许：${maxPriceFormatted} 积分`);
    }

    if (processedStock <= 0) {
      throw new Error('商品库存必须大于0');
    }



    // 在交易前最后一次验证商家状态
    const finalMerchantCheck = await readContract(config, {
      address: mmAddress,
      abi: mmABI,
      functionName: 'isMerchant',
      args: [currentAccount.address],
    });

    if (!finalMerchantCheck) {
      throw new Error(`最终商家状态检查失败！当前账户 ${currentAccount.address} 不是认证商家。`);
    }

    // 最后一次直接检查合约状态
    try {
      // 检查合约是否暂停
      const isPaused = await readContract(config, {
        address: contractAddress,
        abi: contractABI,
        functionName: 'paused',
      });

      if (isPaused) {
        throw new Error('ProductManagement 合约已暂停，无法创建商品');
      }

      // 再次检查商家状态（使用合约配置的地址）
      const contractMerchantStatus = await readContract(config, {
        address: configuredMerchantMgmt,
        abi: mmABI,
        functionName: 'isMerchant',
        args: [currentAccount.address],
      });

      if (!contractMerchantStatus) {
        throw new Error('合约配置的 MerchantManagement 中商家状态为 false');
      }

      // 检查黑名单状态
      const isBlacklisted = await readContract(config, {
        address: configuredMerchantMgmt,
        abi: mmABI,
        functionName: 'blacklist',
        args: [currentAccount.address],
      });

      if (isBlacklisted) {
        throw new Error('账户在黑名单中，无法创建商品');
      }

    } catch (preCheckError) {
      throw preCheckError;
    }

    // 使用当前连接的账户进行交易，而不是传入的signer地址
    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'createProduct',
      args: [encodedName, encodedDescription, processedImages, processedPrice, processedStock],
    });

    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });

    return { receipt, txHash };

  } catch (error) {
    throw new Error(`商品创建失败: ${error.message}`);
  }
}

/**
 * 购买商品（使用默认地址，更稳妥的方式）
 * @param {Object} params - 参数对象
 * @param {number} params.productId - 商品ID
 * @param {number} params.quantity - 购买数量
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function buyProductWithDefaultAddress({ productId, quantity, signer }) {
  try {
    console.log('🛒 [buyProductWithDefaultAddress] 开始购买商品流程（使用默认地址）');

    console.log('📋 [buyProductWithDefaultAddress] 步骤1: 获取合约信息');
    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();
    console.log('✅ [buyProductWithDefaultAddress] 合约信息获取成功:', { contractAddress });

    console.log('📋 [buyProductWithDefaultAddress] 步骤2: 验证签名者地址');
    const signerAddress = signer?.address || signer?.account?.address;
    if (!signerAddress) {
      throw new Error('无效的签名者对象');
    }
    console.log('✅ [buyProductWithDefaultAddress] 签名者地址验证成功:', signerAddress);

    console.log('📋 [buyProductWithDefaultAddress] 步骤3: 检查用户注册状态');
    const isRegistered = await checkUserRegistered({ userAddress: signerAddress });
    if (!isRegistered) {
      throw new Error('用户未注册，请先注册代理系统');
    }
    console.log('✅ [buyProductWithDefaultAddress] 用户注册状态检查通过');

    console.log('📋 [buyProductWithDefaultAddress] 步骤4: 发送交易');

    // 确保参数类型正确
    const finalProductId = BigInt(productId);
    const finalQuantity = BigInt(quantity);

    console.log('🔧 [buyProductWithDefaultAddress] 交易参数:', {
      address: contractAddress,
      functionName: 'buyProductWithDefaultAddress',
      args: [finalProductId, finalQuantity],
      account: signerAddress,
    });

    console.log('🔍 [buyProductWithDefaultAddress] 参数类型检查:', {
      productId: { value: finalProductId.toString(), type: typeof finalProductId },
      quantity: { value: finalQuantity.toString(), type: typeof finalQuantity },
    });

    console.log('⏳ [buyProductWithDefaultAddress] 正在等待用户确认交易...');
    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'buyProductWithDefaultAddress',
      args: [finalProductId, finalQuantity],
      account: signerAddress,
    });
    console.log('✅ [buyProductWithDefaultAddress] 交易发送成功，哈希:', txHash);

    console.log('📋 [buyProductWithDefaultAddress] 步骤5: 等待交易确认');
    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });
    console.log('✅ [buyProductWithDefaultAddress] 交易确认成功，收据:', receipt);

    console.log('🎉 [buyProductWithDefaultAddress] 购买流程完成');
    return { receipt, txHash };

  } catch (error) {
    console.error('🚨 [buyProductWithDefaultAddress] 商品购买失败:', error);
    throw new Error(`商品购买失败: ${error.message}`);
  }
}

/**
 * 购买商品（原有函数，指定地址ID）
 * @param {Object} params - 参数对象
 * @param {number} params.productId - 商品ID
 * @param {number} params.quantity - 购买数量
 * @param {number} params.addressId - 收货地址ID
 * @param {Object} params.signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 */
export async function buyProduct({ productId, quantity, addressId, signer }) {
  try {
    console.log('🛒 [buyProduct] 开始购买商品流程');

    console.log('📋 [buyProduct] 步骤1: 获取合约信息');
    const { contractAddress, contractABI } = await getContractInfo('ProductManagement');
    const config = await getWagmiConfig();
    console.log('✅ [buyProduct] 合约信息获取成功:', { contractAddress });

    console.log('📋 [buyProduct] 步骤2: 验证签名者地址');
    const signerAddress = signer.account?.address || signer.address;
    if (!signerAddress) {
      throw new Error('无法获取签名者地址');
    }
    console.log('✅ [buyProduct] 签名者地址验证成功:', signerAddress);

    console.log('📋 [buyProduct] 步骤3: 检查用户注册状态');
    // 检查用户是否已注册代理系统
    const { checkUserRegistered } = await import('./agentSystemApi');
    const isRegistered = await checkUserRegistered({ userAddress: signerAddress });

    if (!isRegistered) {
      throw new Error('请先注册代理系统才能购买商品！');
    }
    console.log('✅ [buyProduct] 用户注册状态检查通过');

    console.log('📋 [buyProduct] 步骤4: 发送交易');

    // 确保参数类型正确
    const finalProductId = Number(productId);
    const finalQuantity = Number(quantity);
    const finalAddressId = Number(addressId);

    console.log('🔧 [buyProduct] 交易参数:', {
      address: contractAddress,
      functionName: 'buyProduct',
      args: [finalProductId, finalQuantity, finalAddressId],
      account: signerAddress,
    });

    console.log('🔍 [buyProduct] 参数类型检查:', {
      productId: { value: finalProductId, type: typeof finalProductId },
      quantity: { value: finalQuantity, type: typeof finalQuantity },
      addressId: { value: finalAddressId, type: typeof finalAddressId },
    });

    console.log('⏳ [buyProduct] 正在等待用户确认交易...');
    const txHash = await writeContract(config, {
      address: contractAddress,
      abi: contractABI,
      functionName: 'buyProduct',
      args: [finalProductId, finalQuantity, finalAddressId],
      account: signerAddress,
    });
    console.log('✅ [buyProduct] 交易发送成功，哈希:', txHash);

    console.log('📋 [buyProduct] 步骤5: 等待交易确认');
    const receipt = await waitForTransactionReceipt(config, {
      hash: txHash,
      timeout: 60000,
    });
    console.log('✅ [buyProduct] 交易确认成功，收据:', receipt);

    console.log('🎉 [buyProduct] 购买流程完成');
    return { receipt, txHash };

  } catch (error) {
    console.error('🚨 [buyProduct] 商品购买失败:', error);
    throw new Error(`商品购买失败: ${error.message}`);
  }
}
