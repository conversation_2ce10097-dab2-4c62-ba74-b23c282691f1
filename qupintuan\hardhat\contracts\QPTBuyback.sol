// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol";
import "./StorageValidator.sol";

interface IAgentSystem {
    function getLevel(address user) external view returns (uint8);
    function getUserInfo(address user) external view returns (
        address inviter,
        uint8 level,
        uint256 totalPerformance,
        uint256 referralsCount,
        bool isRegistered,
        uint256 personalPerformance
    );
}

/**
 * @title QPTBuyback
 * @dev QPT回购合约 - 支持多档位QPT回购
 * @notice 功能特性：
 * 1. 支持100/200/500/1000 QPT四个档位
 * 2. 不同档位对应不同的补贴数量
 * 3. 支持 refundExpired 函数处理满员但过期未开奖房间的退款
 * 4. 优化的退款逻辑，确保所有情况都能正确退款
 * 5. 智能退款状态检查功能
 * 6. 完全向后兼容的存储布局
 */
contract QPTBuyback is
    Initializable,
    StorageValidator,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using SafeERC20Upgradeable for IERC20Upgradeable;

    // QPT档位枚举
    enum QPTTier {
        TIER_100,   // 100 QPT
        TIER_200,   // 200 QPT
        TIER_500,   // 500 QPT
        TIER_1000   // 1000 QPT
    }

    // 档位配置结构体
    struct TierConfig {
        uint256 participantQPT;    // 参与所需QPT数量
        uint256 qptSubsidy;        // 非赢家补贴数量
    }

    // 常量定义
    uint256 public constant ROOM_PARTICIPANTS = 8;
    uint256 public constant ROOM_DURATION = 24 * 60 * 60; // 24小时

    // 向后兼容的常量（保留原有100 QPT档位的常量）
    uint256 public constant PARTICIPANT_QPT = 100 * 10**18; // 100 QPT
    uint256 public constant QPT_SUBSIDY = 5 * 10**18; // 5 QPT 补贴

    // 房间结构体（保持与原版本兼容的存储布局）
    struct GroupRoom {
        uint256 id;                    // 保持原有字段顺序
        address creator;
        address[] participants;
        mapping(address => bool) refunded;
        uint256 deadline;
        bool locked;
        uint256 closeBlock;
        address winner;                // 新增字段放在后面
        uint256 lockedBuybackAmount;   // 新增字段
        bool rewardClaimed;            // 新增字段
        QPTTier tier;                  // 新增：房间档位
        uint256 winnerReward;          // 新增：赢家可领取的奖励金额
        mapping(address => uint256) participantLocked; // 新增：记录每个参与者在此房间的锁仓数量

        // 两步开奖相关字段
        bool readyForWinner;           // 房间是否准备设置赢家
        bytes32 lotteryTxHash;         // 开奖交易哈希
        uint256 lotteryTimestamp;      // 开奖时间戳
        uint256 lastActionTime;        // 最后操作时间

        // 新增：房间创建时间（与GroupBuyRoom保持一致）
        uint256 createTime;            // 房间真实创建时间戳
    }

    // QPT锁仓记录结构体
    struct UserLockInfo {
        uint256 totalLocked;           // 用户总锁仓数量
        uint256[] roomIds;             // 参与的房间ID列表
        mapping(uint256 => uint256) roomLocked; // 每个房间的锁仓数量
    }

    // 状态变量
    mapping(uint256 => GroupRoom) public rooms;
    uint256 public roomCounter;
    uint256 public totalBuybackPool;

    // 档位配置映射
    mapping(QPTTier => TierConfig) public tierConfigs;

    // 合约引用
    IAgentSystem public agentSystem;
    IERC20Upgradeable public usdtToken;
    IERC20Upgradeable public qptToken;                  // QPT代币合约
    address public timelock;
    address public adminAddress;

    // QPT锁仓相关状态变量
    mapping(address => UserLockInfo) public userLocks;  // 用户锁仓信息
    uint256 public totalLockedQPT;                      // 总锁仓QPT数量

    // 事件定义
    event RoomCreated(uint256 indexed roomId, address indexed creator, uint256 deadline, QPTTier tier);
    event RoomJoined(uint256 indexed roomId, address indexed participant);
    event RoomFinalized(uint256 indexed roomId, address indexed winner, uint8 winnerIndex);
    event RoomExpired(uint256 indexed roomId);
    event RewardClaimed(uint256 indexed roomId, address indexed winner, uint256 reward, uint8 level, uint256 percent);
    event Refunded(address indexed user, uint256 amount);
    event RefundWithSubsidy(address indexed user, uint256 principal, uint256 subsidy);
    event RefundExpired(address indexed user, uint256 amount); // 新增：过期退款事件
    event BuybackPoolUpdated(uint256 newTotal, uint256 amount, bool isAdd);
    event BNBReceived(address indexed sender, uint256 amount);
    event BNBWithdrawn(address indexed to, uint256 amount);
    event EmergencyUSDTWithdrawn(address indexed to, uint256 amount);
    event TierConfigUpdated(QPTTier tier, uint256 participantQPT, uint256 qptSubsidy);
    event RoomLockedAmountSet(uint256 indexed roomId, uint256 amount);
    event BuybackPoolSynced(uint256 oldAmount, uint256 newAmount);
    event RoomRemainingRecovered(uint256 indexed roomId, uint256 amount);
    event WinnerRewardCalculated(uint256 indexed roomId, address indexed winner, uint256 rewardAmount, uint8 level, uint256 percent);

    // QPT锁仓相关事件
    event QPTLocked(address indexed user, uint256 indexed roomId, uint256 amount);
    event QPTUnlocked(address indexed user, uint256 indexed roomId, uint256 amount);
    event SubsidyTransferred(address indexed user, uint256 amount);
    event SubsidyPoolUpdated(uint256 newTotal, uint256 amount, bool isAdd);
    event EmergencyQPTWithdrawn(address indexed to, uint256 amount);

    // 两步开奖相关事件
    event RoomReadyForWinner(uint256 indexed roomId, uint256 timestamp);
    event WinnerSet(uint256 indexed roomId, address indexed winner, bytes32 lotteryTxHash, uint256 lotteryTimestamp);

    // 修饰符
    modifier onlyWhenFinalizedOrExpired(uint256 roomId) {
        GroupRoom storage room = rooms[roomId];
        require(
            room.winner != address(0) || // 已开奖
            block.timestamp > room.deadline || // 已过期
            !room.locked, // 已处理过期
            "Room not finalized or expired"
        );
        _;
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address _qptToken,
        address _agentSystem,
        address _usdtToken,
        address _timelock,
        address _adminAddress
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        qptToken = IERC20Upgradeable(_qptToken);
        agentSystem = IAgentSystem(_agentSystem);
        usdtToken = IERC20Upgradeable(_usdtToken);
        timelock = _timelock;
        adminAddress = _adminAddress;
        roomCounter = 0;
        totalBuybackPool = 0;
        totalLockedQPT = 0;

        // 初始化档位配置
        _initializeTierConfigs();
    }

    /// @notice 初始化档位配置
    function _initializeTierConfigs() internal {
        // 100 QPT档位：参与100 QPT，补贴5 QPT
        tierConfigs[QPTTier.TIER_100] = TierConfig({
            participantQPT: 100 * 10**18,
            qptSubsidy: 5 * 10**18
        });

        // 200 QPT档位：参与200 QPT，补贴10 QPT
        tierConfigs[QPTTier.TIER_200] = TierConfig({
            participantQPT: 200 * 10**18,
            qptSubsidy: 10 * 10**18
        });

        // 500 QPT档位：参与500 QPT，补贴25 QPT
        tierConfigs[QPTTier.TIER_500] = TierConfig({
            participantQPT: 500 * 10**18,
            qptSubsidy: 25 * 10**18
        });

        // 1000 QPT档位：参与1000 QPT，补贴50 QPT
        tierConfigs[QPTTier.TIER_1000] = TierConfig({
            participantQPT: 1000 * 10**18,
            qptSubsidy: 50 * 10**18
        });
    }

    // 创建房间函数（向后兼容版本 - 默认100 QPT档位）
    function createRoom() external nonReentrant whenNotPaused {
        createRoom(QPTTier.TIER_100);
    }

    // 创建房间函数（支持档位参数）- 仅管理员可创建，不需要锁仓QPT
    function createRoom(QPTTier tier) public nonReentrant whenNotPaused {
        require(msg.sender == adminAddress || msg.sender == owner(), "Only admin can create rooms");

        roomCounter++;
        uint256 roomId = roomCounter;

        GroupRoom storage room = rooms[roomId];
        room.creator = msg.sender;
        room.createTime = block.timestamp;              // 🆕 记录真实创建时间
        room.deadline = block.timestamp + ROOM_DURATION;
        room.locked = false;
        room.closeBlock = 0;
        room.tier = tier;

        // 管理员创建房间时不自动加入参与者列表，也不锁定QPT
        // 房间创建后等待用户主动加入

        emit RoomCreated(roomId, msg.sender, room.deadline, tier);
    }

    // 加入房间函数（支持多档位）
    function joinRoom(uint256 roomId) external nonReentrant whenNotPaused {
        GroupRoom storage room = rooms[roomId];
        require(room.creator != address(0), "Room not exist");
        require(block.timestamp <= room.deadline, "Room expired");
        require(room.participants.length < ROOM_PARTICIPANTS, "Room full");
        require(!_isParticipant(roomId, msg.sender), "Already joined");

        TierConfig memory config = tierConfigs[room.tier];

        // 检查用户QPT余额
        require(qptToken.balanceOf(msg.sender) >= config.participantQPT, "Insufficient QPT balance");

        // 检查授权
        require(qptToken.allowance(msg.sender, address(this)) >= config.participantQPT, "Insufficient QPT allowance");

        // 锁仓QPT到本合约
        qptToken.safeTransferFrom(msg.sender, address(this), config.participantQPT);

        // 更新用户锁仓记录
        UserLockInfo storage userLock = userLocks[msg.sender];
        if (userLock.roomLocked[roomId] == 0) {
            userLock.roomIds.push(roomId);
        }
        userLock.roomLocked[roomId] += config.participantQPT;
        userLock.totalLocked += config.participantQPT;

        // 更新房间参与者锁仓记录
        room.participantLocked[msg.sender] = config.participantQPT;
        room.participants.push(msg.sender);

        // 更新全局锁仓统计
        totalLockedQPT += config.participantQPT;

        if (room.participants.length == ROOM_PARTICIPANTS) {
            room.locked = true;
            room.closeBlock = block.number;
            // 房间满员时不锁定金额，等待管理员开奖时锁定
        }

        emit RoomJoined(roomId, msg.sender);
        emit QPTLocked(msg.sender, roomId, config.participantQPT);
    }

    // ==================== 两步开奖逻辑 ====================

    /// @notice 第一步：房间创建者发起开奖，锁定回购池金额并准备设置赢家
    /// @param roomId 房间ID
    function startLottery(uint256 roomId) external nonReentrant whenNotPaused {
        GroupRoom storage room = rooms[roomId];
        require(msg.sender == room.creator, "Only creator can start lottery");
        require(room.participants.length == ROOM_PARTICIPANTS, "Room not full");
        require(room.locked, "Room not locked");
        require(room.winner == address(0), "Already finalized");
        require(!room.readyForWinner, "Lottery already started");
        require(block.timestamp <= room.deadline, "Expired");

        // 锁定可用回购池金额
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        uint256 totalPendingRewards = getTotalPendingRewards();

        // 可用回购池 = 合约总余额 - 待领取奖励
        uint256 availableAmount = contractBalance > totalPendingRewards ?
                                 contractBalance - totalPendingRewards : 0;

        require(availableAmount > 0, "No available funds for lottery");

        // 设置房间状态
        room.lockedBuybackAmount = availableAmount;
        room.readyForWinner = true;
        room.lastActionTime = block.timestamp;

        emit RoomReadyForWinner(roomId, block.timestamp);
    }

    /// @notice 第二步：房间创建者设置赢家，完成开奖
    /// @param roomId 房间ID
    /// @param winner 赢家地址
    /// @param lotteryTxHash 第一步开奖的交易哈希
    /// @param lotteryTimestamp 开奖时间戳
    function setWinner(
        uint256 roomId,
        address winner,
        bytes32 lotteryTxHash,
        uint256 lotteryTimestamp
    ) external nonReentrant whenNotPaused {
        GroupRoom storage room = rooms[roomId];
        require(msg.sender == room.creator, "Only creator can set winner");
        require(room.readyForWinner, "Room not ready for winner");
        require(room.winner == address(0), "Winner already set");
        require(room.locked, "Room not locked");

        // 验证赢家是有效参与者
        require(_isValidParticipant(roomId, winner), "Invalid winner");

        // 验证时间戳合理性
        require(lotteryTimestamp >= room.lastActionTime, "Timestamp too early");
        require(lotteryTimestamp <= block.timestamp, "Future timestamp");
        require(lotteryTxHash != bytes32(0), "Invalid transaction hash");

        // 设置赢家信息
        room.winner = winner;
        room.lotteryTxHash = lotteryTxHash;
        room.lotteryTimestamp = lotteryTimestamp;

        // 找到赢家在参与者列表中的索引
        uint8 winnerIndex = 0;
        for (uint8 i = 0; i < room.participants.length; i++) {
            if (room.participants[i] == winner) {
                winnerIndex = i;
                break;
            }
        }

        // 计算奖励并分离资金
        uint8 level = agentSystem.getLevel(room.winner);
        uint256 percent = 5 + level * 5; // 5%-25%
        uint256 rewardAmount = room.lockedBuybackAmount * percent / 100;
        uint256 remainingAmount = room.lockedBuybackAmount - rewardAmount;

        // 设置赢家可领取的奖励金额
        room.winnerReward = rewardAmount;

        emit WinnerSet(roomId, winner, lotteryTxHash, lotteryTimestamp);
        emit RoomFinalized(roomId, room.winner, winnerIndex);
        emit WinnerRewardCalculated(roomId, room.winner, rewardAmount, level, percent);
        emit BuybackPoolUpdated(totalBuybackPool, remainingAmount, true);
    }



    // 处理过期房间函数（保持不变）
    function finalizeTimeout(uint256 roomId) external nonReentrant whenNotPaused {
        GroupRoom storage room = rooms[roomId];
        require(room.creator != address(0), "Room not exist");
        require(block.timestamp > room.deadline, "Not expired");
        require(room.locked, "Not locked");
        
        room.locked = false;
        emit RoomExpired(roomId);
    }

    /// @notice 参与者退款（房间未满时）
    function refundFailed(uint256 roomId) external nonReentrant whenNotPaused onlyWhenFinalizedOrExpired(roomId) {
        require(_isParticipant(roomId, msg.sender), "Not a participant");
        GroupRoom storage room = rooms[roomId];
        require(room.participants.length < ROOM_PARTICIPANTS, "Room success");
        require(!room.refunded[msg.sender], "Already refunded");

        // 验证用户在此房间的锁仓数量
        uint256 lockedAmount = room.participantLocked[msg.sender];
        require(lockedAmount > 0, "No locked QPT in this room");

        room.refunded[msg.sender] = true;

        // 解锁QPT
        _unlockQPT(msg.sender, roomId, lockedAmount);

        emit Refunded(msg.sender, lockedAmount);
    }

    /// @notice 参与者退款（房间已满且有赢家时）
    function refundSuccess(uint256 roomId) external nonReentrant whenNotPaused onlyWhenFinalizedOrExpired(roomId) {
        require(_isParticipant(roomId, msg.sender), "Not a participant");
        GroupRoom storage room = rooms[roomId];
        require(room.participants.length == ROOM_PARTICIPANTS, "Room not full");
        require(msg.sender != room.winner, "Winner cannot refund");
        require(!room.refunded[msg.sender], "Already refunded");

        // 验证用户在此房间的锁仓数量
        uint256 lockedAmount = room.participantLocked[msg.sender];
        require(lockedAmount > 0, "No locked QPT in this room");

        TierConfig memory config = tierConfigs[room.tier];
        room.refunded[msg.sender] = true;

        // 解锁QPT
        _unlockQPT(msg.sender, roomId, lockedAmount);

        // 发放补贴
        _transferSubsidy(msg.sender, config.qptSubsidy);

        emit RefundWithSubsidy(msg.sender, lockedAmount, config.qptSubsidy);
    }

    /// @notice 新增：处理满员但过期未开奖房间的退款
    /// @param roomId 要退款的房间 ID
    /// @dev 修复满员过期未开奖房间无法退款的问题
    function refundExpired(uint256 roomId) external nonReentrant whenNotPaused {
        require(_isParticipant(roomId, msg.sender), "Not a participant");
        GroupRoom storage room = rooms[roomId];

        // 检查房间状态：满员、过期、未开奖、已处理过期
        require(room.participants.length == ROOM_PARTICIPANTS, "Room not full");
        require(block.timestamp > room.deadline, "Room not expired");
        require(room.winner == address(0), "Room already finalized");
        require(!room.locked, "Room not processed for expiry");
        require(!room.refunded[msg.sender], "Already refunded");

        // 验证用户在此房间的锁仓数量
        uint256 lockedAmount = room.participantLocked[msg.sender];
        require(lockedAmount > 0, "No locked QPT in this room");

        room.refunded[msg.sender] = true;

        // 满员过期未开奖：只退本金，不给补贴
        _unlockQPT(msg.sender, roomId, lockedAmount);

        emit RefundExpired(msg.sender, lockedAmount);
    }

    /// @dev 判断 address 是否在 participants 列表中
    function _isParticipant(uint256 roomId, address user) internal view returns (bool) {
        address[] storage ps = rooms[roomId].participants;
        for (uint i = 0; i < ps.length; i++) {
            if (ps[i] == user) {
                return true;
            }
        }
        return false;
    }

    /// @dev 验证用户是否为有效参与者（参与了房间且锁仓了QPT）
    function _isValidParticipant(uint256 roomId, address user) internal view returns (bool) {
        GroupRoom storage room = rooms[roomId];

        // 检查是否在参与者列表中
        bool isInParticipants = false;
        for (uint i = 0; i < room.participants.length; i++) {
            if (room.participants[i] == user) {
                isInParticipants = true;
                break;
            }
        }

        if (!isInParticipants) {
            return false;
        }

        // 检查是否锁仓了QPT
        return room.participantLocked[user] > 0;
    }

    /// @notice 获取房间信息
    function getRoomInfo(uint256 roomId) external view returns (
        address creator,
        address[] memory participants,
        address winner,
        uint256 lockedBuybackAmount,
        uint256 deadline,
        bool locked
    ) {
        GroupRoom storage room = rooms[roomId];
        return (
            room.creator,
            room.participants,
            room.winner,
            room.lockedBuybackAmount,
            room.deadline,
            room.locked
        );
    }

    /// @notice 获取房间完整信息（包含奖励金额）
    function getRoomInfoComplete(uint256 roomId) external view returns (
        address creator,
        address[] memory participants,
        address winner,
        uint256 lockedBuybackAmount,
        uint256 winnerReward,
        uint256 deadline,
        bool locked,
        bool rewardClaimed
    ) {
        GroupRoom storage room = rooms[roomId];
        return (
            room.creator,
            room.participants,
            room.winner,
            room.lockedBuybackAmount,
            room.winnerReward,
            room.deadline,
            room.locked,
            room.rewardClaimed
        );
    }

    /// @notice 获取详细的余额信息
    function getBalanceDetails() external view returns (
        uint256 totalContractBalance,    // 合约总USDT余额
        uint256 availableBuybackPool     // 可用回购池余额（总余额 - 待领取奖励）
    ) {
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        uint256 totalPendingRewards = getTotalPendingRewards();

        // 可用回购池 = 合约总余额 - 待领取奖励
        uint256 available = contractBalance > totalPendingRewards ?
                           contractBalance - totalPendingRewards : 0;

        return (
            contractBalance,
            available
        );
    }

    /// @notice 获取所有待领取奖励的总额
    function getTotalPendingRewards() public view returns (uint256 total) {
        for (uint256 i = 1; i <= roomCounter; i++) {
            GroupRoom storage room = rooms[i];
            if (room.winner != address(0) && !room.rewardClaimed && room.winnerReward > 0) {
                total += room.winnerReward;
            }
        }
        return total;
    }



    /// @notice 获取房间信息（包含档位）
    function getRoomInfoWithTier(uint256 roomId) external view returns (
        address creator,
        address[] memory participants,
        address winner,
        uint256 lockedBuybackAmount,
        uint256 deadline,
        bool locked,
        QPTTier tier,
        uint256 participantQPT,
        uint256 qptSubsidy
    ) {
        GroupRoom storage room = rooms[roomId];
        TierConfig memory config = tierConfigs[room.tier];
        return (
            room.creator,
            room.participants,
            room.winner,
            room.lockedBuybackAmount,
            room.deadline,
            room.locked,
            room.tier,
            config.participantQPT,
            config.qptSubsidy
        );
    }

    /// @notice 获取房间的开奖状态信息 - 与GroupBuyRoom保持一致
    function getRoomFinalizeStatus(uint256 roomId) external view returns (
        uint256 deadline,
        bool canFinalize,
        string memory reason
    ) {
        GroupRoom storage room = rooms[roomId];
        deadline = room.deadline;

        if (room.participants.length != ROOM_PARTICIPANTS) {
            return (deadline, false, "Room not full");
        }

        if (!room.locked) {
            return (deadline, false, "Room not locked");
        }

        if (room.winner != address(0)) {
            return (deadline, false, "Already finalized");
        }

        if (block.timestamp > room.deadline) {
            return (deadline, false, "Room expired");
        }

        bytes32 seed1 = blockhash(block.number - 1);
        bytes32 seed2 = blockhash(block.number - 2);
        if (seed1 == bytes32(0) || seed2 == bytes32(0)) {
            return (deadline, false, "Seed blocks unavailable");
        }

        return (deadline, true, "");
    }

    /// @notice 检查用户是否可以退款以及应该使用哪个退款函数
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @return canRefund 是否可以退款
    /// @return refundType 退款类型：0=不能退款, 1=refundFailed, 2=refundSuccess, 3=refundExpired
    /// @return reason 说明
    function getRefundStatus(uint256 roomId, address user) external view returns (
        bool canRefund,
        uint8 refundType,
        string memory reason
    ) {
        GroupRoom storage room = rooms[roomId];

        // 基本检查
        if (!_isParticipant(roomId, user)) {
            return (false, 0, "Not a participant");
        }

        if (room.refunded[user]) {
            return (false, 0, "Already refunded");
        }

        // 检查房间状态
        bool isExpired = block.timestamp > room.deadline;
        bool isFull = room.participants.length == ROOM_PARTICIPANTS;
        bool hasWinner = room.winner != address(0);
        bool isProcessedExpiry = !room.locked;

        // 情况1：房间未满且过期
        if (!isFull && isExpired) {
            return (true, 1, "Use refundFailed - room not full");
        }

        // 情况2：房间满员且有赢家（用户不是赢家）
        if (isFull && hasWinner && user != room.winner) {
            return (true, 2, "Use refundSuccess - room finalized, not winner");
        }

        // 情况3：房间满员、过期、无赢家、已处理过期
        if (isFull && isExpired && !hasWinner && isProcessedExpiry) {
            return (true, 3, "Use refundExpired - room expired without winner");
        }

        // 其他情况不能退款
        if (user == room.winner) {
            return (false, 0, "Winner cannot refund");
        }

        if (isFull && !isExpired && !hasWinner) {
            return (false, 0, "Room full but not expired or finalized");
        }

        if (isFull && isExpired && !hasWinner && room.locked) {
            return (false, 0, "Room expired but not processed yet");
        }

        return (false, 0, "Room still active");
    }

    /// @notice 设置管理员地址
    function setAdmin(address _admin) external onlyOwner {
        adminAddress = _admin;
    }

    /// @notice 更新档位配置（仅管理员）
    function updateTierConfig(
        QPTTier tier,
        uint256 participantQPT,
        uint256 qptSubsidy
    ) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");

        tierConfigs[tier] = TierConfig({
            participantQPT: participantQPT,
            qptSubsidy: qptSubsidy
        });

        emit TierConfigUpdated(tier, participantQPT, qptSubsidy);
    }

    /// @notice 获取档位配置
    function getTierConfig(QPTTier tier) external view returns (
        uint256 participantQPT,
        uint256 qptSubsidy
    ) {
        TierConfig memory config = tierConfigs[tier];
        return (config.participantQPT, config.qptSubsidy);
    }

    /// @notice 管理员添加回购池资金
    /// @param amount USDT金额
    function addToBuybackPool(uint256 amount) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");
        require(amount > 0, "Amount must be positive");

        usdtToken.safeTransferFrom(msg.sender, address(this), amount);
        totalBuybackPool += amount;

        emit BuybackPoolUpdated(totalBuybackPool, amount, true);
    }

    /// @notice 管理员从回购池提取资金
    /// @param amount USDT金额
    function removeFromBuybackPool(uint256 amount) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");
        require(amount > 0, "Amount must be positive");
        require(amount <= totalBuybackPool, "Insufficient pool balance");

        totalBuybackPool -= amount;
        usdtToken.safeTransfer(msg.sender, amount);

        emit BuybackPoolUpdated(totalBuybackPool, amount, false);
    }

    /// @notice 管理员手动为房间设置锁定金额（用于修复历史房间）
    /// @param roomId 房间ID
    /// @param amount 锁定金额
    function setRoomLockedAmount(uint256 roomId, uint256 amount) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");
        GroupRoom storage room = rooms[roomId];
        require(room.creator != address(0), "Room not exist");
        require(room.winner != address(0), "Room not finalized");
        require(amount <= totalBuybackPool, "Insufficient pool balance");

        room.lockedBuybackAmount = amount;

        // 从回购池中减去锁定的金额
        totalBuybackPool -= amount;

        // 立即计算并分离奖励，剩余金额返回回购池
        uint8 level = agentSystem.getLevel(room.winner);
        uint256 percent = 5 + level * 5; // 5%-25%
        uint256 rewardAmount = amount * percent / 100;
        uint256 remainingAmount = amount - rewardAmount;

        room.winnerReward = rewardAmount;
        totalBuybackPool += remainingAmount;

        emit RoomLockedAmountSet(roomId, amount);
        emit WinnerRewardCalculated(roomId, room.winner, rewardAmount, level, percent);
        emit BuybackPoolUpdated(totalBuybackPool, remainingAmount, true);
    }

    /// @notice 管理员同步USDT余额到回购池（用于修复回购池记录）
    function syncBuybackPool() external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");

        uint256 contractBalance = usdtToken.balanceOf(address(this));
        uint256 oldPool = totalBuybackPool;
        totalBuybackPool = contractBalance;

        emit BuybackPoolSynced(oldPool, contractBalance);
    }

    /// @notice 管理员回收房间剩余金额到回购池（用于未领取奖励的房间）
    /// @param roomId 房间ID
    function recoverRoomRemainingAmount(uint256 roomId) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");
        GroupRoom storage room = rooms[roomId];
        require(room.winner != address(0), "Room not finalized");
        require(!room.rewardClaimed, "Reward already claimed");
        require(room.lockedBuybackAmount > 0, "No locked amount");

        uint8 level = agentSystem.getLevel(room.winner);
        uint256 percent = 5 + level * 5;
        uint256 reward = room.lockedBuybackAmount * percent / 100;
        uint256 remaining = room.lockedBuybackAmount - reward;

        // 标记为已处理，防止重复回收
        room.rewardClaimed = true;

        // 剩余金额返回回购池
        totalBuybackPool += remaining;

        emit RoomRemainingRecovered(roomId, remaining);
        emit BuybackPoolUpdated(totalBuybackPool, remaining, true);
    }

    /// @notice 获取回购池余额
    function getBuybackPoolBalance() external view returns (uint256) {
        return totalBuybackPool;
    }

    /// @notice 获取房间时间信息（与GroupBuyRoom保持一致）
    /// @param roomId 房间ID
    /// @return createTime 房间创建时间
    /// @return endTime 房间结束时间（deadline）
    /// @return currentTime 当前区块时间
    /// @return isExpired 是否已过期
    /// @return timeLeft 剩余时间（如果已过期则为0）
    function getRoomTimeInfo(uint256 roomId) external view returns (
        uint256 createTime,
        uint256 endTime,
        uint256 currentTime,
        bool isExpired,
        uint256 timeLeft
    ) {
        GroupRoom storage room = rooms[roomId];
        require(room.creator != address(0), "Room not exist");

        uint256 endTime_ = room.deadline;
        bool isExpired_ = block.timestamp > endTime_;

        return (
            room.createTime,
            endTime_,
            block.timestamp,
            isExpired_,
            isExpired_ ? 0 : endTime_ - block.timestamp
        );
    }

    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    fallback() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    function getBNBBalance() external view returns (uint256) {
        return address(this).balance;
    }

    function withdrawBNB(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount <= address(this).balance, "Insufficient balance");

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");

        emit BNBWithdrawn(to, amount);
    }

    function withdrawUSDT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= usdtToken.balanceOf(address(this)), "Insufficient USDT balance");

        usdtToken.safeTransfer(to, amount);
        emit EmergencyUSDTWithdrawn(to, amount);
    }

    /// @notice 赢家领取奖励
    /// @param roomId 要领取奖励的房间 ID
    function claimReward(uint256 roomId) external nonReentrant whenNotPaused {
        GroupRoom storage room = rooms[roomId];
        require(room.winner == msg.sender, "Not winner");
        require(!room.rewardClaimed, "Already claimed");
        require(room.winnerReward > 0, "No reward available");

        uint256 rewardAmount = room.winnerReward;
        room.rewardClaimed = true;

        // 转账预先计算好的奖励给赢家
        usdtToken.safeTransfer(msg.sender, rewardAmount);

        uint8 level = agentSystem.getLevel(msg.sender);
        uint256 percent = 5 + level * 5; // 用于事件记录
        emit RewardClaimed(roomId, msg.sender, rewardAmount, level, percent);
    }

    /// @notice 获取赢家奖励信息（预览）
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @return canClaim 是否可以领取
    /// @return level 代理等级
    /// @return percent 奖励百分比
    /// @return rewardAmount 奖励金额
    /// @return alreadyClaimed 是否已领取
    /// @return reason 不能领取的原因
    function getWinnerRewardInfo(uint256 roomId, address user) external view returns (
        bool canClaim,
        uint8 level,
        uint256 percent,
        uint256 rewardAmount,
        bool alreadyClaimed,
        string memory reason
    ) {
        GroupRoom storage room = rooms[roomId];

        // 获取用户代理等级
        level = agentSystem.getLevel(user);
        percent = 5 + level * 5;
        rewardAmount = room.lockedBuybackAmount * percent / 100;
        alreadyClaimed = room.rewardClaimed;

        // 检查是否可以领取
        if (room.winner == address(0)) {
            return (false, level, percent, rewardAmount, alreadyClaimed, "Room not finalized");
        }

        if (room.winner != user) {
            return (false, level, percent, 0, alreadyClaimed, "Not the winner");
        }

        if (alreadyClaimed) {
            return (false, level, percent, rewardAmount, alreadyClaimed, "Already claimed");
        }

        return (true, level, percent, rewardAmount, alreadyClaimed, "");
    }





    /// @notice 获取代理等级对应的奖励百分比
    /// @param level 代理等级
    /// @return percent 奖励百分比
    function getRewardPercentByLevel(uint8 level) external pure returns (uint256 percent) {
        require(level <= 4, "Invalid level");
        return 5 + level * 5;
    }

    /// @notice 批量获取用户在多个房间的奖励状态
    /// @param roomIds 房间ID数组
    /// @param user 用户地址
    /// @return canClaim 是否可以领取数组
    /// @return rewardAmounts 奖励金额数组
    /// @return alreadyClaimed 是否已领取数组
    function getBatchWinnerRewardInfo(uint256[] calldata roomIds, address user)
        external view returns (
            bool[] memory canClaim,
            uint256[] memory rewardAmounts,
            bool[] memory alreadyClaimed
        ) {
        uint256 length = roomIds.length;
        canClaim = new bool[](length);
        rewardAmounts = new uint256[](length);
        alreadyClaimed = new bool[](length);

        for (uint256 i = 0; i < length; i++) {
            GroupRoom storage room = rooms[roomIds[i]];

            if (room.winner == user && !room.rewardClaimed) {
                canClaim[i] = true;
                uint8 level = agentSystem.getLevel(user);
                uint256 percent = 5 + level * 5;
                rewardAmounts[i] = room.lockedBuybackAmount * percent / 100;
            }

            alreadyClaimed[i] = room.rewardClaimed;
        }
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证房间计数器合理性
        if (roomCounter > 1000000) return false;

        // 验证回购池余额合理性
        if (totalBuybackPool > 10000000 * 10**6) return false; // 1000万USDT上限

        // 验证关键地址
        if (address(usdtToken) == address(0)) return false;
        if (address(agentSystem) == address(0)) return false;
        if (address(qptToken) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            roomCounter,
            totalBuybackPool,
            address(usdtToken),
            address(agentSystem),
            address(qptToken),
            totalLockedQPT,
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // 重新计算回购池余额
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        if (totalBuybackPool != contractBalance) {
            totalBuybackPool = contractBalance;
        }

        emit StorageFixed(address(this), "QPTBuyback storage fixed");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    // ==================== 内部QPT管理函数 ====================

    /// @notice 内部函数：解锁用户在特定房间的QPT
    /// @param user 用户地址
    /// @param roomId 房间ID
    /// @param amount 解锁数量
    function _unlockQPT(address user, uint256 roomId, uint256 amount) internal {
        require(amount > 0, "Amount must be greater than 0");

        UserLockInfo storage userLock = userLocks[user];
        require(userLock.roomLocked[roomId] >= amount, "Insufficient locked amount in room");

        // 更新用户锁仓记录
        userLock.roomLocked[roomId] -= amount;
        userLock.totalLocked -= amount;

        // 更新房间参与者锁仓记录
        GroupRoom storage room = rooms[roomId];
        room.participantLocked[user] -= amount;

        // 更新全局锁仓统计
        totalLockedQPT -= amount;

        // 转账QPT给用户
        qptToken.safeTransfer(user, amount);

        emit QPTUnlocked(user, roomId, amount);
    }

    /// @notice 内部函数：发放补贴给用户
    /// @param user 用户地址
    /// @param amount 补贴数量
    function _transferSubsidy(address user, uint256 amount) internal {
        require(amount > 0, "Subsidy amount must be greater than 0");

        // 直接检查合约QPT余额是否足够支付补贴
        uint256 contractBalance = qptToken.balanceOf(address(this));
        require(contractBalance >= amount, "Insufficient contract QPT balance");

        // 直接转账QPT补贴给用户，不需要管理专门的补贴池
        qptToken.safeTransfer(user, amount);

        emit SubsidyTransferred(user, amount);
    }

    // ==================== 新增查询函数 ====================

    /// @notice 查询房间开奖信息
    /// @param roomId 房间ID
    /// @return readyForWinner 是否准备设置赢家
    /// @return winner 赢家地址
    /// @return lotteryTxHash 开奖交易哈希
    /// @return lotteryTimestamp 开奖时间戳
    /// @return winnerIndex 赢家索引
    function getLotteryInfo(uint256 roomId) external view returns (
        bool readyForWinner,
        address winner,
        bytes32 lotteryTxHash,
        uint256 lotteryTimestamp,
        uint8 winnerIndex
    ) {
        GroupRoom storage room = rooms[roomId];

        // 找到赢家索引
        winnerIndex = 0;
        if (room.winner != address(0)) {
            for (uint8 i = 0; i < room.participants.length; i++) {
                if (room.participants[i] == room.winner) {
                    winnerIndex = i;
                    break;
                }
            }
        }

        return (
            room.readyForWinner,
            room.winner,
            room.lotteryTxHash,
            room.lotteryTimestamp,
            winnerIndex
        );
    }

    /// @notice 查询用户在特定房间的锁仓数量
    /// @param user 用户地址
    /// @param roomId 房间ID
    /// @return 锁仓数量
    function getUserRoomLocked(address user, uint256 roomId) external view returns (uint256) {
        return userLocks[user].roomLocked[roomId];
    }



    /// @notice 查询用户参与的房间ID列表
    /// @param user 用户地址
    /// @return 房间ID数组
    function getUserRoomIds(address user) external view returns (uint256[] memory) {
        return userLocks[user].roomIds;
    }

    /// @notice 批量查询用户在多个房间的锁仓数量
    /// @param user 用户地址
    /// @param roomIds 房间ID数组
    /// @return amounts 对应的锁仓数量数组
    function getUserRoomLockedBatch(address user, uint256[] calldata roomIds)
        external view returns (uint256[] memory amounts) {
        amounts = new uint256[](roomIds.length);
        UserLockInfo storage userLock = userLocks[user];

        for (uint256 i = 0; i < roomIds.length; i++) {
            amounts[i] = userLock.roomLocked[roomIds[i]];
        }
    }

    // ==================== 管理员函数 ====================



    /// @notice 紧急提取QPT（仅所有者）
    /// @param to 接收地址
    /// @param amount 提取数量
    function emergencyWithdrawQPT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= qptToken.balanceOf(address(this)), "Insufficient QPT balance");

        qptToken.safeTransfer(to, amount);
        emit EmergencyQPTWithdrawn(to, amount);
    }

    /// @notice 设置QPT代币合约地址（仅所有者）
    /// @param _qptToken 新的QPT代币合约地址
    function setQPTToken(address _qptToken) external onlyOwner {
        require(_qptToken != address(0), "Zero address");
        qptToken = IERC20Upgradeable(_qptToken);
    }

    /// @notice 设置代理系统合约地址（仅所有者）
    /// @param _agentSystem 新的代理系统合约地址
    function setAgentSystem(address _agentSystem) external onlyOwner {
        require(_agentSystem != address(0), "Zero address");
        agentSystem = IAgentSystem(_agentSystem);
    }

    /// @notice 设置管理员地址（仅所有者）
    /// @param _adminAddress 新的管理员地址
    function setAdminAddress(address _adminAddress) external onlyOwner {
        require(_adminAddress != address(0), "Zero address");
        adminAddress = _adminAddress;
    }

    /// @notice 批量设置档位配置（仅管理员）
    /// @param tiers 档位数组
    /// @param participantQPTs 参与QPT数量数组
    /// @param qptSubsidies 补贴数量数组
    function batchUpdateTierConfigs(
        QPTTier[] calldata tiers,
        uint256[] calldata participantQPTs,
        uint256[] calldata qptSubsidies
    ) external {
        require(msg.sender == adminAddress || msg.sender == owner(), "Not authorized");
        require(tiers.length == participantQPTs.length && tiers.length == qptSubsidies.length, "Array length mismatch");

        for (uint256 i = 0; i < tiers.length; i++) {

            tierConfigs[tiers[i]] = TierConfig({
                participantQPT: participantQPTs[i],
                qptSubsidy: qptSubsidies[i]
            });

            emit TierConfigUpdated(tiers[i], participantQPTs[i], qptSubsidies[i]);
        }
    }

    /// @notice 获取合约QPT余额信息
    /// @return contractBalance 合约QPT总余额
    /// @return totalLocked 总锁仓数量
    /// @return subsidyPool 补贴池数量
    /// @return available 可用余额（合约余额 - 锁仓 - 补贴池）
    function getQPTBalanceInfo() external view returns (
        uint256 contractBalance,
        uint256 totalLocked,
        uint256 subsidyPool,
        uint256 available
    ) {
        contractBalance = qptToken.balanceOf(address(this));
        totalLocked = totalLockedQPT;
        subsidyPool = 0; // 不再使用专门的补贴池，设为0保持接口兼容性
        available = contractBalance >= totalLocked ? contractBalance - totalLocked : 0;
    }






}
