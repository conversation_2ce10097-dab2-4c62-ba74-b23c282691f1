/* 商品管理组件样式 */
.product-management {
  padding: 24px;
}

/* 模块头部 */
.module-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.module-header h3 {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.module-header p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

/* 功能导航 */
.product-nav {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  background: #f8fafc;
  padding: 4px;
  border-radius: 8px;
}

.nav-btn {
  flex: 1;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.nav-btn.active {
  background: white;
  color: #667eea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 内容区域 */
.product-content {
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.content-section {
  padding: 40px 20px;
}

/* 开发中状态 */
.coming-soon {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.coming-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.coming-soon h4 {
  font-size: 20px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.coming-soon p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 24px 0;
}

.feature-preview {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  text-align: left;
}

.preview-item {
  font-size: 14px;
  color: #475569;
  margin-bottom: 8px;
  padding-left: 8px;
  position: relative;
}

.preview-item:last-child {
  margin-bottom: 0;
}

/* 功能说明 */
.feature-notice {
  margin-top: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.feature-notice h4 {
  font-size: 16px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notice-content p {
  font-size: 14px;
  color: #475569;
  margin: 0 0 16px 0;
}

.notice-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.notice-item {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.notice-item strong {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.notice-item span {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-management {
    padding: 16px;
  }

  .product-nav {
    flex-direction: column;
    gap: 4px;
  }

  .nav-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .content-section {
    padding: 24px 16px;
  }

  .coming-icon {
    font-size: 36px;
  }

  .coming-soon h4 {
    font-size: 18px;
  }

  .coming-soon p {
    font-size: 14px;
  }

  .feature-preview {
    padding: 16px;
  }

  .feature-notice {
    padding: 16px;
  }

  .notice-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .product-management {
    padding: 12px;
  }

  .nav-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .content-section {
    padding: 20px 12px;
  }

  .coming-icon {
    font-size: 32px;
  }

  .coming-soon h4 {
    font-size: 16px;
  }

  .feature-preview {
    padding: 12px;
  }

  .preview-item {
    font-size: 13px;
  }

  .feature-notice {
    padding: 12px;
  }

  .notice-item {
    padding: 12px;
  }

  .notice-item strong {
    font-size: 13px;
  }

  .notice-item span {
    font-size: 12px;
  }
}

/* 商品列表样式 */
.product-list-header {
  margin-bottom: 20px;
}

.product-list-header h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
}

.product-list-header p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-container h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
}

.empty-container p {
  margin: 0 0 20px 0;
  color: #6b7280;
  font-size: 14px;
}

.add-first-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  background: #10b981;
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-first-btn:hover {
  background: #059669;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.product-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.product-image {
  width: 100%;
  aspect-ratio: 1;
  margin-bottom: 12px;
  border-radius: 6px;
  overflow: hidden;
  background: #f3f4f6;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-name {
  margin: 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  margin-right: 12px;
}

.product-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
}

.product-status.active {
  background: #d1fae5;
  color: #065f46;
}

.product-status.inactive {
  background: #fee2e2;
  color: #991b1b;
}

.product-info {
  margin-bottom: 16px;
}

.product-description {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

.product-details {
  display: flex;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item .label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.product-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-btn.edit {
  background: #dbeafe;
  color: #1d4ed8;
}

.action-btn.edit:hover {
  background: #bfdbfe;
}

.action-btn.toggle {
  background: #fef3c7;
  color: #92400e;
}

.action-btn.toggle:hover {
  background: #fde68a;
}

.action-btn.delete {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.delete:hover {
  background: #fecaca;
}

.development-notice {
  background: #f8fafc;
  border: 2px dashed #cbd5e1;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.development-notice h4 {
  margin: 0 0 12px 0;
  color: #64748b;
  font-size: 16px;
}

.notice-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.notice-items .notice-item {
  font-size: 14px;
  color: #64748b;
  padding: 8px;
  background: white;
  border-radius: 6px;
}
