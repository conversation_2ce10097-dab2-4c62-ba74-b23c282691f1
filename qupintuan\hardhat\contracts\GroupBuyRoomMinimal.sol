// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

import "contracts/interfaces/IGroupBuyRoom.sol";
import "contracts/interfaces/IAgentSystem.sol";
import "contracts/interfaces/IQPTLock.sol";
import "contracts/interfaces/IPointsManagement.sol";
import "contracts/interfaces/IFeeSplitManager.sol";
import "./StorageValidator.sol";

enum RoomStatus { CREATED, FUNDED, EXPIRED, CLOSED }

contract GroupBuyRoomMinimal is Initializable, StorageValidator, OwnableUpgradeable, PausableUpgradeable, ReentrancyGuardUpgradeable, UUPSUpgradeable, IGroupBuyRoom {
    using SafeERC20Upgradeable for IERC20Upgradeable;

    uint256 public constant ROOM_DURATION = 24 hours;
    uint8   public constant MAX_PARTICIPANTS = 8;

    // 新增：支持的档位常量，便于前端查询
    uint256 public constant TIER_30 = 30e6;
    uint256 public constant TIER_50 = 50e6;
    uint256 public constant TIER_100 = 100e6;
    uint256 public constant TIER_200 = 200e6;
    uint256 public constant TIER_500 = 500e6;
    uint256 public constant TIER_1000 = 1000e6;

    struct Room {
        address    creator;
        uint256    tier;
        address[]  participants;
        uint256    createTime;
        bool       isClosed;
        bool       isSuccessful;
        uint8      winnerIndex;
        uint256    subsidyPer;
        uint256    creatorCommission;
        uint256    systemFee;
        bool       systemFeeDistributed;
    }

    IERC20Upgradeable                        public usdt;
    IAgentSystem                             public agentSystem;
    IQPTLock                                 public qptLocker;
    IFeeSplitManager                         public feeSplitManager;
    address                                  public nodeStakingAddress;
    address                                  public buybackAddress;
    address                                  public pointsManagementAddress;
    address                                  public platformAddress;
    address                                  public timelock;

    mapping(uint256 => Room)                 public rooms;
    mapping(uint256 => mapping(address => bool)) public hasJoined;
    mapping(uint256 => uint256)              public nonWinnerSubsidies;
    mapping(uint256 => uint256)              public tierPoints;
    mapping(uint256 => bool)                 public expireFailed;
    uint256                                  public nextRoomId;

    // 新增：用户USDT转账记录验证 - 记录每个用户在每个房间实际转入的USDT金额
    mapping(uint256 => mapping(address => uint256)) public userPaidAmount;

    // 新增：两步开奖相关映射
    mapping(uint256 => bool) public roomReadyForWinner;      // 房间是否准备设置赢家
    mapping(uint256 => address) public roomWinner;           // 房间赢家地址
    mapping(uint256 => bytes32) public roomLotteryTxHash;    // 房间开奖交易哈希
    mapping(uint256 => uint256) public roomLotteryTimestamp; // 房间开奖时间戳
    mapping(uint256 => uint256) public roomLastActionTime;   // 房间最后操作时间

    event RoomCreated(uint256 indexed roomId, address indexed creator, uint256 tier);
    event RoomJoined(uint256 indexed roomId, address indexed participant, uint256 tier);
    event RoomReadyForWinner(uint256 indexed roomId, uint256 timestamp);
    event WinnerSet(uint256 indexed roomId, address indexed winner, bytes32 lotteryTxHash, uint256 lotteryTimestamp);
    event RoomClosed(uint256 indexed roomId, address indexed winner);
    event RoomExpired(uint256 indexed roomId);
    event RoomStatusChanged(uint256 indexed roomId, RoomStatus status, uint256 timestamp);
    // 角色分离的领取事件
    event CreatorCommissionClaimed(uint256 indexed roomId, address indexed creator, uint256 amount);
    event ParticipantRefundClaimed(uint256 indexed roomId, address indexed participant, uint256 principal, uint256 subsidy);

    // 赢家奖励独立领取事件
    event WinnerQPTClaimed(uint256 indexed roomId, address indexed winner, uint256 qptAmount);
    event WinnerPointsClaimed(uint256 indexed roomId, address indexed winner, uint256 points);

    event ExpireFailed(uint256 indexed roomId);
    event SystemFeeDistributed(uint256 indexed roomId, uint256 amount);
    event BNBReceived(address indexed sender, uint256 amount);
    event EmergencyUSDTWithdrawn(address indexed to, uint256 amount);
    event QPTLockerSyncFailed(uint256 indexed roomId, string reason);
    event QPTLockerSyncSuccess(uint256 indexed roomId);

    modifier onlyValidAmount(uint256 amount) {
        require(amount > 0 && amount <= 1000e6, "Amount invalid");
        _;
    }

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    function initialize(
        address _usdt,
        address _qptLocker,
        address _agentSystem,
        address _timelock,
        address _feeSplitManager,
        address _nodeStaking,
        address _qptBuyback,
        address _pointsManagement,
        address _platformAddress
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(_usdt != address(0), "Invalid USDT");
        require(_qptLocker != address(0), "Invalid QPTLocker");
        require(_agentSystem != address(0), "Invalid AgentSystem");
        require(_timelock != address(0), "Invalid Timelock");
        require(_platformAddress != address(0), "Invalid Platform");

        usdt = IERC20Upgradeable(_usdt);
        qptLocker = IQPTLock(_qptLocker);
        agentSystem = IAgentSystem(_agentSystem);
        timelock = _timelock;
        platformAddress = _platformAddress;

        // 配置可选的外部合约地址（可以为零地址，稍后配置）
        if (_feeSplitManager != address(0)) {
            feeSplitManager = IFeeSplitManager(_feeSplitManager);
        }
        if (_nodeStaking != address(0)) {
            nodeStakingAddress = _nodeStaking;
        }
        if (_qptBuyback != address(0)) {
            buybackAddress = _qptBuyback;
        }
        if (_pointsManagement != address(0)) {
            pointsManagementAddress = _pointsManagement;
        }

        // 默认非赢家补贴
        nonWinnerSubsidies[TIER_30] = 0.9e6;
        nonWinnerSubsidies[TIER_50] = 1.5e6;
        nonWinnerSubsidies[TIER_100] = 4e6;
        nonWinnerSubsidies[TIER_200] = 8e6;
        nonWinnerSubsidies[TIER_500] = 25e6;
        nonWinnerSubsidies[TIER_1000] = 50e6;

        // 初始化 tierPoints 映射（考虑积分系统的6位小数精度）
        // 积分系统使用 6 位小数，所以需要乘以 10^6
        tierPoints[TIER_30]   = 30 * 10**6;   // 30 积分 = 30,000,000
        tierPoints[TIER_50]   = 50 * 10**6;   // 50 积分 = 50,000,000
        tierPoints[TIER_100]  = 100 * 10**6;  // 100 积分 = 100,000,000
        tierPoints[TIER_200]  = 200 * 10**6;  // 200 积分 = 200,000,000
        tierPoints[TIER_500]  = 500 * 10**6;  // 500 积分 = 500,000,000
        tierPoints[TIER_1000] = 1000 * 10**6; // 1000 积分 = 1,000,000,000
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证房间ID合理性
        if (nextRoomId > 1000000) return false;

        // 验证合约地址
        if (address(usdt) == address(0)) return false;
        if (address(qptLocker) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            nextRoomId,
            address(usdt),
            address(qptLocker),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // GroupBuyRoomMinimal的存储修复逻辑
        emit StorageFixed(address(this), "GroupBuyRoomMinimal storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }



    // 优化：合并地址设置函数，减少代码重复
    function setFeeSplitManager(address addr) external onlyOwner {
        require(addr != address(0), "Zero address not allowed");
        feeSplitManager = IFeeSplitManager(addr);
    }

    function setNodeStakingAddress(address addr) external onlyOwner {
        require(addr != address(0), "Zero address not allowed");
        nodeStakingAddress = addr;
    }

    function setBuybackAddress(address addr) external onlyOwner {
        require(addr != address(0), "Zero address not allowed");
        buybackAddress = addr;
    }

    function setPointsManagementAddress(address addr) external onlyOwner {
        require(addr != address(0), "Zero address not allowed");
        pointsManagementAddress = addr;
    }

    function setPlatformAddress(address addr) external onlyOwner {
        require(addr != address(0), "Zero address not allowed");
        platformAddress = addr;
    }

    function createRoom(uint256 tier) external onlyValidAmount(tier) {
        require(
            tier == TIER_30 || tier == TIER_50 || tier == TIER_100 ||
            tier == TIER_200 || tier == TIER_500 || tier == TIER_1000,
            "Invalid tier"
        );
        uint256 roomId = nextRoomId++;
        rooms[roomId] = Room({
            creator: msg.sender,
            tier: tier,
            participants: new address[](0),
            createTime: block.timestamp,
            isClosed: false,
            isSuccessful: false,
            winnerIndex: 0,
            subsidyPer: 0,
            creatorCommission: 0,
            systemFee: 0,
            systemFeeDistributed: false
        });
        emit RoomCreated(roomId, msg.sender, tier);
        emit RoomStatusChanged(roomId, RoomStatus.CREATED, block.timestamp);
    }

    function joinRoom(uint256 roomId) external nonReentrant {
        require(msg.sender == tx.origin, "Contracts not allowed");
        Room storage r = rooms[roomId];
        require(!r.isClosed, "Closed");
        require(r.participants.length < MAX_PARTICIPANTS, "Full");
        require(block.timestamp <= r.createTime + ROOM_DURATION, "Expired");
        require(!hasJoined[roomId][msg.sender], "Joined");

        (address creator, , , , ) = qptLocker.getRoomInfo(roomId);
        require(creator == r.creator, "Lock QPT first");

        hasJoined[roomId][msg.sender] = true;
        r.participants.push(msg.sender);
        emit RoomJoined(roomId, msg.sender, r.tier);

        usdt.safeTransferFrom(msg.sender, address(this), r.tier);

        // 记录用户实际转账金额，用于退款时验证
        userPaidAmount[roomId][msg.sender] = r.tier;

        if (r.participants.length == MAX_PARTICIPANTS) {
            emit RoomStatusChanged(roomId, RoomStatus.FUNDED, block.timestamp);
        }
    }

    function closeRoom(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(msg.sender == r.creator, "Only creator can close room");
        require(r.participants.length == MAX_PARTICIPANTS, "Not full");
        require(!r.isClosed, "Closed");
        require(r.createTime + ROOM_DURATION >= block.timestamp, "Expired");

        // 第一步：准备开奖，等待前端提交赢家
        r.isClosed = true;
        roomReadyForWinner[roomId] = true;
        roomLastActionTime[roomId] = block.timestamp;

        emit RoomReadyForWinner(roomId, block.timestamp);
        emit RoomStatusChanged(roomId, RoomStatus.CLOSED, block.timestamp);
    }

    function setWinner(
        uint256 roomId,
        address winner,
        bytes32 lotteryTxHash,
        uint256 lotteryTimestamp
    ) external nonReentrant {
        Room storage r = rooms[roomId];
        require(msg.sender == r.creator, "Only creator can set winner");
        require(roomReadyForWinner[roomId], "Room not ready for winner");
        require(roomWinner[roomId] == address(0), "Winner already set");
        require(r.isClosed, "Room not closed");

        // 验证赢家是有效参与者
        require(_isValidParticipant(roomId, winner), "Invalid winner");

        // 验证时间戳合理性
        require(lotteryTimestamp >= roomLastActionTime[roomId], "Timestamp too early");
        require(lotteryTimestamp <= block.timestamp, "Future timestamp");
        require(lotteryTxHash != bytes32(0), "Invalid transaction hash");

        // 设置赢家信息
        roomWinner[roomId] = winner;
        roomLotteryTxHash[roomId] = lotteryTxHash;
        roomLotteryTimestamp[roomId] = lotteryTimestamp;
        r.isSuccessful = true;

        // 找到赢家在参与者列表中的索引
        for (uint8 i = 0; i < r.participants.length; i++) {
            if (r.participants[i] == winner) {
                r.winnerIndex = i;
                break;
            }
        }

        emit WinnerSet(roomId, winner, lotteryTxHash, lotteryTimestamp);
        emit RoomClosed(roomId, winner);

        // 添加赢家业绩
        if (address(agentSystem) != address(0)) {
            agentSystem.addPerformance(winner, r.tier);
        }

        // 计算费用分配
        r.subsidyPer = nonWinnerSubsidies[r.tier];
        r.creatorCommission = _calculateCreatorReward(r.creator, r.tier);

        uint256 totalPool = r.tier * MAX_PARTICIPANTS;
        uint256 nonWinnerTotal = uint256(MAX_PARTICIPANTS - 1) * (r.tier + r.subsidyPer);
        r.systemFee = totalPool - nonWinnerTotal - r.creatorCommission;

        // 分配系统费用
        if (r.systemFee > 0 && address(feeSplitManager) != address(0)) {
            _distributeSystemFeeInternal(roomId);
        }

        // 同步QPT锁仓状态
        if (address(qptLocker) != address(0)) {
            try qptLocker.markRoomSuccess(roomId) {
                emit QPTLockerSyncSuccess(roomId);
            } catch Error(string memory reason) {
                emit QPTLockerSyncFailed(roomId, reason);
            } catch (bytes memory) {
                emit QPTLockerSyncFailed(roomId, "Unknown error");
            }
        }
    }

    function _isValidParticipant(uint256 roomId, address user) internal view returns (bool) {
        Room storage r = rooms[roomId];

        // 检查是否在参与者列表中
        for (uint i = 0; i < r.participants.length; i++) {
            if (r.participants[i] == user) {
                // 检查是否支付了USDT
                return userPaidAmount[roomId][user] >= r.tier;
            }
        }
        return false;
    }

    function expireRoom(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(!r.isClosed, "Closed");
        require(block.timestamp > r.createTime + ROOM_DURATION, "TooEarly");
        r.isClosed = true;
        r.isSuccessful = false;
        emit RoomStatusChanged(roomId, RoomStatus.EXPIRED, block.timestamp);
        emit RoomExpired(roomId);
        if (address(qptLocker) != address(0)) {
            try qptLocker.forceUnlock(roomId) {} catch { expireFailed[roomId] = true; }
        }
    }

    function _distributeSystemFeeInternal(uint256 roomId) internal {
        Room storage r = rooms[roomId];
        require(r.isSuccessful, "Not successful");
        require(!r.systemFeeDistributed, "Already distributed");
        require(r.systemFee > 0, "No fee to distribute");

        // 获取房间创建者的代理等级
        uint8 creatorLevel = 0;
        if (address(agentSystem) != address(0)) {
            try agentSystem.getLevel(r.creator) returns (uint8 level) {
                creatorLevel = level;
            } catch {
                // 如果获取失败，使用默认等级0
                creatorLevel = 0;
            }
        }

        // 使用新的分配函数，包含代理等级
        (uint256 pointsAmt, uint256 nodeAmt, uint256 buybackAmt, uint256 platformAmt) =
            feeSplitManager.calcSplitAmountsByTierAndLevel(r.tier, creatorLevel);

        // 分配固定金额给各个合约
        if (pointsAmt   > 0) usdt.safeTransfer(pointsManagementAddress, pointsAmt);
        if (nodeAmt     > 0) usdt.safeTransfer(nodeStakingAddress,     nodeAmt);
        if (buybackAmt  > 0) usdt.safeTransfer(buybackAddress,          buybackAmt);
        if (platformAmt > 0) usdt.safeTransfer(platformAddress,        platformAmt);

        r.systemFeeDistributed = true;
        emit SystemFeeDistributed(roomId, r.systemFee);
    }





    /// @notice 发起人领取佣金
    /// @param roomId 房间ID
    function claimCreatorCommission(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(r.isClosed, "Room not closed");
        require(r.isSuccessful, "Room not successful");
        require(msg.sender == r.creator, "Not creator");
        require(!creatorClaimed[roomId][msg.sender], "Creator commission already claimed");

        creatorClaimed[roomId][msg.sender] = true;

        uint256 commission = r.creatorCommission;
        require(commission > 0, "No commission to claim");

        usdt.safeTransfer(msg.sender, commission);
        emit CreatorCommissionClaimed(roomId, msg.sender, commission);
    }

    /// @notice 参与者领取退款（本金+补贴）
    /// @param roomId 房间ID
    function claimParticipantRefund(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(r.isClosed, "Room not closed");
        require(!participantClaimed[roomId][msg.sender], "Participant refund already claimed");

        // 检查用户是否是参与者
        bool isParticipantInMapping = hasJoined[roomId][msg.sender];
        bool isParticipantInArray = false;
        for (uint i = 0; i < r.participants.length; i++) {
            if (r.participants[i] == msg.sender) {
                isParticipantInArray = true;
                break;
            }
        }
        bool isActualParticipant = isParticipantInMapping || isParticipantInArray;
        require(isActualParticipant, "Not a participant");

        // 检查不是赢家
        if (r.isSuccessful && roomWinner[roomId] != address(0)) {
            require(roomWinner[roomId] != msg.sender, "Winner cannot claim participant refund");
        }

        // 验证USDT转账记录
        require(userPaidAmount[roomId][msg.sender] >= r.tier, "No USDT payment record");

        participantClaimed[roomId][msg.sender] = true;

        uint256 principal = r.tier;
        uint256 subsidy = 0;

        if (r.isSuccessful) {
            // 成功房间：退本金+补贴
            subsidy = r.subsidyPer;
            usdt.safeTransfer(msg.sender, principal);
            if (subsidy > 0) {
                usdt.safeTransfer(msg.sender, subsidy);
            }
        } else {
            // 失败房间：只退本金
            usdt.safeTransfer(msg.sender, principal);
        }

        emit ParticipantRefundClaimed(roomId, msg.sender, principal, subsidy);
    }



    /// @notice 赢家领取QPT奖励
    /// @param roomId 房间ID
    function claimWinnerQPT(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(r.isClosed, "Room not closed");
        require(r.isSuccessful, "Room not successful");
        require(roomWinner[roomId] == msg.sender, "Not the winner");
        require(!winnerQPTClaimed[roomId][msg.sender], "Winner QPT already claimed");

        // 验证USDT转账记录 - 赢家必须支付对应档位的USDT
        require(userPaidAmount[roomId][msg.sender] >= r.tier, "No USDT payment record");

        winnerQPTClaimed[roomId][msg.sender] = true;

        // 发放QPT奖励
        qptLocker.rewardWinner(roomId, msg.sender);

        emit WinnerQPTClaimed(roomId, msg.sender, 0); // QPT数量由QPTLocker管理
    }

    /// @notice 赢家领取积分奖励
    /// @param roomId 房间ID
    function claimWinnerPoints(uint256 roomId) external nonReentrant {
        Room storage r = rooms[roomId];
        require(r.isClosed, "Room not closed");
        require(r.isSuccessful, "Room not successful");
        require(roomWinner[roomId] == msg.sender, "Not the winner");
        require(!winnerPointsClaimed[roomId][msg.sender], "Winner points already claimed");

        // 验证USDT转账记录 - 赢家必须支付对应档位的USDT
        require(userPaidAmount[roomId][msg.sender] >= r.tier, "No USDT payment record");

        winnerPointsClaimed[roomId][msg.sender] = true;

        // 发放积分奖励
        uint256 points = tierPoints[r.tier];
        require(points > 0, "Tier not supported");
        IPointsManagement(pointsManagementAddress).generatePoints(
            msg.sender,
            points,
            "GroupBuyRoom",
            "GROUPBUY"
        );

        emit WinnerPointsClaimed(roomId, msg.sender, points);
    }

    function _calculateCreatorReward(address creator, uint256 tier) internal view returns (uint256) {
        if (address(agentSystem) == address(0)) return 0;
        try agentSystem.getLevel(creator) returns (uint8 level) {
            if (tier == TIER_30) { if (level == 0) return 1.5e6; if (level == 1) return 3e6; if (level == 2) return 4.5e6; if (level == 3) return 6e6; if (level == 4) return 7.5e6; }
            else if (tier == TIER_50) { if (level == 0) return 2.5e6; if (level == 1) return 5e6; if (level == 2) return 7.5e6; if (level == 3) return 10e6; if (level == 4) return 12.5e6; }
            else if (tier == TIER_100) { if (level == 0) return 5e6; if (level == 1) return 10e6; if (level == 2) return 15e6; if (level == 3) return 20e6; if (level == 4) return 25e6; }
            else if (tier == TIER_200) { if (level == 0) return 10e6; if (level == 1) return 20e6; if (level == 2) return 30e6; if (level == 3) return 40e6; if (level == 4) return 50e6; }
            else if (tier == TIER_500) { if (level == 0) return 25e6; if (level == 1) return 50e6; if (level == 2) return 75e6; if (level == 3) return 100e6; if (level == 4) return 125e6; }
            else if (tier == TIER_1000) { if (level == 0) return 50e6; if (level == 1) return 100e6; if (level == 2) return 150e6; if (level == 3) return 200e6; if (level == 4) return 250e6; }
        } catch {
            return 0;
        }
        return 0;
    }

    // 基础查询函数（保留）
    function totalRooms() external view returns (uint256) {
        return nextRoomId;
    }

    // 新增：获取所有支持的档位
    function getSupportedTiers() external pure returns (uint256[] memory) {
        uint256[] memory tiers = new uint256[](6);
        tiers[0] = TIER_30;
        tiers[1] = TIER_50;
        tiers[2] = TIER_100;
        tiers[3] = TIER_200;
        tiers[4] = TIER_500;
        tiers[5] = TIER_1000;
        return tiers;
    }





    function isGroupBuyRoom() external pure override returns (bool) {
        return true;
    }

    function getRoomTier(uint256 roomId) external view override returns (uint256) {
        return rooms[roomId].tier;
    }

    function getWinner(uint256 roomId) external view override returns (address) {
        Room storage r = rooms[roomId];
        require(r.isClosed && r.isSuccessful, "No winner");
        require(roomWinner[roomId] != address(0), "Winner not set");
        return roomWinner[roomId];
    }

    function getWinnerPerformance(uint256 roomId) external view override returns (uint256) {
        Room storage r = rooms[roomId];
        require(r.isClosed && r.isSuccessful, "No winner");
        return r.tier; // 返回房间等级作为业绩
    }

    function getRoom(uint256 roomId) external view returns (
        address creator,
        uint256 tier,
        address[] memory participants,
        bool isSuccessful,
        bool isClosed,
        address winner,
        uint256 endTime
    ) {
        Room storage r = rooms[roomId];
        return (
            r.creator,
            r.tier,
            r.participants,
            r.isSuccessful,
            r.isClosed,
            roomWinner[roomId],
            r.createTime + ROOM_DURATION
        );
    }

    // 新增：获取房间详细信息的函数，避免前端解析复杂结构体
    function getRoomDetails(uint256 roomId) external view returns (
        address creator,
        uint256 tier,
        address[] memory participants,
        uint256 createTime,
        bool isClosed,
        bool isSuccessful,
        uint8 winnerIndex,
        address winner,
        uint256 subsidyPer,
        uint256 creatorCommission,
        uint256 systemFee,
        bool systemFeeDistributed,
        uint256 endTime,
        bool readyForWinner,
        bytes32 lotteryTxHash,
        uint256 lotteryTimestamp
    ) {
        Room storage r = rooms[roomId];
        return (
            r.creator,
            r.tier,
            r.participants,
            r.createTime,
            r.isClosed,
            r.isSuccessful,
            r.winnerIndex,
            roomWinner[roomId],
            r.subsidyPer,
            r.creatorCommission,
            r.systemFee,
            r.systemFeeDistributed,
            r.createTime + ROOM_DURATION,
            roomReadyForWinner[roomId],
            roomLotteryTxHash[roomId],
            roomLotteryTimestamp[roomId]
        );
    }

    // 新增：检查房间是否过期的函数
    function isRoomExpired(uint256 roomId) external view returns (bool) {
        Room storage r = rooms[roomId];
        return block.timestamp > r.createTime + ROOM_DURATION;
    }

    // 新增：获取房间状态的函数
    function getRoomStatus(uint256 roomId) external view returns (
        bool exists,
        bool isClosed,
        bool isSuccessful,
        bool isExpired,
        bool isFull,
        uint256 participantCount
    ) {
        Room storage r = rooms[roomId];
        bool exists_ = r.creator != address(0);
        bool isExpired_ = block.timestamp > r.createTime + ROOM_DURATION;
        bool isFull_ = r.participants.length >= MAX_PARTICIPANTS;

        return (
            exists_,
            r.isClosed,
            r.isSuccessful,
            isExpired_,
            isFull_,
            r.participants.length
        );
    }

    // 新增：检查用户权限的函数
    function getUserPermissions(uint256 roomId, address user) external view returns (
        bool isCreator,
        bool isParticipant,
        bool isWinner,
        bool hasJoinedMapping,
        bool hasJoinedArray,
        bool hasClaimed,
        bool canClaim
    ) {
        Room storage r = rooms[roomId];

        hasJoinedMapping = hasJoined[roomId][user];
        hasClaimed = creatorClaimed[roomId][user] || participantClaimed[roomId][user] ||
                    winnerQPTClaimed[roomId][user] || winnerPointsClaimed[roomId][user];

        // 检查是否在参与者数组中
        for (uint256 i = 0; i < r.participants.length; i++) {
            if (r.participants[i] == user) {
                hasJoinedArray = true;
                break;
            }
        }

        // 计算权限，与 claim 函数逻辑完全一致
        bool isCreatorLocal = (user == r.creator);
        bool isActualParticipant = hasJoinedMapping || hasJoinedArray;
        bool isWinnerLocal = r.isSuccessful && roomWinner[roomId] != address(0) && (roomWinner[roomId] == user);

        bool canClaimLocal = false;
        if (r.isClosed && !hasClaimed) {
            if (r.isSuccessful) {
                // 成功的房间：创建者、参与者或赢家可以领取
                canClaimLocal = isCreatorLocal || isActualParticipant || isWinnerLocal;
            } else {
                // 失败的房间：只有参与者可以获得退款
                canClaimLocal = isActualParticipant;
            }
        }

        return (
            isCreatorLocal,
            isActualParticipant,
            isWinnerLocal,
            hasJoinedMapping,
            hasJoinedArray,
            hasClaimed,
            canClaimLocal
        );
    }

    /// @notice 检查用户角色分离的领取状态
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @return hasClaimedCreator 是否已领取发起人佣金
    /// @return hasClaimedParticipant 是否已领取参与者退款
    /// @return canClaimCreator 是否可以领取发起人佣金
    /// @return canClaimParticipant 是否可以领取参与者退款
    function getUserClaimStatus(uint256 roomId, address user) external view returns (
        bool hasClaimedCreator,
        bool hasClaimedParticipant,
        bool canClaimCreator,
        bool canClaimParticipant
    ) {
        Room storage r = rooms[roomId];

        // 获取领取状态
        hasClaimedCreator = creatorClaimed[roomId][user];
        hasClaimedParticipant = participantClaimed[roomId][user];

        // 检查用户角色
        bool isCreator = (user == r.creator);
        bool isParticipant = hasJoined[roomId][user];
        for (uint256 i = 0; i < r.participants.length && !isParticipant; i++) {
            if (r.participants[i] == user) {
                isParticipant = true;
            }
        }
        bool isWinner = r.isSuccessful && roomWinner[roomId] != address(0) && (roomWinner[roomId] == user);

        // 计算可领取状态
        if (r.isClosed) {
            // 发起人佣金：房间成功 + 是创建者 + 未领取
            canClaimCreator = r.isSuccessful && isCreator && !hasClaimedCreator;

            // 参与者退款：是参与者 + 不是赢家 + 未领取 + 有支付记录
            canClaimParticipant = isParticipant && !isWinner && !hasClaimedParticipant &&
                                userPaidAmount[roomId][user] >= r.tier;
        }

        return (
            hasClaimedCreator,
            hasClaimedParticipant,
            canClaimCreator,
            canClaimParticipant
        );
    }

    /// @notice 检查用户是否已领取奖励
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @return 是否已领取任何奖励
    function checkClaimed(uint256 roomId, address user) external view returns (bool) {
        return creatorClaimed[roomId][user] ||
               participantClaimed[roomId][user] ||
               winnerQPTClaimed[roomId][user] ||
               winnerPointsClaimed[roomId][user];
    }

    /// @notice 获取用户在指定房间的独立赢家奖励领取状态
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @return hasClaimedQPT 是否已领取QPT奖励
    /// @return hasClaimedPoints 是否已领取积分奖励
    /// @return canClaimQPT 是否可以领取QPT奖励
    /// @return canClaimPoints 是否可以领取积分奖励
    function getWinnerRewardStatus(uint256 roomId, address user) external view returns (
        bool hasClaimedQPT,
        bool hasClaimedPoints,
        bool canClaimQPT,
        bool canClaimPoints
    ) {
        Room storage r = rooms[roomId];

        // 获取独立领取状态
        hasClaimedQPT = winnerQPTClaimed[roomId][user];
        hasClaimedPoints = winnerPointsClaimed[roomId][user];

        // 检查是否为赢家
        bool isWinner = r.isSuccessful && roomWinner[roomId] != address(0) && (roomWinner[roomId] == user);

        // 计算可领取状态
        if (r.isClosed && r.isSuccessful && isWinner) {
            // 检查USDT支付记录
            bool hasPaidUSDT = userPaidAmount[roomId][user] >= r.tier;

            canClaimQPT = !hasClaimedQPT && hasPaidUSDT;
            canClaimPoints = !hasClaimedPoints && hasPaidUSDT;
        }
    }

    /// @notice 紧急情况下重置用户领取状态（仅管理员）
    /// @param roomId 房间ID
    /// @param user 用户地址
    /// @param claimType 领取类型：0=发起人，1=参与者，2=赢家QPT，3=赢家积分
    function emergencyResetClaimStatus(uint256 roomId, address user, uint8 claimType)
        external onlyOwner {
        require(claimType <= 3, "Invalid claim type");

        if (claimType == 0) {
            creatorClaimed[roomId][user] = false;
        } else if (claimType == 1) {
            participantClaimed[roomId][user] = false;
        } else if (claimType == 2) {
            winnerQPTClaimed[roomId][user] = false;
        } else if (claimType == 3) {
            winnerPointsClaimed[roomId][user] = false;
        }
    }

    /// @notice 获取房间的奖励分配信息
    /// @param roomId 房间ID
    /// @return creatorCommission 发起人佣金
    /// @return participantSubsidy 参与者补贴
    /// @return winnerQPTAmount 赢家QPT数量
    /// @return winnerPoints 赢家积分
    function getRoomRewardInfo(uint256 roomId) external view returns (
        uint256 creatorCommission,
        uint256 participantSubsidy,
        uint256 winnerQPTAmount,
        uint256 winnerPoints
    ) {
        Room storage r = rooms[roomId];
        creatorCommission = r.creatorCommission;
        participantSubsidy = r.subsidyPer;
        // QPT数量根据档位计算（与QPTLocker合约保持一致）
        if (r.tier == TIER_30) winnerQPTAmount = 100 * 1e18;
        else if (r.tier == TIER_50) winnerQPTAmount = 150 * 1e18;
        else if (r.tier == TIER_100) winnerQPTAmount = 200 * 1e18;
        else if (r.tier == TIER_200) winnerQPTAmount = 300 * 1e18;
        else if (r.tier == TIER_500) winnerQPTAmount = 400 * 1e18;
        else if (r.tier == TIER_1000) winnerQPTAmount = 500 * 1e18;
        winnerPoints = tierPoints[r.tier];

        return (creatorCommission, participantSubsidy, winnerQPTAmount, winnerPoints);
    }

    // 新增：获取房间时间信息的函数
    function getRoomTimeInfo(uint256 roomId) external view returns (
        uint256 createTime,
        uint256 endTime,
        uint256 currentTime,
        bool isExpired,
        uint256 timeLeft
    ) {
        Room storage r = rooms[roomId];
        uint256 endTime_ = r.createTime + ROOM_DURATION;
        bool isExpired_ = block.timestamp > endTime_;

        return (
            r.createTime,
            endTime_,
            block.timestamp,
            isExpired_,
            isExpired_ ? 0 : endTime_ - block.timestamp
        );
    }

    // 移除暂停功能，简化权限管理

    // 新增：查询用户USDT转账记录
    function getUserPaidAmount(uint256 roomId, address user) external view returns (uint256) {
        return userPaidAmount[roomId][user];
    }

    // 新增：获取开奖信息
    function getLotteryInfo(uint256 roomId) external view returns (
        bool readyForWinner,
        address winner,
        bytes32 lotteryTxHash,
        uint256 lotteryTimestamp,
        uint8 winnerIndex
    ) {
        Room storage r = rooms[roomId];
        return (
            roomReadyForWinner[roomId],
            roomWinner[roomId],
            roomLotteryTxHash[roomId],
            roomLotteryTimestamp[roomId],
            r.winnerIndex
        );
    }

    // 新增：批量查询房间信息
    function getRoomsBatch(uint256[] calldata roomIds) external view returns (
        address[] memory creators,
        uint256[] memory tiers,
        bool[] memory isClosed,
        bool[] memory isSuccessful,
        address[] memory winners,
        uint256[] memory participantCounts
    ) {
        uint256 length = roomIds.length;
        creators = new address[](length);
        tiers = new uint256[](length);
        isClosed = new bool[](length);
        isSuccessful = new bool[](length);
        winners = new address[](length);
        participantCounts = new uint256[](length);

        for (uint256 i = 0; i < length; i++) {
            Room storage r = rooms[roomIds[i]];
            creators[i] = r.creator;
            tiers[i] = r.tier;
            isClosed[i] = r.isClosed;
            isSuccessful[i] = r.isSuccessful;
            winners[i] = roomWinner[roomIds[i]];
            participantCounts[i] = r.participants.length;
        }
    }



    // 新增：获取合约基本统计信息（移除复杂遍历以减少合约大小）
    function getContractStats() external view returns (
        uint256 totalRoomsCount,
        uint256 contractUSDTBalance
    ) {
        totalRoomsCount = nextRoomId;
        contractUSDTBalance = usdt.balanceOf(address(this));
    }

    // 新增：检查房间是否可以开奖
    function canCloseRoom(uint256 roomId) external view returns (bool canClose, string memory reason) {
        Room storage r = rooms[roomId];

        if (r.creator == address(0)) {
            return (false, "Room does not exist");
        }
        if (r.isClosed) {
            return (false, "Room already closed");
        }
        if (r.participants.length < MAX_PARTICIPANTS) {
            return (false, "Room not full");
        }
        if (block.timestamp > r.createTime + ROOM_DURATION) {
            return (false, "Room expired");
        }

        return (true, "");
    }

    // 新增：检查房间是否可以过期
    function canExpireRoom(uint256 roomId) external view returns (bool canExpire, string memory reason) {
        Room storage r = rooms[roomId];

        if (r.creator == address(0)) {
            return (false, "Room does not exist");
        }
        if (r.isClosed) {
            return (false, "Room already closed");
        }
        if (block.timestamp <= r.createTime + ROOM_DURATION) {
            return (false, "Room not expired yet");
        }

        return (true, "");
    }

    function withdrawUSDT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= usdt.balanceOf(address(this)), "Insufficient USDT balance");
        usdt.safeTransfer(to, amount);
        emit EmergencyUSDTWithdrawn(to, amount);
    }

    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    // 角色分离的领取状态映射
    mapping(uint256 => mapping(address => bool)) public creatorClaimed;     // 发起人佣金领取状态
    mapping(uint256 => mapping(address => bool)) public participantClaimed; // 参与者退款领取状态

    // 赢家奖励独立领取状态
    mapping(uint256 => mapping(address => bool)) public winnerQPTClaimed;   // 赢家QPT奖励领取状态
    mapping(uint256 => mapping(address => bool)) public winnerPointsClaimed; // 赢家积分奖励领取状态

    /// @notice 升级到 V2 版本的初始化函数
    /// @dev 修复 tierPoints 精度问题 - 只能调用一次
    function initializeV2() external reinitializer(2) {
        // 设置正确的 tierPoints 值（考虑积分系统的6位小数精度）
        // 积分系统使用 6 位小数，所以需要乘以 10^6
        tierPoints[30000000] = 30 * 10**6;   // 30 积分 = 30,000,000
        tierPoints[50000000] = 50 * 10**6;   // 50 积分 = 50,000,000
        tierPoints[100000000] = 100 * 10**6; // 100 积分 = 100,000,000
        tierPoints[200000000] = 200 * 10**6; // 200 积分 = 200,000,000
        tierPoints[500000000] = 500 * 10**6; // 500 积分 = 500,000,000
        tierPoints[1000000000] = 1000 * 10**6; // 1000 积分 = 1,000,000,000
    }

    // 调整__gap大小，为新增的映射预留空间
    // 新增了8个映射：roomReadyForWinner, roomWinner, roomLotteryTxHash, roomLotteryTimestamp, roomLastActionTime, creatorClaimed, participantClaimed, winnerQPTClaimed, winnerPointsClaimed
    // 移除了1个废弃映射：claimed
    uint256[41] private __gap;
}
