{"_format": "hh-sol-cache-2", "files": {"E:\\qupintuan\\qupintuan\\hardhat\\contracts\\AddressManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "343ec64c0da0547f99519cac9b4c86ad", "sourceName": "contracts/AddressManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["AddressManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\StorageValidator.sol": {"lastModificationDate": 1240428288000, "contentHash": "0e6ec050f38ea6ddf6150976e5457f5a", "sourceName": "contracts/StorageValidator.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.19"], "artifacts": ["StorageValidator"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "db24b956904575cc79d02867ec03263c", "sourceName": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../utils/ContextUpgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["OwnableUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "cbe53d6b803c1102081e00b3a0418ea3", "sourceName": "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../utils/ContextUpgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["PausableUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "c30c805386fda8a42ff515da963d3a95", "sourceName": "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["AddressUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "efeffcf5034292d7ea635de588c95995", "sourceName": "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ReentrancyGuardUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol": {"lastModificationDate": 1240428288000, "contentHash": "b0970a564d121abf9adfff8d1a01eb16", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../../utils/AddressUpgradeable.sol"], "versionPragmas": ["^0.8.2"], "artifacts": ["Initializable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "52ef5d6c909d6f8ef3077445d3328eca", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../../interfaces/draft-IERC1822Upgradeable.sol", "../ERC1967/ERC1967UpgradeUpgradeable.sol", "./Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["UUPSUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "6200b84950eb05b4a92a39fd1d6e0f9b", "sourceName": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ContextUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\interfaces\\draft-IERC1822Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "a6883cfd85607b16c121a02728369eb3", "sourceName": "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1822ProxiableUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "2587366284baa690cce0accaca1a20f9", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../beacon/IBeaconUpgradeable.sol", "../../interfaces/IERC1967Upgradeable.sol", "../../interfaces/draft-IERC1822Upgradeable.sol", "../../utils/AddressUpgradeable.sol", "../../utils/StorageSlotUpgradeable.sol", "../utils/Initializable.sol"], "versionPragmas": ["^0.8.2"], "artifacts": ["ERC1967UpgradeUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\StorageSlotUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "02cf959b00ba5c650708472f195f8b29", "sourceName": "@openzeppelin/contracts-upgradeable/utils/StorageSlotUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["StorageSlotUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\interfaces\\IERC1967Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "a5421a5e611067b9202e83329fbf6fc7", "sourceName": "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1967Upgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\proxy\\beacon\\IBeaconUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "21bffd733b4ca013e125a0ffe2e52254", "sourceName": "@openzeppelin/contracts-upgradeable/proxy/beacon/IBeaconUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IBeaconUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "5f2d8b81c0ff5bd2047b4846c20b998d", "sourceName": "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./IERC165Upgradeable.sol", "../../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC165Upgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\introspection\\IERC165Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "d6ecf203a5e72c845be9bbf2f304a289", "sourceName": "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC165Upgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC1155\\IERC1155ReceiverUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "eb51ed084f6f7fd2c7098715c5690285", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC1155/IERC1155ReceiverUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../../utils/introspection/IERC165Upgradeable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155ReceiverUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "a58e70d6b4aa349fbfd9b6e2f5aab4a3", "sourceName": "@openzeppelin/contracts-upgradeable/governance/TimelockControllerUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../access/AccessControlUpgradeable.sol", "../token/ERC721/IERC721ReceiverUpgradeable.sol", "../token/ERC1155/IERC1155ReceiverUpgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["TimelockControllerUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "c88845618a815ef601f3f552c99d7ebb", "sourceName": "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./IAccessControlUpgradeable.sol", "../utils/ContextUpgradeable.sol", "../utils/StringsUpgradeable.sol", "../utils/introspection/ERC165Upgradeable.sol", "../proxy/utils/Initializable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["AccessControlUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC721\\IERC721ReceiverUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "ba28cf60b52b00d2b3a190bcdf0952bd", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC721/IERC721ReceiverUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721ReceiverUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\access\\IAccessControlUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "21b43d1337ebc77c11da3cbe3fd65316", "sourceName": "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IAccessControlUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\StringsUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "c221361be1c4953f5b71f47475b90266", "sourceName": "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./math/MathUpgradeable.sol", "./math/SignedMathUpgradeable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["StringsUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\math\\MathUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "5a2a749b45b6a8eb035f4bf75addcb27", "sourceName": "@openzeppelin/contracts-upgradeable/utils/math/MathUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["MathUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\math\\SignedMathUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "2a6b819b2e241091ada6d645df3e3929", "sourceName": "@openzeppelin/contracts-upgradeable/utils/math/SignedMathUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SignedMathUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\OrderManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "91aade4691ce66d006099f45035b9ce9", "sourceName": "contracts/OrderManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IAddressManagement.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["OrderManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IAddressManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "e0d37e0896ca58da94aec2cf76d7609d", "sourceName": "contracts/interfaces/IAddressManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IAddressManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\QPTLocker.sol": {"lastModificationDate": 1240428288000, "contentHash": "d5b3b9ba9bd6019a7c9b6032196744c1", "sourceName": "contracts/QPTLocker.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts/token/ERC20/IERC20.sol", "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts/utils/Address.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "./interfaces/IAgentSystem.sol", "./interfaces/IQPTLock.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["QPTLocker"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IAgentSystem.sol": {"lastModificationDate": 1240428288000, "contentHash": "7a68499f3911c4dd0e063b801c62a7b1", "sourceName": "contracts/interfaces/IAgentSystem.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IAgentSystem"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IQPTLock.sol": {"lastModificationDate": 1240428288000, "contentHash": "47b71f133633f9913c68474243975348", "sourceName": "contracts/interfaces/IQPTLock.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IQPTLock"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\Address.sol": {"lastModificationDate": 1240428288000, "contentHash": "211ffd288c1588ba8c10eae668ca3c66", "sourceName": "@openzeppelin/contracts/utils/Address.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.1"], "artifacts": ["Address"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1240428288000, "contentHash": "df36f7051335cd1e748b1b6463b7fdd3", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\utils\\SafeERC20.sol": {"lastModificationDate": 1240428288000, "contentHash": "1b5d667d3740d866eca0352758e59827", "sourceName": "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../IERC20.sol", "../extensions/IERC20Permit.sol", "../../../utils/Address.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["SafeERC20"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Permit.sol": {"lastModificationDate": 1240428288000, "contentHash": "aa849939a4ae83a4dcb2dca9b3e9e707", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Permit"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\SimpleQueryContract.sol": {"lastModificationDate": 1240428288000, "contentHash": "0eb47ccc70a34933792959958ba5e0e8", "sourceName": "contracts/SimpleQueryContract.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts/token/ERC20/IERC20.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["IAgentSystem", "IGroupBuyRoom", "INodeStaking", "IPointsManagement", "IProductManagement", "IQPTBuyback", "IQPTLocker", "SimpleQueryContract"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\QPTBuyback.sol": {"lastModificationDate": 1240428288000, "contentHash": "4523853c2006d8f223b720714e9f8b46", "sourceName": "contracts/QPTBuyback.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["IAgentSystem", "QPTBuyback"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC20\\IERC20Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "9a9398a7dbda9d014f04d5eb0fb581fd", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20Upgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC20\\utils\\SafeERC20Upgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "f6d59fee2d6fafaea5a33717dfba1260", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../IERC20Upgradeable.sol", "../extensions/IERC20PermitUpgradeable.sol", "../../../utils/AddressUpgradeable.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["SafeERC20Upgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\IERC20PermitUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "30ef13e9a1b22e74e5e4ce5ab2a247e4", "sourceName": "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/IERC20PermitUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC20PermitUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\PointsManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "f6f280a728da4a9589d9d5d553e9f62b", "sourceName": "contracts/PointsManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["IMerchantManagement", "PointsManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\AgentSystemMinimal.sol": {"lastModificationDate": 1240428288000, "contentHash": "2c23fb289eadfa78b032421a145b0687", "sourceName": "contracts/AgentSystemMinimal.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol", "@openzeppelin/contracts/governance/TimelockController.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["AgentSystem"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\governance\\TimelockController.sol": {"lastModificationDate": 1240428288000, "contentHash": "39cfb1304186349ce1051fe20ff67567", "sourceName": "@openzeppelin/contracts/governance/TimelockController.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../access/AccessControl.sol", "../token/ERC721/IERC721Receiver.sol", "../token/ERC1155/IERC1155Receiver.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["TimelockController"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\access\\AccessControl.sol": {"lastModificationDate": 1240428288000, "contentHash": "a2b1ec38a8dad325a596f926890772b8", "sourceName": "@openzeppelin/contracts/access/AccessControl.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./IAccessControl.sol", "../utils/Context.sol", "../utils/Strings.sol", "../utils/introspection/ERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["AccessControl"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\token\\ERC721\\IERC721Receiver.sol": {"lastModificationDate": 1240428288000, "contentHash": "c22d4395e33763de693fd440c6fd10e1", "sourceName": "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC721Receiver"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\token\\ERC1155\\IERC1155Receiver.sol": {"lastModificationDate": 1240428288000, "contentHash": "9f8822b72fe2702979e40160cb6d9636", "sourceName": "@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["../../utils/introspection/IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC1155Receiver"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\access\\IAccessControl.sol": {"lastModificationDate": 1240428288000, "contentHash": "57c84298234411cea19c7c284d86be8b", "sourceName": "@openzeppelin/contracts/access/IAccessControl.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IAccessControl"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1240428288000, "contentHash": "5f2c5c4b6af2dd4551027144797bc8be", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Context"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\Strings.sol": {"lastModificationDate": 1240428288000, "contentHash": "48686fc32a22a3754b8e63321857dd2a", "sourceName": "@openzeppelin/contracts/utils/Strings.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./math/Math.sol", "./math/SignedMath.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["Strings"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol": {"lastModificationDate": 1240428288000, "contentHash": "0e7db055ce108f9da7bb6686a00287c0", "sourceName": "@openzeppelin/contracts/utils/introspection/ERC165.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["./IERC165.sol"], "versionPragmas": ["^0.8.0"], "artifacts": ["ERC165"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\math\\Math.sol": {"lastModificationDate": 1240428288000, "contentHash": "fe63409d8a06818b926cf89e0ea88b1b", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["Math"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SignedMath.sol": {"lastModificationDate": 1240428288000, "contentHash": "9488ebd4daacfee8ad04811600d7d061", "sourceName": "@openzeppelin/contracts/utils/math/SignedMath.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["SignedMath"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol": {"lastModificationDate": 1240428288000, "contentHash": "03e6768535ac4da0e9756f1d8a4a018a", "sourceName": "@openzeppelin/contracts/utils/introspection/IERC165.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["IERC165"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\TimelockControllerUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "7828db536e4c90280913286a5a0cd213", "sourceName": "contracts/TimelockControllerUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/governance/TimelockControllerUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["CustomTimelockController"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\ProductManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "79da74fed4888d4cc41adb3a4a2aa925", "sourceName": "contracts/ProductManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IAddressManagement.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["IMerchantManagement", "IOrderManagement", "IPointsManagement", "ProductManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\MultiSigWallet.sol": {"lastModificationDate": 1240428288000, "contentHash": "05ac3f2f7dcd9fc47b15b883c6bd902b", "sourceName": "contracts/MultiSigWallet.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.19"], "artifacts": ["MultiSigWallet"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\MerchantManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "3ad92f3069ed4d0b63f6c9321e2bf5e9", "sourceName": "contracts/MerchantManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["IPointsManagement", "MerchantManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\GroupBuyRoomMinimal.sol": {"lastModificationDate": 1240428288000, "contentHash": "ac5d8bb95858b29047a54ccc0718742f", "sourceName": "contracts/GroupBuyRoomMinimal.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "contracts/interfaces/IGroupBuyRoom.sol", "contracts/interfaces/IAgentSystem.sol", "contracts/interfaces/IQPTLock.sol", "contracts/interfaces/IPointsManagement.sol", "contracts/interfaces/IFeeSplitManager.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["GroupBuyRoomMinimal"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IGroupBuyRoom.sol": {"lastModificationDate": 1240428288000, "contentHash": "beba7c8b49b506c8d3e2f0c828b02f06", "sourceName": "contracts/interfaces/IGroupBuyRoom.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IGroupBuyRoom"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IPointsManagement.sol": {"lastModificationDate": 1240428288000, "contentHash": "781a07224731430efd0afed893382fb0", "sourceName": "contracts/interfaces/IPointsManagement.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IPointsManagement"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IFeeSplitManager.sol": {"lastModificationDate": 1240428288000, "contentHash": "e0ac7c683e1046bca3ee9957fe7e4e71", "sourceName": "contracts/interfaces/IFeeSplitManager.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IFeeSplitManager"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\FeeSplitManager.sol": {"lastModificationDate": 1240428288000, "contentHash": "********************************", "sourceName": "contracts/FeeSplitManager.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IFeeSplitManager.sol", "./interfaces/IGroupBuyRoom.sol", "./interfaces/INodeStaking.sol", "./StorageValidator.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["FeeSplitManager"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\INodeStaking.sol": {"lastModificationDate": 1240428288000, "contentHash": "ebd28a1467122cc3bd01e9f2f4eac7b6", "sourceName": "contracts/interfaces/INodeStaking.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["INodeStaking"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\NodeStaking.sol": {"lastModificationDate": 1240428288000, "contentHash": "9d4cb19fa716d83f5467b4d15234ca17", "sourceName": "contracts/NodeStaking.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": ["@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol", "@openzeppelin/contracts-upgradeable/utils/structs/EnumerableSetUpgradeable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol", "./interfaces/IQPTLock.sol", "./interfaces/INodeStaking.sol"], "versionPragmas": ["^0.8.22"], "artifacts": ["NodeStaking"]}, "E:\\qupintuan\\qupintuan\\hardhat\\node_modules\\@openzeppelin\\contracts-upgradeable\\utils\\structs\\EnumerableSetUpgradeable.sol": {"lastModificationDate": 1240428288000, "contentHash": "b5cad7eaf6f7db7b48d990721ee0d89e", "sourceName": "@openzeppelin/contracts-upgradeable/utils/structs/EnumerableSetUpgradeable.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.0"], "artifacts": ["EnumerableSetUpgradeable"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IQPTBuyback.sol": {"lastModificationDate": 1240428288000, "contentHash": "cfe0d05da2e67eaca9aba0d1e118feca", "sourceName": "contracts/interfaces/IQPTBuyback.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IQPTBuyback"]}, "E:\\qupintuan\\qupintuan\\hardhat\\contracts\\interfaces\\IVersioned.sol": {"lastModificationDate": 1240428288000, "contentHash": "ed73a547c53cbd5a83c966fa7a940af3", "sourceName": "contracts/interfaces/IVersioned.sol", "solcConfig": {"version": "0.8.22", "settings": {"optimizer": {"enabled": true, "runs": 10}, "viaIR": true, "outputSelection": {"*": {"*": ["abi", "storageLayout", "metadata", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers"], "": ["ast"]}}, "evmVersion": "paris"}}, "imports": [], "versionPragmas": ["^0.8.22"], "artifacts": ["IVersioned"]}}}