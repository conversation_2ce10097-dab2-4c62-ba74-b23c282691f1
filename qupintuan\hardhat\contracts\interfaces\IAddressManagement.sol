// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

struct AddressInfo {
    uint256 addressId;
    address user;
    string  name;
    string  phone;
    string  province;
    string  city;
    string  district;
    string  detail;
    bool    isDefault;
    uint256 createTime;
}

interface IAddressManagement {
    function getAddress(address user, uint256 addressId) external view returns (AddressInfo memory);
    function getDefaultAddress(address user) external view returns (AddressInfo memory);
    function getMyDefaultAddress() external view returns (AddressInfo memory);
    function getMyAddresses() external view returns (AddressInfo[] memory);
}
