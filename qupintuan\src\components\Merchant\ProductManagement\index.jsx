// src/components/Merchant/ProductManagement/index.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { getMerchantProducts, createProduct } from '@/apis/mallApi';
import { fixPriceDisplay, needsPriceFix } from '@/utils/priceDisplayFix';
import AddProduct from './AddProduct';
import OrderManagement from '../OrderManagement';
import './index.css';

export default function ProductManagement() {
  const { address: account } = useAccount();
  const [activeTab, setActiveTab] = useState('list');
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // 加载商品列表
  const loadProducts = async () => {
    if (!account) return;

    setIsLoading(true);
    try {


      // 获取商家的商品ID列表
      const productIds = await getMerchantProducts({ merchantAddress: account });


      if (productIds.length === 0) {
        setProducts([]);

        return;
      }

      // 获取每个商品的详细信息
      const { getProductInfo } = await import('@/apis/mallApi');
      const productPromises = productIds.map(id => getProductInfo({ productId: id }));
      const productList = await Promise.all(productPromises);

      setProducts(productList);

    } catch (error) {
      console.error('❌ [ProductManagement] 加载商品列表失败:', error);

      // 如果是合约未部署或其他错误，显示模拟数据
      if (error.message.includes('contract') ||
          error.message.includes('network') ||
          error.message.includes('ABI') ||
          error.message.includes('encoding')) {
        console.log('🔄 [ProductManagement] 使用模拟数据，原因:', error.message);
        const mockProducts = [
          {
            productId: 1,
            name: '示例商品1',
            description: '这是一个示例商品，用于展示商品管理功能',
            price: 100,
            stock: 50,
            isActive: true,
            sales: 5,
            images: []
          }
        ];
        setProducts(mockProducts);
        toast.success('🎉 趣拼团示例商品数据加载成功！愛拼才会赢！');
      } else {
        toast.error(`加载商品列表失败: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadProducts();
  }, [account]);

  return (
    <div className="product-management">
      <div className="module-header">
        <h3>📦 商品管理</h3>
        <p>管理您的商品上架和库存</p>
        <button
          className="refresh-btn"
          onClick={loadProducts}
          disabled={isLoading}
        >
          {isLoading ? '🔄 加载中...' : '🔄 刷新'}
        </button>
      </div>

      {/* 功能导航 */}
      <div className="product-nav">
        <button
          className={`nav-btn ${activeTab === 'list' ? 'active' : ''}`}
          onClick={() => setActiveTab('list')}
        >
          📋 商品列表 ({products.length})
        </button>
        <button
          className={`nav-btn ${activeTab === 'add' ? 'active' : ''}`}
          onClick={() => setActiveTab('add')}
        >
          ➕ 添加商品
        </button>
        <button
          className={`nav-btn ${activeTab === 'orders' ? 'active' : ''}`}
          onClick={() => setActiveTab('orders')}
        >
          📊 订单管理
        </button>
      </div>

      {/* 内容区域 */}
      <div className="product-content">
        {activeTab === 'list' && (
          <div className="content-section">
            <div className="product-list-header">
              <h4>📋 商品列表</h4>
              <p>管理您的所有商品</p>
            </div>

            {isLoading ? (
              <div className="loading-container">
                <div className="loading-spinner">🔄</div>
                <p>正在加载商品列表...</p>
              </div>
            ) : products.length === 0 ? (
              <div className="empty-container">
                <div className="empty-icon">📦</div>
                <h3>暂无商品</h3>
                <p>您还没有添加任何商品</p>
                <button
                  className="add-first-btn"
                  onClick={() => setActiveTab('add')}
                >
                  ➕ 添加第一个商品
                </button>
              </div>
            ) : (
              <div className="product-grid">
                {products.map((product) => (
                  <div key={product.productId || product.id} className="product-card">
                    <div className="product-header">
                      <h5 className="product-name">{product.name}</h5>
                      <div className={`product-status ${product.isActive ? 'active' : 'inactive'}`}>
                        {product.isActive ? '✅ 上架中' : '⏸️ 已下架'}
                      </div>
                    </div>

                    {/* 商品图片 */}
                    {product.images && product.images.length > 0 && (
                      <div className="product-image">
                        <img
                          src={product.images[0].startsWith('http')
                            ? product.images[0]
                            : `https://gateway.pinata.cloud/ipfs/${product.images[0]}`}
                          alt={product.name}
                          onError={(e) => {
                            // 尝试备用网关
                            if (e.target.src.includes('pinata')) {
                              e.target.src = `https://cloudflare-ipfs.com/ipfs/${product.images[0]}`;
                            } else if (e.target.src.includes('cloudflare')) {
                              e.target.src = `https://dweb.link/ipfs/${product.images[0]}`;
                            } else {
                              e.target.style.display = 'none';
                            }
                          }}
                        />
                      </div>
                    )}

                    <div className="product-info">
                      <p className="product-description">
                        {product.description.length > 100
                          ? `${product.description.substring(0, 100)}...`
                          : product.description
                        }
                      </p>
                      <div className="product-details">
                        <div className="detail-item">
                          <span className="label">价格:</span>
                          <span className="value">
                            {needsPriceFix(product.price) ? fixPriceDisplay(product.price) : product.price} 积分
                          </span>
                        </div>
                        <div className="detail-item">
                          <span className="label">库存:</span>
                          <span className="value">{product.stock} 件</span>
                        </div>
                        <div className="detail-item">
                          <span className="label">销量:</span>
                          <span className="value">{product.sales || 0} 件</span>
                        </div>
                      </div>
                    </div>

                    <div className="product-actions">
                      <button className="action-btn edit">✏️ 编辑</button>
                      <button className="action-btn toggle">
                        {product.isActive ? '⏸️ 下架' : '▶️ 上架'}
                      </button>
                      <button className="action-btn delete">🗑️ 删除</button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="development-notice">
              <h4>🚧 功能开发中</h4>
              <div className="notice-items">
                <div className="notice-item">• 商品编辑功能</div>
                <div className="notice-item">• 批量操作功能</div>
                <div className="notice-item">• 商品分类管理</div>
                <div className="notice-item">• 销售数据统计</div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'add' && (
          <div className="content-section">
            <AddProduct
              onProductAdded={async (result) => {
          
                toast.success('🎉 趣拼团商品添加成功！愛拼才会赢！');
                await loadProducts(); // 重新加载商品列表
                setActiveTab('list'); // 跳转到商品列表
              }}
            />
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="content-section">
            <OrderManagement />
          </div>
        )}
      </div>

      {/* 功能说明 */}
      <div className="feature-notice">
        <h4>📖 功能说明</h4>
        <div className="notice-content">
          <p>商品管理功能将包含以下核心功能：</p>
          <div className="notice-grid">
            <div className="notice-item">
              <strong>商品上架</strong>
              <span>支持多图片上传，详细商品描述</span>
            </div>
            <div className="notice-item">
              <strong>库存管理</strong>
              <span>实时库存监控，自动补货提醒</span>
            </div>
            <div className="notice-item">
              <strong>订单处理</strong>
              <span>订单状态跟踪，发货物流管理</span>
            </div>
            <div className="notice-item">
              <strong>销售统计</strong>
              <span>销售数据分析，收益报表生成</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
