// 测试 buyProduct 中的每个合约调用
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";
const ADDRESS_MANAGEMENT = "0xA27C195F6e80Dd8742a3beaD3e4871f31C813102";
const POINTS_MANAGEMENT = "0xC3486bf571914BC5c83f89c69fA23fD3C75Aa5f4";
const MERCHANT_MANAGEMENT = "0x13a311f1d52376861207ae641278f6e5de55F4B5";
const USER_ADDRESS = "0xA2dD965E6AAE7Bea5cd24CfC05653B8c72c2F9EF";

// ABI 定义
const ADDRESS_MANAGEMENT_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "user", "type": "address"},
      {"internalType": "uint256", "name": "addressId", "type": "uint256"}
    ],
    "name": "getAddress",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "addressId", "type": "uint256"},
          {"internalType": "address", "name": "user", "type": "address"},
          {"internalType": "string", "name": "name", "type": "string"},
          {"internalType": "string", "name": "phone", "type": "string"},
          {"internalType": "string", "name": "province", "type": "string"},
          {"internalType": "string", "name": "city", "type": "string"},
          {"internalType": "string", "name": "district", "type": "string"},
          {"internalType": "string", "name": "detail", "type": "string"},
          {"internalType": "bool", "name": "isDefault", "type": "bool"},
          {"internalType": "uint256", "name": "createTime", "type": "uint256"}
        ],
        "internalType": "struct AddressInfo",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

const POINTS_MANAGEMENT_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "user", "type": "address"},
      {"internalType": "address", "name": "merchant", "type": "address"},
      {"internalType": "uint256", "name": "points", "type": "uint256"}
    ],
    "name": "exchangeGoods",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "user", "type": "address"}],
    "name": "groupBuyPointsNonExchangeable",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  }
];

const MERCHANT_MANAGEMENT_ABI = [
  {
    "inputs": [
      {"internalType": "address", "name": "merchant", "type": "address"},
      {"internalType": "uint256", "name": "points", "type": "uint256"}
    ],
    "name": "updateMerchantSalesPoints",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

async function testContractCalls() {
  console.log("🔍 测试 buyProduct 中的每个合约调用...");
  
  const merchant = "0x9B30CfdD2333f19509e1203a35fe36bB4f2A062A";
  const addressId = 0;
  const points = 1880000n; // 1.88 积分

  try {
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 测试 AddressManagement.getAddress()
    console.log("\n🔍 测试1：AddressManagement.getAddress()...");
    try {
      // 注意：这个调用会失败，因为前端不是授权的合约
      const addressInfo = await publicClient.readContract({
        address: ADDRESS_MANAGEMENT,
        abi: ADDRESS_MANAGEMENT_ABI,
        functionName: 'getAddress',
        args: [USER_ADDRESS, addressId]
      });
      console.log("✅ getAddress 成功:", addressInfo);
    } catch (error) {
      console.log("❌ getAddress 失败:", error.message);
      console.log("💡 这是预期的，因为前端不是授权的合约");
    }

    // 2. 测试 PointsManagement.exchangeGoods() 模拟
    console.log("\n🔍 测试2：PointsManagement.exchangeGoods() 模拟...");
    try {
      // 使用 simulateContract 模拟调用
      const simulation = await publicClient.simulateContract({
        address: POINTS_MANAGEMENT,
        abi: POINTS_MANAGEMENT_ABI,
        functionName: 'exchangeGoods',
        args: [USER_ADDRESS, merchant, points],
        account: PRODUCT_MANAGEMENT // 模拟 ProductManagement 调用
      });
      console.log("✅ exchangeGoods 模拟成功:", simulation);
    } catch (error) {
      console.log("❌ exchangeGoods 模拟失败:", error.message);
      
      if (error.message.includes("Only marketplace")) {
        console.log("💡 错误原因：只有 marketplace 可以调用");
      } else if (error.message.includes("Insufficient group buy points")) {
        console.log("💡 错误原因：用户积分不足");
      } else if (error.message.includes("System not fully configured")) {
        console.log("💡 错误原因：系统配置不完整");
      }
    }

    // 3. 检查用户积分余额
    console.log("\n🔍 测试3：检查用户积分余额...");
    const userPoints = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'groupBuyPointsNonExchangeable',
      args: [USER_ADDRESS]
    });
    
    console.log("用户积分:", userPoints.toString());
    console.log("需要积分:", points.toString());
    console.log("积分充足:", userPoints >= points ? "✅ 是" : "❌ 否");

    // 4. 测试 MerchantManagement.updateMerchantSalesPoints() 模拟
    console.log("\n🔍 测试4：MerchantManagement.updateMerchantSalesPoints() 模拟...");
    try {
      const simulation = await publicClient.simulateContract({
        address: MERCHANT_MANAGEMENT,
        abi: MERCHANT_MANAGEMENT_ABI,
        functionName: 'updateMerchantSalesPoints',
        args: [merchant, points],
        account: PRODUCT_MANAGEMENT // 模拟 ProductManagement 调用
      });
      console.log("✅ updateMerchantSalesPoints 模拟成功:", simulation);
    } catch (error) {
      console.log("❌ updateMerchantSalesPoints 模拟失败:", error.message);
    }

    console.log("\n📊 测试总结:");
    console.log("1. AddressManagement.getAddress: 前端调用失败（预期）");
    console.log("2. PointsManagement.exchangeGoods: 需要检查具体错误");
    console.log("3. 用户积分余额: 需要检查是否充足");
    console.log("4. MerchantManagement.updateMerchantSalesPoints: 需要检查权限");

  } catch (error) {
    console.error("❌ 测试过程失败:", error.message);
  }
}

// 运行测试
testContractCalls().catch(console.error);
