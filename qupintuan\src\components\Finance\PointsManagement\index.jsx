import React, { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { formatUnits, parseUnits } from 'ethers'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import './index.css'

export default function PointsManagement() {
  const { address: account, isConnected } = useAccount()

  // 状态管理
  const [pointsData, setPointsData] = useState({
    groupBuyPoints: '0',
    salesPoints: '0',
    totalExchanged: '0',
    lastExchangeTime: 0
  })
  const [transferAmount, setTransferAmount] = useState('')
  const [transferTo, setTransferTo] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTransferring, setIsTransferring] = useState(false)

  // 移除USDT余额获取，避免重复显示

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 加载积分数据
  const loadPointsData = async () => {
    if (!account || !isConnected) return

    setIsLoading(true)
    try {
      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement

      // 并行查询所有数据
      const [groupBuyPoints, salesPoints, lastExchange, stats] = await Promise.all([
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'groupBuyPointsNonExchangeable',
          args: [account]
        }),
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'salesPointsExchangeable',
          args: [account]
        }),
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'lastExchangeTime',
          args: [account]
        }),
        publicClient.readContract({
          address: pointsAddress,
          abi: ABIS.PointsManagement,
          functionName: 'pointsStats'
        })
      ])

      setPointsData({
        groupBuyPoints: groupBuyPoints ? formatUnits(groupBuyPoints, 6) : '0',
        salesPoints: salesPoints ? formatUnits(salesPoints, 6) : '0',
        totalExchanged: stats?.totalUSDTExchanged ? formatUnits(stats.totalUSDTExchanged, 6) : '0',
        lastExchangeTime: lastExchange ? Number(lastExchange) : 0
      })
    } catch (error) {
      console.error('加载积分数据失败:', error)
      toast.error('加载积分数据失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 转账拼团积分
  const handleTransfer = async () => {
    if (!account || !transferAmount || !transferTo) return

    // 检查用户权限（防止被限制的用户转账积分）
    try {
      const { checkUserPermission, PERMISSION_TYPES } = await import('@/utils/permissionManager');
      const permission = await checkUserPermission(account, PERMISSION_TYPES.ALL_OPERATIONS);
      if (!permission.allowed) {
        toast.error(permission.reason, {
          duration: 8000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('❌ [PointsManagement] 权限检查失败:', error);
      toast.error('权限检查失败，请重试');
      return;
    }

    // 检查用户是否已注册代理系统
    try {
      const { checkUserRegistered } = await import('@/apis/agentSystemApi');
      const isRegistered = await checkUserRegistered({ userAddress: account });

      if (!isRegistered) {
        toast.error('请先注册代理系统才能转账积分！', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('检查注册状态失败:', error);
      toast.error('检查注册状态失败，请重试');
      return;
    }

    const amount = parseInt(transferAmount)
    if (amount < 1) {
      toast.error('转账数量不能少于1积分')
      return
    }

    if (amount > parseInt(pointsData.groupBuyPoints)) {
      toast.error('拼团积分不足')
      return
    }

    // 验证接收地址
    if (!/^0x[a-fA-F0-9]{40}$/.test(transferTo)) {
      toast.error('请输入有效的钱包地址')
      return
    }

    if (transferTo.toLowerCase() === account.toLowerCase()) {
      toast.error('不能转账给自己')
      return
    }

    // 检查接收用户是否已注册代理系统
    try {
      const { checkUserRegistered } = await import('@/apis/agentSystemApi');
      const isReceiverRegistered = await checkUserRegistered({ userAddress: transferTo });

      if (!isReceiverRegistered) {
        toast.error('接收用户未注册代理系统，无法接收积分转账！', {
          duration: 5000,
          position: 'top-center',
        });
        return;
      }
    } catch (error) {
      console.error('检查接收用户注册状态失败:', error);
      toast.error('检查接收用户注册状态失败，请重试');
      return;
    }

    setIsTransferring(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement

      const hash = await walletClient.writeContract({
        address: pointsAddress,
        abi: ABIS.PointsManagement,
        functionName: 'transferGroupBuyPoints',
        args: [transferTo, parseUnits(transferAmount, 0)],
        account
      })

      toast.success('转账交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash })

      toast.success(`成功转账 ${transferAmount} 积分给 ${transferTo.slice(0, 6)}...${transferTo.slice(-4)}`)
      setTransferAmount('')
      setTransferTo('')

      // 刷新数据
      await loadPointsData()

    } catch (error) {
      console.error('转账失败:', error)
      toast.error('转账失败: ' + (error.message || '未知错误'))
    } finally {
      setIsTransferring(false)
    }
  }

  // 计算可获得的USDT
  const calculateUSDT = (points) => {
    return (parseInt(points || 0) * 0.3).toFixed(1)
  }

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '从未兑换'
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
  }

  useEffect(() => {
    loadPointsData()
  }, [account, isConnected])

  if (!isConnected) {
    return (
      <div className="points-management">
        <div className="module-header">
          <h3>🎯 积分管理</h3>
          <p>管理您的积分</p>
        </div>
        <div className="connect-prompt">
          <p>请连接钱包以查看积分信息</p>
        </div>
      </div>
    )
  }

  return (
    <div className="points-management">
      <div className="module-header">
        <h3>🎯 积分管理</h3>
        <p>管理您的积分</p>
        <button
          className="refresh-btn"
          onClick={loadPointsData}
          disabled={isLoading}
        >
          {isLoading ? '🔄' : '🔄 刷新'}
        </button>
      </div>

      {/* 拼团积分余额 */}
      <div className="points-balance">
        <div className="balance-item">
          <div className="balance-label">拼团积分</div>
          <div className="balance-value">{pointsData.groupBuyPoints}</div>
          <div className="balance-note">可转账 • 可购物</div>
        </div>
      </div>

      {/* 积分转账功能 */}
      <div className="transfer-section">
        <h4>积分转账</h4>
        <div className="transfer-note">
          <span>拼团积分可以转账给其他用户，用于购物或其他用途</span>
        </div>

        <div className="transfer-form">
          <div className="input-group">
            <input
              type="text"
              placeholder="输入接收方钱包地址"
              value={transferTo}
              onChange={(e) => setTransferTo(e.target.value)}
            />
            <span className="input-suffix">地址</span>
          </div>

          <div className="input-group">
            <input
              type="number"
              placeholder="输入转账积分数量"
              value={transferAmount}
              onChange={(e) => setTransferAmount(e.target.value)}
              min="1"
              max={pointsData.groupBuyPoints}
            />
            <span className="input-suffix">积分</span>
          </div>

          <button
            className="transfer-btn"
            onClick={handleTransfer}
            disabled={isTransferring || !transferAmount || !transferTo || parseInt(transferAmount) < 1}
          >
            {isTransferring ? '转账中...' : '立即转账'}
          </button>
        </div>

        <div className="transfer-info">
          <div className="info-item">
            <span>可转账积分：</span>
            <span>{pointsData.groupBuyPoints}</span>
          </div>
          <div className="info-note">
            * 积分转账功能已开放，请确保接收地址正确
          </div>
        </div>
      </div>
    </div>
  )
}
