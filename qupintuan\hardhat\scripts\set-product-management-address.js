// 通过 Timelock 设置 MerchantManagement 的 ProductManagement 地址
const { ethers, upgrades } = require("hardhat");
require('dotenv').config();

async function main() {
  console.log("🔧 设置 MerchantManagement 的 ProductManagement 地址");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 1. 获取合约地址
  const MERCHANT_MANAGEMENT_ADDRESS = process.env.MERCHANT_MANAGEMENT_ADDRESS;
  const PRODUCT_MANAGEMENT_ADDRESS = process.env.PRODUCT_MANAGEMENT_ADDRESS;
  const TIMELOCK_ADDRESS = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;
  const MULTISIG_ADDRESS = process.env.MULTISIG_WALLET_ADDRESS;

  if (!MERCHANT_MANAGEMENT_ADDRESS || !PRODUCT_MANAGEMENT_ADDRESS || !TIMELOCK_ADDRESS || !MULTISIG_ADDRESS) {
    throw new Error("请在 .env 文件中设置所有必要的地址");
  }

  console.log("📍 MerchantManagement:", MERCHANT_MANAGEMENT_ADDRESS);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT_ADDRESS);
  console.log("📍 Timelock:", TIMELOCK_ADDRESS);
  console.log("📍 多签钱包:", MULTISIG_ADDRESS);

  console.log("\n✅ 检查当前状态");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 2. 检查当前状态
  const merchantManagement = await ethers.getContractAt("MerchantManagement", MERCHANT_MANAGEMENT_ADDRESS);
  
  const currentProductMgmt = await merchantManagement.productManagementAddress();
  const owner = await merchantManagement.owner();
  
  console.log("📋 当前状态:");
  console.log("   - 当前 ProductManagement 地址:", currentProductMgmt);
  console.log("   - 合约所有者:", owner);
  console.log("   - 是否需要设置:", currentProductMgmt === ethers.ZeroAddress ? "✅ 是" : "❌ 否");

  if (currentProductMgmt.toLowerCase() === PRODUCT_MANAGEMENT_ADDRESS.toLowerCase()) {
    console.log("✅ ProductManagement 地址已正确设置，无需操作");
    return;
  }

  console.log("\n✅ 准备 Timelock 调用数据");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 3. 准备调用数据
  const setProductMgmtCalldata = merchantManagement.interface.encodeFunctionData(
    "setProductManagementAddress",
    [PRODUCT_MANAGEMENT_ADDRESS]
  );

  console.log("📋 设置调用数据:");
  console.log("   - 目标合约:", MERCHANT_MANAGEMENT_ADDRESS);
  console.log("   - 函数:", "setProductManagementAddress");
  console.log("   - 参数:", PRODUCT_MANAGEMENT_ADDRESS);

  // 4. 准备 Timelock schedule 调用数据
  const TimelockController = await ethers.getContractFactory("TimelockController");
  const timelock = TimelockController.attach(TIMELOCK_ADDRESS);

  const salt = ethers.keccak256(ethers.toUtf8Bytes(`SetProductManagement-${Date.now()}`));
  const delay = 10 * 60; // 10分钟延迟
  const predecessor = ethers.ZeroHash;

  const scheduleCalldata = timelock.interface.encodeFunctionData("schedule", [
    MERCHANT_MANAGEMENT_ADDRESS,  // target
    0,                            // value
    setProductMgmtCalldata,       // data
    predecessor,                  // predecessor
    salt,                         // salt
    delay                         // delay
  ]);

  console.log("📋 Timelock Schedule 调用数据:");
  console.log("   - 目标合约:", TIMELOCK_ADDRESS);
  console.log("   - 函数:", "schedule");
  console.log("   - 延迟时间:", delay, "秒 (10分钟)");
  console.log("   - Salt:", salt);

  console.log("\n✅ 提交到多签钱包");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 5. 连接多签钱包合约
  const [deployer] = await ethers.getSigners();
  console.log("📍 提交者:", deployer.address);

  const multiSigABI = [
    "function submitTransaction(address to, uint256 value, bytes memory data) external returns (uint256)",
    "function getTransactionCount() external view returns (uint256)",
    "event Submission(uint256 indexed transactionId)"
  ];

  const multiSigContract = new ethers.Contract(MULTISIG_ADDRESS, multiSigABI, deployer);

  // 获取当前交易数量
  const currentTxCount = await multiSigContract.getTransactionCount();
  const proposalTxId = currentTxCount;

  console.log("📋 准备提交多签交易...");
  console.log("   - 目标地址:", TIMELOCK_ADDRESS);
  console.log("   - 调用数据长度:", scheduleCalldata.length, "字符");
  console.log("   - 预期交易ID:", proposalTxId.toString());

  try {
    // 6. 提交多签交易
    console.log("⏳ 提交设置提案到多签钱包...");
    
    const tx = await multiSigContract.submitTransaction(
      TIMELOCK_ADDRESS,    // to: Timelock 合约地址
      0,                   // value: 0 ETH
      scheduleCalldata,    // data: schedule 调用数据
      {
        gasLimit: 500000   // 设置足够的 gas limit
      }
    );

    console.log("📋 交易已提交，等待确认...");
    console.log("   - 交易哈希:", tx.hash);

    const receipt = await tx.wait();
    console.log("✅ 交易已确认");
    console.log("   - 区块号:", receipt.blockNumber);
    console.log("   - Gas 使用量:", receipt.gasUsed.toString());

    // 7. 解析事件获取交易ID
    let actualTransactionId = null;
    for (const log of receipt.logs) {
      try {
        const parsed = multiSigContract.interface.parseLog(log);
        if (parsed.name === "Submission") {
          actualTransactionId = parsed.args.transactionId;
          break;
        }
      } catch (error) {
        // 忽略解析错误
      }
    }

    console.log("📋 多签交易信息:");
    console.log("   - 多签交易ID:", actualTransactionId ? actualTransactionId.toString() : "未获取到");
    console.log("   - 状态: 等待多签所有者确认");

  } catch (error) {
    console.error("❌ 提交多签交易失败:", error.message);
    throw error;
  }

  // 8. 计算执行时间和提案ID
  const currentBlock = await ethers.provider.getBlock('latest');
  const executeTime = currentBlock.timestamp + delay;
  const executeDate = new Date(executeTime * 1000);

  const proposalId = ethers.keccak256(
    ethers.AbiCoder.defaultAbiCoder().encode(
      ["address", "uint256", "bytes", "bytes32", "bytes32"],
      [
        MERCHANT_MANAGEMENT_ADDRESS,
        0,
        setProductMgmtCalldata,
        predecessor,
        salt
      ]
    )
  );

  console.log("\n📋 提案信息");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("   - 提案ID:", proposalId);
  console.log("   - 执行时间:", executeTime, `(${executeDate.toISOString()})`);
  console.log("   - 当前时间:", currentBlock.timestamp, `(${new Date(currentBlock.timestamp * 1000).toISOString()})`);

  // 9. 保存提案信息
  const proposalInfo = {
    timestamp: new Date().toISOString(),
    stage: "set-product-management-address",
    proposalTxId: actualTransactionId ? actualTransactionId.toString() : proposalTxId.toString(),
    transactionHash: tx.hash,
    targetAddress: MERCHANT_MANAGEMENT_ADDRESS,
    productManagementAddress: PRODUCT_MANAGEMENT_ADDRESS,
    timelockAddress: TIMELOCK_ADDRESS,
    multiSigAddress: MULTISIG_ADDRESS,
    salt: salt,
    delay: delay,
    executeTime: executeTime,
    proposalId: proposalId,
    setCalldata: setProductMgmtCalldata,
    scheduleCalldata: scheduleCalldata,
    proposer: deployer.address,
    status: "SUBMITTED",
    description: "设置 MerchantManagement 的 ProductManagement 地址"
  };

  const fs = require('fs');
  fs.writeFileSync(
    './set-product-management-proposal.json',
    JSON.stringify(proposalInfo, null, 2)
  );

  console.log("\n🎉 设置提案提交完成！");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("✅ 提案已提交到多签钱包");
  console.log("✅ 提案信息已保存");
  console.log("⏰ 延迟期：10分钟");
  console.log("📅 可执行时间:", executeDate.toISOString());
  
  console.log("\n📋 下一步操作:");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("1. 🌐 打开前端：个人中心 → 设置管理 → 多签管理");
  console.log("2. 🔍 找到刚创建的设置提案交易");
  console.log("3. ✅ 多签所有者确认交易（需要达到阈值）");
  console.log("4. ⏰ 等待10分钟延迟期");
  console.log("5. 🚀 运行执行脚本设置 ProductManagement 地址");
  
  console.log("\n💾 提案信息已保存到 set-product-management-proposal.json");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 设置 ProductManagement 地址失败:", error);
    process.exit(1);
  });
