# 购买系统重设计方案

## 当前问题分析

### 根本问题
1. **地址验证复杂性**：AddressManagement 合约的地址验证逻辑过于复杂
2. **授权机制问题**：多层授权导致权限验证失败
3. **数据同步问题**：前端和合约的地址数据不同步

### 失败原因
- `getAddress(user, addressId)` 调用在合约内部失败
- 地址ID验证逻辑与实际存储不匹配
- 复杂的权限检查导致意外的访问拒绝

## 解决方案

### 方案一：简化购买流程（已实现）

**特点：**
- 绕过复杂的地址验证
- 使用固定的地址ID (0)
- 简化的错误处理
- 保持现有合约不变

**实现：**
- 创建 `SimplePurchase.jsx` 组件
- 直接调用 `buyProduct(productId, quantity, 0)`
- 移除复杂的地址验证逻辑

### 方案二：重写地址管理系统

如果简化购买仍然失败，建议完全重写：

#### 2.1 新的合约设计

```solidity
// 简化的地址管理
contract SimpleAddressManagement {
    struct UserAddress {
        string name;
        string phone;
        string fullAddress;
        bool isDefault;
    }
    
    mapping(address => UserAddress[]) public userAddresses;
    
    // 简化的获取地址函数 - 无需复杂授权
    function getUserAddress(address user, uint256 index) 
        external view returns (UserAddress memory) {
        require(index < userAddresses[user].length, "Invalid index");
        return userAddresses[user][index];
    }
    
    // 获取用户地址数量
    function getUserAddressCount(address user) 
        external view returns (uint256) {
        return userAddresses[user].length;
    }
}
```

#### 2.2 新的购买合约

```solidity
// 简化的购买逻辑
contract SimpleProductManagement {
    function buyProduct(
        uint256 productId,
        uint256 qty,
        string calldata deliveryAddress
    ) external {
        // 直接使用字符串地址，无需复杂验证
        Product storage p = products[productId];
        require(p.isActive, "Not active");
        require(p.stock >= qty, "Insufficient stock");
        
        uint256 totalPrice = p.price * qty;
        
        // 扣除积分
        pointsManagement.exchangeGoods(msg.sender, totalPrice);
        
        // 更新库存
        p.stock -= qty;
        p.sales += qty;
        
        // 触发购买事件（包含地址信息）
        emit ProductPurchased(
            productId, 
            msg.sender, 
            qty, 
            totalPrice, 
            deliveryAddress
        );
    }
}
```

#### 2.3 前端简化

```javascript
// 简化的购买API
export async function simpleBuyProduct({ 
  productId, 
  quantity, 
  deliveryAddress, 
  signer 
}) {
  const txHash = await writeContract(config, {
    address: contractAddress,
    abi: contractABI,
    functionName: 'buyProduct',
    args: [productId, quantity, deliveryAddress], // 直接传递地址字符串
    account: signer.address,
  });
  
  return await waitForTransactionReceipt(config, { hash: txHash });
}
```

### 方案三：使用事件驱动的地址管理

#### 3.1 概念
- 不在合约中存储复杂的地址结构
- 使用事件记录地址信息
- 前端管理地址数据
- 购买时直接传递地址字符串

#### 3.2 实现步骤

1. **修改 ProductManagement 合约**
   ```solidity
   function buyProduct(
       uint256 productId,
       uint256 qty,
       string calldata shippingInfo
   ) external {
       // 购买逻辑...
       emit ProductPurchased(productId, msg.sender, qty, shippingInfo);
   }
   ```

2. **前端地址管理**
   ```javascript
   // 使用 localStorage 或数据库存储地址
   const userAddresses = JSON.parse(localStorage.getItem('userAddresses') || '[]');
   ```

3. **购买流程**
   ```javascript
   const selectedAddress = userAddresses[selectedIndex];
   const shippingInfo = `${selectedAddress.name}|${selectedAddress.phone}|${selectedAddress.address}`;
   
   await buyProduct({
     productId,
     quantity,
     shippingInfo
   });
   ```

## 推荐实施顺序

### 第一步：测试简化购买
1. 使用已创建的 `SimplePurchase.jsx`
2. 点击"快速购买"按钮测试
3. 如果成功，问题解决

### 第二步：如果仍然失败
1. 检查合约中的具体失败点
2. 考虑重新部署简化的合约
3. 实施方案二或方案三

### 第三步：长期优化
1. 重构整个地址管理系统
2. 简化合约逻辑
3. 提高系统稳定性

## 优势对比

| 方案 | 复杂度 | 稳定性 | 开发时间 | 推荐度 |
|------|--------|--------|----------|--------|
| 简化购买 | 低 | 高 | 1天 | ⭐⭐⭐⭐⭐ |
| 重写合约 | 中 | 很高 | 1周 | ⭐⭐⭐⭐ |
| 事件驱动 | 中 | 高 | 3天 | ⭐⭐⭐ |

## 结论

建议先测试简化购买方案。如果仍然失败，说明问题更深层，需要重写部分合约逻辑。

简化购买的核心思路是：
- **避免复杂的地址验证**
- **使用固定参数**
- **保持购买核心功能**
- **提供稳定的用户体验**
