/* 地址管理组件样式 - 全新设计 */
.address-management {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
  width: 100%;
  max-width: none;
  margin: 0;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40px 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 0;
}

.header-content h3 {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn,
.add-btn {
  padding: 12px 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.add-btn {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  border-color: rgba(255, 255, 255, 0.5);
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.add-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
}

.refresh-btn:disabled,
.add-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 内容区域 */
.address-content {
  background: #f8fafc;
  min-height: 500px;
  padding: 48px;
  width: 100%;
}

/* 连接提示 */
.connect-prompt {
  text-align: center;
  padding: 60px 32px;
  color: #64748b;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 60px 32px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.loading-spinner {
  font-size: 24px;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 80px 40px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.empty-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-container h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.empty-container p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 32px 0;
  line-height: 1.6;
}

.add-first-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-first-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 地址列表 */
.address-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  width: 100%;
}

.address-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.address-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.address-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
}

.address-card:hover::before {
  transform: scaleX(1);
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.address-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.address-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.address-phone {
  font-size: 14px;
  color: #64748b;
}

.default-badge {
  padding: 2px 8px;
  background: #dcfce7;
  color: #166534;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.address-actions {
  display: flex;
  gap: 8px;
}

.edit-btn,
.delete-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #f8fafc;
  color: #475569;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.edit-btn:hover {
  background: #e2e8f0;
}

.delete-btn:hover:not(:disabled) {
  background: #fee2e2;
  border-color: #fca5a5;
}

.address-detail {
  font-size: 14px;
  color: #475569;
  line-height: 1.5;
}

/* 表单弹窗 */
.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.form-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.form-header h4 {
  font-size: 18px;
  font-weight: bold;
  color: #1e293b;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #475569;
}

.form-content {
  padding: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #f8fafc;
  color: #475569;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.cancel-btn:hover {
  background: #e2e8f0;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .address-management {
    border-radius: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
    padding: 24px;
  }

  .header-content h3 {
    font-size: 20px;
  }

  .header-content p {
    font-size: 14px;
  }

  .header-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  .address-content {
    padding: 24px;
  }

  .address-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .address-card {
    padding: 20px;
  }

  .address-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .address-actions {
    align-self: flex-end;
  }
  
  .form-overlay {
    padding: 12px;
  }
  
  .form-content {
    padding: 16px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .form-actions {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .address-management {
    border-radius: 12px;
  }

  .section-header {
    padding: 20px;
  }

  .header-content h3 {
    font-size: 18px;
  }

  .header-content p {
    font-size: 13px;
  }

  .refresh-btn,
  .add-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .address-content {
    padding: 16px;
  }

  .empty-container {
    padding: 60px 20px;
  }

  .empty-icon {
    font-size: 48px;
  }

  .empty-container h3 {
    font-size: 20px;
  }

  .empty-container p {
    font-size: 14px;
  }

  .add-first-btn {
    padding: 14px 24px;
    font-size: 14px;
  }

  .address-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .address-card {
    padding: 16px;
    border-radius: 12px;
  }

  .form-content {
    padding: 12px;
  }

  .form-actions {
    padding: 12px;
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}

/* 地址使用规则样式 */
.address-rules {
  background: white;
  margin: 0 48px 32px 48px;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

.rules-header {
  margin-bottom: 20px;
}

.rules-header h4 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.rule-icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.rule-text {
  flex: 1;
  color: #555;
  line-height: 1.5;
}

.rule-text strong {
  color: #333;
  font-weight: 600;
}

/* 表单提示样式 */
.auto-default-tip {
  color: #52c41a;
  font-size: 12px;
  margin-left: 8px;
  font-weight: 500;
}

.default-address-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  color: #1890ff;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 - 规则说明 */
@media (max-width: 768px) {
  .address-rules {
    margin: 0 20px 24px 20px;
    padding: 20px;
  }

  .rules-header h4 {
    font-size: 16px;
  }

  .rule-item {
    padding: 12px;
    gap: 10px;
  }

  .rule-icon {
    font-size: 18px;
  }

  .rule-text {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .address-rules {
    margin: 0 12px 20px 12px;
    padding: 16px;
  }

  .rules-content {
    gap: 12px;
  }

  .rule-item {
    padding: 10px;
    gap: 8px;
  }
}
