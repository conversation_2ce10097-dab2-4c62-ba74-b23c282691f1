// 检查 buyProduct 的所有要求
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";
const MERCHANT_MANAGEMENT = "0x13a311f1d52376861207ae641278f6e5de55F4B5";
const USER_ADDRESS = "0xA2dD965E6AAE7Bea5cd24CfC05653B8c72c2F9EF";

// ABI
const PRODUCT_MANAGEMENT_ABI = [
  {
    "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "name": "products",
    "outputs": [
      {"internalType": "uint256", "name": "productId", "type": "uint256"},
      {"internalType": "address", "name": "merchant", "type": "address"},
      {"internalType": "string", "name": "name", "type": "string"},
      {"internalType": "string", "name": "description", "type": "string"},
      {"internalType": "uint256", "name": "price", "type": "uint256"},
      {"internalType": "uint256", "name": "stock", "type": "uint256"},
      {"internalType": "bool", "name": "isActive", "type": "bool"},
      {"internalType": "uint256", "name": "sales", "type": "uint256"},
      {"internalType": "uint256", "name": "createTime", "type": "uint256"},
      {"internalType": "uint256", "name": "updateTime", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

const MERCHANT_MANAGEMENT_ABI = [
  {
    "inputs": [{"internalType": "address", "name": "merchant", "type": "address"}],
    "name": "isMerchant",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

async function checkBuyProductRequirements() {
  console.log("🔍 检查 buyProduct 的所有要求...");
  console.log("👤 用户地址:", USER_ADDRESS);
  console.log("🛒 商品ID: 1");
  console.log("📦 购买数量: 1");
  console.log("📍 地址ID: 0");

  try {
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 检查商品信息
    console.log("\n🔍 步骤1：检查商品信息...");
    const product = await publicClient.readContract({
      address: PRODUCT_MANAGEMENT,
      abi: PRODUCT_MANAGEMENT_ABI,
      functionName: 'products',
      args: [1]
    });

    console.log("商品信息:", {
      productId: product[0].toString(),
      merchant: product[1],
      name: product[2],
      description: product[3],
      price: product[4].toString(),
      stock: product[5].toString(),
      isActive: product[6],
      sales: product[7].toString()
    });

    // 检查商品状态
    const isActive = product[6];
    const stock = Number(product[5]);
    const merchant = product[1];

    console.log("\n📋 商品状态检查:");
    console.log("1. 商品是否激活:", isActive ? "✅ 是" : "❌ 否");
    console.log("2. 库存是否充足:", stock >= 1 ? `✅ 是 (${stock} >= 1)` : `❌ 否 (${stock} < 1)`);

    if (!isActive) {
      console.log("❌ 商品未激活，这是购买失败的原因！");
      return;
    }

    if (stock < 1) {
      console.log("❌ 库存不足，这是购买失败的原因！");
      return;
    }

    // 2. 检查商家状态
    console.log("\n🔍 步骤2：检查商家状态...");
    const isMerchant = await publicClient.readContract({
      address: MERCHANT_MANAGEMENT,
      abi: MERCHANT_MANAGEMENT_ABI,
      functionName: 'isMerchant',
      args: [merchant]
    });

    console.log("3. 商家是否存在:", isMerchant ? "✅ 是" : "❌ 否");
    console.log("   商家地址:", merchant);

    if (!isMerchant) {
      console.log("❌ 商家不存在或未激活，这是购买失败的原因！");
      return;
    }

    // 3. 总结
    console.log("\n📊 buyProduct 要求检查总结:");
    console.log("1. 商品激活:", isActive ? "✅" : "❌");
    console.log("2. 库存充足:", stock >= 1 ? "✅" : "❌");
    console.log("3. 商家存在:", isMerchant ? "✅" : "❌");

    if (isActive && stock >= 1 && isMerchant) {
      console.log("\n🤔 所有基本要求都满足，但购买仍然失败...");
      console.log("💡 问题可能在于：");
      console.log("   1. AddressManagement.getAddress() 调用失败");
      console.log("   2. PointsManagement.exchangeGoods() 调用失败");
      console.log("   3. 其他未知的合约内部问题");
      
      console.log("\n🔧 建议解决方案：");
      console.log("   1. 检查 PointsManagement 是否授权 ProductManagement");
      console.log("   2. 检查用户积分余额是否足够");
      console.log("   3. 检查合约之间的依赖关系");
    } else {
      console.log("\n❌ 发现问题！某些基本要求不满足");
    }

  } catch (error) {
    console.error("❌ 检查过程失败:", error.message);
  }
}

// 运行检查
checkBuyProductRequirements().catch(console.error);
