// src/apis/orderApi.js
// 订单管理相关 API

import { createPublicClient, createWalletClient, http, custom } from 'viem';
import { bscTestnet } from 'viem/chains';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';

// 创建公共客户端
const publicClient = createPublicClient({
  chain: bscTestnet,
  transport: http()
});

/**
 * 获取商家订单列表
 * @param {Object} params - 参数对象
 * @param {string} params.merchantAddress - 商家地址
 * @returns {Promise<Array>} - 订单列表
 */
export async function getMerchantOrders({ merchantAddress }) {
  try {
    console.log('🔍 [getMerchantOrders] 获取商家订单列表:', merchantAddress);

    // 1. 获取订单ID列表
    const orderIds = await publicClient.readContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'getMerchantOrders',
      args: [merchantAddress]
    });

    console.log('📋 [getMerchantOrders] 订单ID列表:', orderIds);

    if (!orderIds || orderIds.length === 0) {
      return [];
    }

    // 2. 批量获取订单详细信息
    const orderInfos = await publicClient.readContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'getOrderInfoBatch',
      args: [orderIds]
    });

    console.log('📋 [getMerchantOrders] 订单详细信息:', orderInfos);

    // 3. 格式化订单数据
    const formattedOrders = orderInfos.map(order => ({
      orderId: Number(order.orderId),
      buyer: order.buyer,
      merchant: order.merchant,
      productId: Number(order.productId),
      quantity: Number(order.quantity),
      totalPrice: Number(order.totalPrice),
      addressId: Number(order.addressId),
      status: Number(order.status), // 0: Pending, 1: Shipped, 2: Delivered, 3: Completed
      trackingNumber: order.trackingNumber,
      shippingCompany: order.shippingCompany,
      createTime: Number(order.createTime),
      shippedTime: Number(order.shippedTime),
      deliveredTime: Number(order.deliveredTime)
    }));

    console.log('✅ [getMerchantOrders] 格式化后的订单:', formattedOrders);
    return formattedOrders;

  } catch (error) {
    console.error('❌ [getMerchantOrders] 获取商家订单失败:', error);
    throw new Error(`获取订单列表失败: ${error.message}`);
  }
}

/**
 * 获取买家订单列表
 * @param {Object} params - 参数对象
 * @param {string} params.buyerAddress - 买家地址
 * @returns {Promise<Array>} - 订单列表
 */
export async function getBuyerOrders({ buyerAddress }) {
  try {
    console.log('🔍 [getBuyerOrders] 获取买家订单列表:', buyerAddress);

    // 1. 获取订单ID列表
    const orderIds = await publicClient.readContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'getBuyerOrders',
      args: [buyerAddress]
    });

    if (!orderIds || orderIds.length === 0) {
      return [];
    }

    // 2. 批量获取订单详细信息
    const orderInfos = await publicClient.readContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'getOrderInfoBatch',
      args: [orderIds]
    });

    // 3. 格式化订单数据
    const formattedOrders = orderInfos.map(order => ({
      orderId: Number(order.orderId),
      buyer: order.buyer,
      merchant: order.merchant,
      productId: Number(order.productId),
      quantity: Number(order.quantity),
      totalPrice: Number(order.totalPrice),
      addressId: Number(order.addressId),
      status: Number(order.status),
      trackingNumber: order.trackingNumber,
      shippingCompany: order.shippingCompany,
      createTime: Number(order.createTime),
      shippedTime: Number(order.shippedTime),
      deliveredTime: Number(order.deliveredTime)
    }));

    console.log('✅ [getBuyerOrders] 买家订单列表:', formattedOrders);
    return formattedOrders;

  } catch (error) {
    console.error('❌ [getBuyerOrders] 获取买家订单失败:', error);
    throw new Error(`获取订单列表失败: ${error.message}`);
  }
}

/**
 * 商家发货
 * @param {Object} params - 参数对象
 * @param {number} params.orderId - 订单ID
 * @param {string} params.trackingNumber - 快递单号
 * @param {string} params.shippingCompany - 快递公司
 * @param {Object} params.signer - 签名者对象
 * @returns {Promise<Object>} - 交易结果
 */
export async function shipOrder({ orderId, trackingNumber, shippingCompany, signer }) {
  try {
    console.log('🚀 [shipOrder] 开始发货流程:', {
      orderId,
      trackingNumber,
      shippingCompany,
      signerAddress: signer.address
    });

    // 创建钱包客户端
    const walletClient = createWalletClient({
      chain: bscTestnet,
      transport: custom(window.ethereum)
    });

    // 发送交易
    const hash = await walletClient.writeContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'shipOrder',
      args: [BigInt(orderId), trackingNumber, shippingCompany],
      account: signer.address
    });

    console.log('📋 [shipOrder] 交易已发送:', hash);

    // 等待交易确认
    const receipt = await publicClient.waitForTransactionReceipt({
      hash,
      timeout: 60000
    });

    console.log('✅ [shipOrder] 发货成功:', receipt);
    return { receipt, txHash: hash };

  } catch (error) {
    console.error('❌ [shipOrder] 发货失败:', error);
    throw new Error(`发货失败: ${error.message}`);
  }
}

/**
 * 更新订单状态
 * @param {Object} params - 参数对象
 * @param {number} params.orderId - 订单ID
 * @param {number} params.newStatus - 新状态 (0: Pending, 1: Shipped, 2: Delivered, 3: Completed)
 * @param {Object} params.signer - 签名者对象
 * @returns {Promise<Object>} - 交易结果
 */
export async function updateOrderStatus({ orderId, newStatus, signer }) {
  try {
    console.log('🔄 [updateOrderStatus] 更新订单状态:', {
      orderId,
      newStatus,
      signerAddress: signer.address
    });

    // 创建钱包客户端
    const walletClient = createWalletClient({
      chain: bscTestnet,
      transport: custom(window.ethereum)
    });

    // 发送交易
    const hash = await walletClient.writeContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'updateOrderStatus',
      args: [BigInt(orderId), newStatus],
      account: signer.address
    });

    console.log('📋 [updateOrderStatus] 交易已发送:', hash);

    // 等待交易确认
    const receipt = await publicClient.waitForTransactionReceipt({
      hash,
      timeout: 60000
    });

    console.log('✅ [updateOrderStatus] 状态更新成功:', receipt);
    return { receipt, txHash: hash };

  } catch (error) {
    console.error('❌ [updateOrderStatus] 状态更新失败:', error);
    throw new Error(`状态更新失败: ${error.message}`);
  }
}

/**
 * 获取单个订单详细信息
 * @param {Object} params - 参数对象
 * @param {number} params.orderId - 订单ID
 * @returns {Promise<Object>} - 订单信息
 */
export async function getOrderInfo({ orderId }) {
  try {
    console.log('🔍 [getOrderInfo] 获取订单详情:', orderId);

    const orderInfo = await publicClient.readContract({
      address: CONTRACT_ADDRESSES[97].OrderManagement,
      abi: ABIS.OrderManagement,
      functionName: 'getOrderInfo',
      args: [BigInt(orderId)]
    });

    const formattedOrder = {
      orderId: Number(orderInfo.orderId),
      buyer: orderInfo.buyer,
      merchant: orderInfo.merchant,
      productId: Number(orderInfo.productId),
      quantity: Number(orderInfo.quantity),
      totalPrice: Number(orderInfo.totalPrice),
      addressId: Number(orderInfo.addressId),
      status: Number(orderInfo.status),
      trackingNumber: orderInfo.trackingNumber,
      shippingCompany: orderInfo.shippingCompany,
      createTime: Number(orderInfo.createTime),
      shippedTime: Number(orderInfo.shippedTime),
      deliveredTime: Number(orderInfo.deliveredTime)
    };

    console.log('✅ [getOrderInfo] 订单详情:', formattedOrder);
    return formattedOrder;

  } catch (error) {
    console.error('❌ [getOrderInfo] 获取订单详情失败:', error);
    throw new Error(`获取订单详情失败: ${error.message}`);
  }
}

// 订单状态常量
export const ORDER_STATUS = {
  PENDING: 0,
  SHIPPED: 1,
  DELIVERED: 2,
  COMPLETED: 3
};

// 订单状态文本映射
export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.PENDING]: '待发货',
  [ORDER_STATUS.SHIPPED]: '已发货',
  [ORDER_STATUS.DELIVERED]: '已送达',
  [ORDER_STATUS.COMPLETED]: '已完成'
};

// 订单状态颜色映射
export const ORDER_STATUS_COLOR = {
  [ORDER_STATUS.PENDING]: '#faad14',
  [ORDER_STATUS.SHIPPED]: '#1890ff',
  [ORDER_STATUS.DELIVERED]: '#52c41a',
  [ORDER_STATUS.COMPLETED]: '#722ed1'
};
