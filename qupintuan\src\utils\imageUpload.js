/**
 * 统一图片上传工具
 * 支持多种存储方案：Cloudinary、IPFS、本地存储
 */

import { uploadToCloudinary } from './cloudinaryUpload.js';
import { uploadToIPFS, getIPFSImageUrl } from './ipfsUpload.js';

// 存储方案配置
const STORAGE_METHODS = {
  CLOUDINARY: 'cloudinary',
  IPFS: 'ipfs',
  LOCAL: 'local'
};

// 默认存储方案（按优先级）
const DEFAULT_STORAGE_ORDER = [
  STORAGE_METHODS.CLOUDINARY,
  STORAGE_METHODS.IPFS,
  STORAGE_METHODS.LOCAL
];

/**
 * 智能图片上传 - 自动选择最佳存储方案
 * @param {File} file - 要上传的图片文件
 * @param {Object} options - 上传选项
 * @returns {Promise<Object>} 上传结果
 */
export const uploadImage = async (file, options = {}) => {
  try {
    // 开始智能图片上传

    // 验证文件
    if (!file) {
      throw new Error('请选择要上传的文件');
    }

    if (!file.type.startsWith('image/')) {
      throw new Error('只支持图片文件上传');
    }

    // 获取存储方案顺序
    const storageOrder = options.storageOrder || DEFAULT_STORAGE_ORDER;

    // 按优先级尝试不同的存储方案
    for (const method of storageOrder) {
      try {
        // 尝试使用存储方案上传
        
        let result;
        switch (method) {
          case STORAGE_METHODS.CLOUDINARY:
            result = await uploadToCloudinary(file, options);
            break;
            
          case STORAGE_METHODS.IPFS:
            const hash = await uploadToIPFS(file);
            result = {
              success: true,
              url: getIPFSImageUrl(hash),
              hash: hash,
              method: STORAGE_METHODS.IPFS
            };
            break;
            
          case STORAGE_METHODS.LOCAL:
            result = await uploadToLocalStorage(file);
            break;
            
          default:
            throw new Error(`不支持的存储方案: ${method}`);
        }

        if (result.success) {
          // 上传成功
          return {
            ...result,
            method: method,
            originalFile: {
              name: file.name,
              size: file.size,
              type: file.type
            }
          };
        }

      } catch (error) {
        console.warn(`⚠️ [imageUpload] ${method} 上传失败:`, error.message);
        continue;
      }
    }

    // 所有方案都失败，返回错误
    throw new Error('所有存储方案都失败，请检查网络连接或配置');

  } catch (error) {
    console.error('❌ [imageUpload] 图片上传失败:', error);
    throw error;
  }
};

/**
 * 批量图片上传
 * @param {FileList|Array} files - 文件列表
 * @param {Object} options - 上传选项
 * @returns {Promise<Array>} 上传结果数组
 */
export const uploadMultipleImages = async (files, options = {}) => {
  try {
    const fileArray = Array.from(files);
    console.log(`🚀 [imageUpload] 开始批量上传 ${fileArray.length} 个图片`);

    const uploadPromises = fileArray.map((file, index) => 
      uploadImage(file, {
        ...options,
        batchIndex: index
      })
    );

    const results = await Promise.allSettled(uploadPromises);
    
    const successResults = [];
    const failedResults = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successResults.push(result.value);
      } else {
        failedResults.push({
          index,
          filename: fileArray[index].name,
          error: result.reason.message
        });
      }
    });

    console.log(`✅ [imageUpload] 批量上传完成: ${successResults.length} 成功, ${failedResults.length} 失败`);

    return {
      success: successResults,
      failed: failedResults,
      total: fileArray.length
    };

  } catch (error) {
    console.error('❌ [imageUpload] 批量上传失败:', error);
    throw error;
  }
};

/**
 * 本地存储上传（备用方案）
 * @param {File} file - 文件
 * @returns {Promise<Object>} 上传结果
 */
const uploadToLocalStorage = async (file) => {
  try {
    // 将文件转换为 Base64
    const base64 = await fileToBase64(file);
    
    // 生成唯一ID
    const fileId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    
    // 存储到 localStorage
    const imageData = {
      id: fileId,
      name: file.name,
      type: file.type,
      size: file.size,
      data: base64,
      uploadTime: new Date().toISOString()
    };

    localStorage.setItem(`qupintuan_image_${fileId}`, JSON.stringify(imageData));

    // 本地存储成功

    return {
      success: true,
      url: base64, // 直接返回 base64 作为 URL
      id: fileId,
      method: STORAGE_METHODS.LOCAL
    };

  } catch (error) {
    // 本地存储失败
    throw error;
  }
};

/**
 * 将文件转换为 Base64
 * @param {File} file - 文件
 * @returns {Promise<string>} Base64 字符串
 */
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * 检查存储方案可用性
 * @returns {Promise<Object>} 各存储方案的可用性
 */
export const checkStorageAvailability = async () => {
  const availability = {};

  // 检查 Cloudinary
  try {
    const cloudinaryConfig = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME && 
                            import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET;
    availability.cloudinary = !!cloudinaryConfig;
  } catch {
    availability.cloudinary = false;
  }

  // 检查 IPFS (Pinata)
  try {
    const pinataConfig = import.meta.env.VITE_PINATA_JWT;
    availability.ipfs = !!pinataConfig;
  } catch {
    availability.ipfs = false;
  }

  // 本地存储总是可用
  availability.local = true;

  console.log('📊 [imageUpload] 存储方案可用性:', availability);
  return availability;
};

/**
 * 获取推荐的存储方案
 * @returns {Promise<string>} 推荐的存储方案
 */
export const getRecommendedStorage = async () => {
  const availability = await checkStorageAvailability();
  
  if (availability.cloudinary) {
    return STORAGE_METHODS.CLOUDINARY;
  } else if (availability.ipfs) {
    return STORAGE_METHODS.IPFS;
  } else {
    return STORAGE_METHODS.LOCAL;
  }
};

// 导出常量
export { STORAGE_METHODS };

// 默认导出
export default {
  uploadImage,
  uploadMultipleImages,
  checkStorageAvailability,
  getRecommendedStorage,
  STORAGE_METHODS
};
