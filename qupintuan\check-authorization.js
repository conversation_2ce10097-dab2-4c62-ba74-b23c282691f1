// 检查 ProductManagement 是否已被 AddressManagement 授权
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const ADDRESS_MANAGEMENT = "0xA27C195F6e80Dd8742a3beaD3e4871f31C813102";
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";

// AddressManagement ABI（只需要我们用到的函数）
const ADDRESS_MANAGEMENT_ABI = [
  {
    "inputs": [{"internalType": "address", "name": "", "type": "address"}],
    "name": "authorizedContracts",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "contractAddr", "type": "address"}],
    "name": "isAuthorizedContract",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  }
];

async function checkAuthorization() {
  console.log("🔍 检查 ProductManagement 合约授权状态...");
  console.log("📍 AddressManagement:", ADDRESS_MANAGEMENT);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT);

  try {
    // 创建公共客户端
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 方法1：直接查询 authorizedContracts 映射
    console.log("\n🔍 方法1：查询 authorizedContracts 映射...");
    const isAuthorized1 = await publicClient.readContract({
      address: ADDRESS_MANAGEMENT,
      abi: ADDRESS_MANAGEMENT_ABI,
      functionName: 'authorizedContracts',
      args: [PRODUCT_MANAGEMENT]
    });
    console.log("结果:", isAuthorized1);

    // 方法2：调用 isAuthorizedContract 函数
    console.log("\n🔍 方法2：调用 isAuthorizedContract 函数...");
    const isAuthorized2 = await publicClient.readContract({
      address: ADDRESS_MANAGEMENT,
      abi: ADDRESS_MANAGEMENT_ABI,
      functionName: 'isAuthorizedContract',
      args: [PRODUCT_MANAGEMENT]
    });
    console.log("结果:", isAuthorized2);

    // 总结
    console.log("\n📊 授权状态总结:");
    console.log("方法1 (authorizedContracts):", isAuthorized1 ? "✅ 已授权" : "❌ 未授权");
    console.log("方法2 (isAuthorizedContract):", isAuthorized2 ? "✅ 已授权" : "❌ 未授权");

    if (isAuthorized1 && isAuthorized2) {
      console.log("\n🎉 ProductManagement 已被正确授权！");
      console.log("💡 购买功能应该可以正常工作");
    } else {
      console.log("\n❌ ProductManagement 未被授权！");
      console.log("💡 这就是购买失败的原因");
      console.log("🔧 需要运行授权脚本来解决此问题");
    }

  } catch (error) {
    console.error("❌ 检查授权状态失败:", error.message);
  }
}

// 运行检查
checkAuthorization().catch(console.error);
