// test-authorization.js
// 简单测试授权状态

const { createPublicClient, http } = require('viem');
const { bscTestnet } = require('viem/chains');

async function main() {
  console.log('🔍 测试授权状态...');

  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  });

  const addressManagementAddress = '0xA27C195F6e80Dd8742a3beaD3e4871f31C813102';
  const productManagementAddress = '0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A';

  // 简单的 ABI，只包含我们需要的函数
  const abi = [
    {
      "inputs": [{"type": "address", "name": ""}],
      "name": "authorizedContracts",
      "outputs": [{"type": "bool", "name": ""}],
      "stateMutability": "view",
      "type": "function"
    }
  ];

  try {
    const isAuthorized = await publicClient.readContract({
      address: addressManagementAddress,
      abi: abi,
      functionName: 'authorizedContracts',
      args: [productManagementAddress]
    });

    console.log('✅ 授权状态:', isAuthorized);
    console.log('📍 AddressManagement:', addressManagementAddress);
    console.log('📍 ProductManagement:', productManagementAddress);

    if (!isAuthorized) {
      console.log('❌ ProductManagement 未被授权！');
      console.log('💡 需要运行授权脚本');
    } else {
      console.log('✅ ProductManagement 已被授权');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

main().catch(console.error);
