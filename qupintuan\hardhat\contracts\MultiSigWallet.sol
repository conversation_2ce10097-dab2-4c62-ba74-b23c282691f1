// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "./StorageValidator.sol";

/**
 * @title MultiSigWallet
 * @dev 多签钱包合约，用于管理关键操作 - UUPS可升级版本
 * @notice 需要多个签名者确认才能执行交易
 */
contract MultiSigWallet is
    Initializable,
    UUPSUpgradeable,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    StorageValidator
{
    // 事件
    event Deposit(address indexed sender, uint256 amount, uint256 balance);
    event SubmitTransaction(
        address indexed owner,
        uint256 indexed txIndex,
        address indexed to,
        uint256 value,
        bytes data
    );
    event ConfirmTransaction(address indexed owner, uint256 indexed txIndex);
    event RevokeConfirmation(address indexed owner, uint256 indexed txIndex);
    event ExecuteTransaction(address indexed owner, uint256 indexed txIndex);

    // 状态变量
    address[] public owners;
    mapping(address => bool) public isOwner;
    uint256 public numConfirmationsRequired;
    address public timelock;

    struct Transaction {
        address to;
        uint256 value;
        bytes data;
        bool executed;
        uint256 numConfirmations;
    }

    // 交易ID => 所有者 => 是否确认
    mapping(uint256 => mapping(address => bool)) public confirmations;
    Transaction[] public transactions;

    // 修饰符
    modifier onlyMultisigOwner() {
        require(isOwner[msg.sender], "not owner");
        _;
    }

    modifier txExists(uint256 _txIndex) {
        require(_txIndex < transactions.length, "tx does not exist");
        _;
    }

    modifier notExecuted(uint256 _txIndex) {
        require(!transactions[_txIndex].executed, "tx already executed");
        _;
    }

    modifier notConfirmed(uint256 _txIndex) {
        require(!confirmations[_txIndex][msg.sender], "tx already confirmed");
        _;
    }

    /**
     * @dev 初始化函数
     * @param _owners 所有者地址数组
     * @param _numConfirmationsRequired 需要的确认数量
     * @param _timelock Timelock合约地址
     */
    function initialize(
        address[] memory _owners,
        uint256 _numConfirmationsRequired,
        address _timelock
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(_owners.length > 0, "owners required");
        require(
            _numConfirmationsRequired > 0 &&
                _numConfirmationsRequired <= _owners.length,
            "invalid number of required confirmations"
        );
        require(_timelock != address(0), "invalid timelock address");

        for (uint256 i = 0; i < _owners.length; i++) {
            address owner = _owners[i];

            require(owner != address(0), "invalid owner");
            require(!isOwner[owner], "owner not unique");

            isOwner[owner] = true;
            owners.push(owner);
        }

        numConfirmationsRequired = _numConfirmationsRequired;
        timelock = _timelock;

        // 初始化紧急暂停相关变量
        emergencyPaused = false;
        emergencyVoteCount = 0;
    }

    /**
     * @dev 接收ETH/BNB
     */
    receive() external payable {
        emit Deposit(msg.sender, msg.value, address(this).balance);
    }

    /**
     * @dev 提交交易
     * @param _to 目标地址
     * @param _value 转账金额
     * @param _data 调用数据
     */
    function submitTransaction(
        address _to,
        uint256 _value,
        bytes memory _data
    ) public onlyMultisigOwner {
        uint256 txIndex = transactions.length;

        transactions.push(
            Transaction({
                to: _to,
                value: _value,
                data: _data,
                executed: false,
                numConfirmations: 0
            })
        );

        emit SubmitTransaction(msg.sender, txIndex, _to, _value, _data);
    }

    /**
     * @dev 确认交易
     * @param _txIndex 交易索引
     */
    function confirmTransaction(uint256 _txIndex)
        public
        onlyMultisigOwner
        txExists(_txIndex)
        notExecuted(_txIndex)
        notConfirmed(_txIndex)
    {
        Transaction storage transaction = transactions[_txIndex];
        transaction.numConfirmations += 1;
        confirmations[_txIndex][msg.sender] = true;

        emit ConfirmTransaction(msg.sender, _txIndex);
    }

    /**
     * @dev 执行交易
     * @param _txIndex 交易索引
     */
    function executeTransaction(uint256 _txIndex)
        public
        onlyMultisigOwner
        txExists(_txIndex)
        notExecuted(_txIndex)
        notPaused
    {
        Transaction storage transaction = transactions[_txIndex];

        require(
            transaction.numConfirmations >= numConfirmationsRequired,
            "cannot execute tx"
        );

        transaction.executed = true;

        (bool success, ) = transaction.to.call{value: transaction.value}(
            transaction.data
        );
        require(success, "tx failed");

        emit ExecuteTransaction(msg.sender, _txIndex);
    }

    /**
     * @dev 撤销确认
     * @param _txIndex 交易索引
     */
    function revokeConfirmation(uint256 _txIndex)
        public
        onlyMultisigOwner
        txExists(_txIndex)
        notExecuted(_txIndex)
    {
        Transaction storage transaction = transactions[_txIndex];

        require(confirmations[_txIndex][msg.sender], "tx not confirmed");

        transaction.numConfirmations -= 1;
        confirmations[_txIndex][msg.sender] = false;

        emit RevokeConfirmation(msg.sender, _txIndex);
    }

    /**
     * @dev 获取所有者列表
     */
    function getOwners() public view returns (address[] memory) {
        return owners;
    }

    /**
     * @dev 获取交易数量
     */
    function getTransactionCount() public view returns (uint256) {
        return transactions.length;
    }

    /**
     * @dev 获取交易详情
     * @param _txIndex 交易索引
     */
    function getTransaction(uint256 _txIndex)
        public
        view
        returns (
            address to,
            uint256 value,
            bytes memory data,
            bool executed,
            uint256 numConfirmations
        )
    {
        Transaction storage transaction = transactions[_txIndex];

        return (
            transaction.to,
            transaction.value,
            transaction.data,
            transaction.executed,
            transaction.numConfirmations
        );
    }

    /**
     * @dev 检查交易是否已确认
     * @param _txIndex 交易索引
     * @param _owner 所有者地址
     */
    function isTransactionConfirmed(uint256 _txIndex, address _owner)
        public
        view
        returns (bool)
    {
        return confirmations[_txIndex][_owner];
    }

    /**
     * @dev 获取交易的确认数量
     * @param _txIndex 交易索引
     */
    function getConfirmationCount(uint256 _txIndex)
        public
        view
        returns (uint256)
    {
        require(_txIndex < transactions.length, "tx does not exist");
        return transactions[_txIndex].numConfirmations;
    }

    /**
     * @dev 检查交易是否已获得足够确认
     * @param _txIndex 交易索引
     */
    function isConfirmed(uint256 _txIndex)
        public
        view
        returns (bool)
    {
        require(_txIndex < transactions.length, "tx does not exist");
        return transactions[_txIndex].numConfirmations >= numConfirmationsRequired;
    }

    /**
     * @dev 检查特定所有者是否已确认交易
     * @param _txIndex 交易索引
     * @param _owner 所有者地址
     */
    function isConfirmedBy(uint256 _txIndex, address _owner)
        public
        view
        returns (bool)
    {
        require(_txIndex < transactions.length, "tx does not exist");
        return confirmations[_txIndex][_owner];
    }

    /**
     * @dev 获取待执行的交易列表
     */
    function getPendingTransactions()
        public
        view
        returns (uint256[] memory)
    {
        uint256[] memory tempPending = new uint256[](transactions.length);
        uint256 pendingCount = 0;

        for (uint256 i = 0; i < transactions.length; i++) {
            if (!transactions[i].executed &&
                transactions[i].numConfirmations >= numConfirmationsRequired) {
                tempPending[pendingCount] = i;
                pendingCount++;
            }
        }

        uint256[] memory pending = new uint256[](pendingCount);
        for (uint256 i = 0; i < pendingCount; i++) {
            pending[i] = tempPending[i];
        }

        return pending;
    }

    /**
     * @dev 批量确认交易（节省gas）
     * @param _txIndexes 交易索引数组
     */
    function batchConfirmTransactions(uint256[] memory _txIndexes)
        public
        onlyMultisigOwner
    {
        for (uint256 i = 0; i < _txIndexes.length; i++) {
            uint256 txIndex = _txIndexes[i];
            if (txIndex < transactions.length &&
                !transactions[txIndex].executed &&
                !confirmations[txIndex][msg.sender]) {

                transactions[txIndex].numConfirmations += 1;
                confirmations[txIndex][msg.sender] = true;
                emit ConfirmTransaction(msg.sender, txIndex);
            }
        }
    }

    /**
     * @dev 紧急暂停功能（需要所有所有者确认）
     */
    bool public emergencyPaused;
    mapping(address => bool) public emergencyVotes;
    uint256 public emergencyVoteCount;

    function emergencyPause() public onlyMultisigOwner {
        require(!emergencyPaused, "already paused");
        require(!emergencyVotes[msg.sender], "already voted");

        emergencyVotes[msg.sender] = true;
        emergencyVoteCount++;

        if (emergencyVoteCount >= owners.length) {
            emergencyPaused = true;
        }
    }

    function emergencyUnpause() public onlyMultisigOwner {
        require(emergencyPaused, "not paused");

        // 重置投票
        for (uint256 i = 0; i < owners.length; i++) {
            emergencyVotes[owners[i]] = false;
        }
        emergencyVoteCount = 0;
        emergencyPaused = false;
    }

    modifier notPaused() {
        require(!emergencyPaused, "contract is paused");
        _;
    }

    /**
     * @dev 更新Timelock地址
     * @param _newTimelock 新的Timelock地址
     */
    function updateTimelock(address _newTimelock) external {
        require(_newTimelock != address(0), "invalid timelock address");
        require(msg.sender == address(this), "only multisig can update");
        timelock = _newTimelock;
    }

    /**
     * @dev 授权升级函数 - 只有timelock可以升级
     * @param newImplementation 新实现合约地址
     */
    function _authorizeUpgrade(address newImplementation) internal override {
        require(msg.sender == timelock, "only timelock can upgrade");
    }

    /**
     * @dev 实现StorageValidator抽象函数 - 验证存储布局
     */
    function validateStorageLayout() public view override returns (bool) {
        // 验证关键存储变量
        return owners.length > 0 &&
               numConfirmationsRequired > 0 &&
               numConfirmationsRequired <= owners.length;
    }

    /**
     * @dev 实现StorageValidator抽象函数 - 计算存储校验和
     */
    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            owners.length,
            numConfirmationsRequired,
            transactions.length,
            timelock
        ));
    }

    /**
     * @dev 实现StorageValidator抽象函数 - 紧急存储修复
     */
    function emergencyStorageFix() external override onlyOwner whenPaused {
        // 多签合约的紧急修复逻辑
        // 这里可以添加特定的修复逻辑
        emit StorageFixed(address(this), "MultiSigWallet emergency fix executed");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    /**
     * @dev 存储间隙，为未来升级预留空间
     */
    uint256[50] private __gap;
}
