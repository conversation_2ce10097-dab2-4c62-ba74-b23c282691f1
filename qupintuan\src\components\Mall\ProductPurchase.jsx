// src/components/Mall/ProductPurchase.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { buyProduct, buyProductWithDefaultAddress } from '@/apis/mallApi';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';
import { fixPriceDisplay, needsPriceFix } from '@/utils/priceDisplayFix';
import AddressSelector from './AddressSelector';
// import { getUserPoints } from '@/apis/pointsApi';
import './ProductPurchase.css';

export default function ProductPurchase({ product, onPurchaseSuccess, onClose }) {
  const { address: account } = useAccount();

  // 状态管理
  const [quantity, setQuantity] = useState(1);
  const [userPoints, setUserPoints] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [userAddresses, setUserAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [currentDefaultAddress, setCurrentDefaultAddress] = useState(null);
  const [showAddressSelector, setShowAddressSelector] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);

  // 价格格式化函数 - 使用系统的价格修复工具
  const formatPrice = (price) => {
    if (needsPriceFix(price)) {
      return fixPriceDisplay(price);
    }

    if (typeof price === 'number') {
      // 如果是整数，不显示小数点
      if (price % 1 === 0) {
        return price.toString();
      }
      return price.toFixed(2);
    }
    return price;
  };

  // 获取修复后的价格用于计算
  // 注意：product.price 可能已经在 API 层面被修复过了，所以要小心处理
  let fixedPrice;
  if (typeof product.price === 'number') {
    // 如果已经是数字，直接使用
    fixedPrice = product.price;
  } else if (needsPriceFix(product.price)) {
    // 如果需要修复，则修复
    fixedPrice = parseFloat(fixPriceDisplay(product.price));
  } else {
    // 否则转换为数字
    fixedPrice = parseFloat(product.price) || 0;
  }

  // 调试价格信息
  console.log('🔍 [ProductPurchase] 价格信息:', {
    originalPrice: product.price,
    originalPriceType: typeof product.price,
    needsPriceFix: needsPriceFix(product.price),
    fixedPrice,
    fixedPriceType: typeof fixedPrice
  });

  // 计算总价
  const totalPrice = fixedPrice * quantity;
  const canAfford = userPoints >= totalPrice;
  const maxQuantity = Math.min(product.stock, Math.floor(userPoints / fixedPrice));

  // 调试信息
  const buttonDisabled = isPurchasing || !canAfford || quantity <= 0 || quantity > maxQuantity;
  console.log('🔍 [ProductPurchase] 购买条件检查:', {
    userPoints,
    fixedPrice,
    quantity,
    totalPrice,
    canAfford,
    maxQuantity,
    productStock: product.stock,
    isPurchasing,
    buttonDisabled,
    disableReasons: {
      isPurchasing,
      notCanAfford: !canAfford,
      invalidQuantity: quantity <= 0,
      exceedsMaxQuantity: quantity > maxQuantity
    }
  });

  // 加载用户积分
  const loadUserPoints = async () => {
    if (!account) return;

    console.log('🔍 [ProductPurchase] 开始加载用户积分, account:', account);
    setIsLoading(true);
    try {
      // 查询真实的用户积分余额
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts');
      const { createPublicClient, http } = await import('viem');
      const { bscTestnet } = await import('viem/chains');

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const pointsAddress = CONTRACT_ADDRESSES[97].PointsManagement;
      console.log('📍 [ProductPurchase] 积分合约地址:', pointsAddress);

      // 查询用户的拼团积分（用于购买商品）
      const groupBuyPoints = await publicClient.readContract({
        address: pointsAddress,
        abi: ABIS.PointsManagement,
        functionName: 'groupBuyPointsNonExchangeable',
        args: [account]
      });

      console.log('💰 [ProductPurchase] 原始积分数据:', groupBuyPoints);

      // 积分系统使用6位精度
      const { formatUnits } = await import('viem');
      const pointsBalance = parseFloat(formatUnits(groupBuyPoints, 6));

      console.log('✅ [ProductPurchase] 解析后积分余额:', pointsBalance);
      setUserPoints(pointsBalance);
    } catch (error) {
      console.error('❌ [ProductPurchase] 加载积分失败:', error);
      toast.error(`加载积分失败: ${error.message}`);
      setUserPoints(0); // 出错时设置为0
    } finally {
      setIsLoading(false);
    }
  };

  // 加载用户收货地址
  const loadUserAddresses = async () => {
    if (!account) {
      setUserAddresses([]);
      return;
    }

    console.log('🔍 [ProductPurchase] 开始加载用户收货地址, account:', account);
    setIsLoadingAddresses(true);

    let addressesLoaded = false;

    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement;

      // 尝试使用新的 getMyAddresses 函数
      try {
        const addresses = await publicClient.readContract({
          address: addressAddress,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        });
        setUserAddresses(addresses || []);
        console.log('✅ [ProductPurchase] 加载收货地址成功 (新函数):', addresses);
        console.log('🔍 [ProductPurchase] 地址获取详情:', {
          contractAddress: addressAddress,
          userAccount: account,
          addressCount: addresses ? addresses.length : 0,
          functionUsed: 'getMyAddresses'
        });

        // 如果有地址，自动选择默认地址或第一个地址
        if (addresses && addresses.length > 0) {
          console.log('🔍 [ProductPurchase] 地址详细信息:', addresses.map((addr, index) => ({
            index,
            addressId: addr.addressId,
            addressIdType: typeof addr.addressId,
            isDefault: addr.isDefault,
            name: addr.name,
            fullAddress: `${addr.province} ${addr.city} ${addr.district} ${addr.detailAddress}`
          })));

          const defaultAddress = addresses.find(addr => addr.isDefault);
          const rawAddressId = defaultAddress ? defaultAddress.addressId : addresses[0].addressId;
          // 尝试不同的数据类型转换
          let selectedId;
          if (typeof rawAddressId === 'bigint') {
            selectedId = Number(rawAddressId); // 先尝试 Number
          } else {
            selectedId = rawAddressId;
          }
          setSelectedAddressId(selectedId);
          console.log('✅ [ProductPurchase] 自动选择地址ID:', {
            rawAddressId,
            selectedId,
            rawType: typeof rawAddressId,
            selectedType: typeof selectedId,
            rawValue: rawAddressId.toString(),
            selectedValue: selectedId.toString()
          });
        }
        addressesLoaded = true;
      } catch (newFunctionError) {
        console.log('⚠️ [ProductPurchase] 新函数失败，尝试旧函数:', newFunctionError.message);

        // 如果新函数不存在，回退到旧函数
        try {
          const addresses = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getUserAddresses',
            args: [account]
          });
          setUserAddresses(addresses || []);
          console.log('✅ [ProductPurchase] 加载收货地址成功 (旧函数):', addresses);

          // 如果有地址，自动选择默认地址或第一个地址
          if (addresses && addresses.length > 0) {
            const defaultAddress = addresses.find(addr => addr.isDefault);
            const rawAddressId = defaultAddress ? defaultAddress.addressId : addresses[0].addressId;
            // 将 BigInt 转换为数字
            const selectedId = typeof rawAddressId === 'bigint' ? Number(rawAddressId) : rawAddressId;
            setSelectedAddressId(selectedId);
            console.log('✅ [ProductPurchase] 自动选择地址ID (旧函数):', { rawAddressId, selectedId, type: typeof selectedId });
          }
          addressesLoaded = true;
        } catch (authError) {
          console.warn('❌ [ProductPurchase] 旧函数也失败:', authError);
        }
      }
    } catch (error) {
      console.error('❌ [ProductPurchase] 地址加载过程中发生错误:', error);
    } finally {
      // 只有在地址加载失败时才清空状态
      if (!addressesLoaded) {
        console.warn('❌ [ProductPurchase] 地址加载完全失败，清空状态');
        setUserAddresses([]);
        setSelectedAddressId(null);
      }

      setIsLoadingAddresses(false);
      console.log('🔍 [ProductPurchase] 地址加载流程结束, 成功:', addressesLoaded);
    }
  };

  // 处理地址变化
  const handleAddressChange = (defaultAddress) => {
    console.log('🔄 [ProductPurchase] 默认地址变化:', defaultAddress);
    setCurrentDefaultAddress(defaultAddress);

    if (defaultAddress) {
      const addressId = typeof defaultAddress.addressId === 'bigint' ? Number(defaultAddress.addressId) : defaultAddress.addressId;
      setSelectedAddressId(addressId);
    }
  };

  // 处理购买
  const handlePurchase = async () => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    if (quantity <= 0 || quantity > product.stock) {
      toast.error('购买数量无效');
      return;
    }

    if (!canAfford) {
      toast.error('积分余额不足');
      return;
    }

    // 验证收货地址（使用默认地址）
    if (!currentDefaultAddress) {
      toast.error('请先设置默认收货地址才能购买商品');
      setShowAddressSelector(true);
      return;
    }

    console.log('🚀 [ProductPurchase] 开始购买流程（使用默认地址）');
    console.log('📋 [ProductPurchase] 当前默认地址:', {
      name: currentDefaultAddress.name,
      phone: currentDefaultAddress.phone,
      address: `${currentDefaultAddress.province} ${currentDefaultAddress.city} ${currentDefaultAddress.district} ${currentDefaultAddress.detail}`
    });

    setIsPurchasing(true);
    try {

      // 创建 signer
      const signer = {
        account: { address: account },
        address: account
      };

      console.log('📞 [ProductPurchase] 准备调用 buyProductWithDefaultAddress API，参数:', {
        productId: product.productId,
        quantity,
        signerAddress: signer.address,
        defaultAddress: currentDefaultAddress.name
      });

      // 直接调用新的默认地址购买函数
      const result = await buyProductWithDefaultAddress({
        productId: product.productId,
        quantity,
        signer
      });

      console.log('✅ [ProductPurchase] buyProductWithDefaultAddress API 调用成功，结果:', result);



      toast.success(`🎉 趣拼团购买成功！消耗 ${formatPrice(totalPrice)} 积分 - 愛拼才会赢！`, {
        duration: 4000,
        position: 'top-center',
      });

      // 通知父组件购买成功
      if (onPurchaseSuccess) {
        onPurchaseSuccess(result);
      }

      // 关闭购买弹窗
      if (onClose) {
        onClose();
      }

    } catch (error) {
      console.error('❌ [ProductPurchase] 购买过程中发生错误:', error);
      console.error('❌ [ProductPurchase] 错误详情:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      let errorMessage = error.message;
      if (error.message.includes('Insufficient points')) {
        errorMessage = '积分余额不足';
      } else if (error.message.includes('Insufficient stock')) {
        errorMessage = '商品库存不足';
      } else if (error.message.includes('Product not active')) {
        errorMessage = '商品已下架';
      } else if (error.message.includes('请先注册代理系统')) {
        errorMessage = '请先注册代理系统才能购买商品';
      }

      console.error('❌ [ProductPurchase] 处理后的错误消息:', errorMessage);
      toast.error(`购买失败: ${errorMessage}`);
    } finally {
      console.log('🏁 [ProductPurchase] 购买流程结束，设置 isPurchasing = false');
      setIsPurchasing(false);
    }
  };

  // 监控 selectedAddressId 的变化
  useEffect(() => {
    console.log('🔍 [ProductPurchase] selectedAddressId 变化:', {
      selectedAddressId,
      type: typeof selectedAddressId,
      userAddressesCount: userAddresses.length
    });
  }, [selectedAddressId, userAddresses]);

  // 监控 isPurchasing 状态变化
  useEffect(() => {
    console.log('🔍 [ProductPurchase] isPurchasing 状态变化:', isPurchasing);
  }, [isPurchasing]);

  // 组件挂载时加载积分
  useEffect(() => {
    console.log('🔄 [ProductPurchase] 组件挂载或账户变化，重新加载数据, account:', account);
    loadUserPoints();
    loadUserAddresses();
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  // 组件挂载/卸载监控
  useEffect(() => {
    console.log('🎯 [ProductPurchase] 组件挂载');
    // 组件挂载时确保 isPurchasing 状态为 false
    if (isPurchasing) {
      console.log('⚠️ [ProductPurchase] 组件挂载时发现 isPurchasing 为 true，重置为 false');
      setIsPurchasing(false);
    }
    return () => {
      console.log('🎯 [ProductPurchase] 组件卸载');
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="product-purchase">
      <div className="purchase-overlay" onClick={onClose}></div>

      <div className="purchase-modal">
        <div className="modal-header">
          <h3>🛒 购买商品</h3>
          <button className="close-btn" onClick={onClose}>✕</button>
        </div>

        <div className="modal-content">
          {/* 商品信息 */}
          <div className="product-summary">
            <div className="product-image">
              {product.images && product.images.length > 0 ? (
                <img
                  src={product.images[0].startsWith('http')
                    ? product.images[0]
                    : `https://ipfs.io/ipfs/${product.images[0]}`}
                  alt={product.name}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="placeholder-image">📦</div>
              )}
            </div>

            <div className="product-details">
              <h4 className="product-name">{product.name}</h4>
              <p className="product-description">
                {product.description.length > 100
                  ? `${product.description.substring(0, 100)}...`
                  : product.description
                }
              </p>
              <div className="product-meta">
                <span className="product-price">{formatPrice(product.price)} 积分/件</span>
                <span className="product-stock">库存: {product.stock} 件</span>
              </div>
            </div>
          </div>

          {/* 用户积分信息 */}
          <div className="user-points">
            <div className="points-info">
              <span className="points-label">我的积分:</span>
              <span className="points-value">
                {isLoading ? '加载中...' : `${userPoints} 积分`}
              </span>
            </div>
            {!canAfford && (
              <div className="insufficient-notice">
                ⚠️ 积分余额不足，无法购买
              </div>
            )}
          </div>

          {/* 收货地址选择 */}
          <div className="address-section">
            <div className="address-label">收货地址:</div>
            {isLoadingAddresses ? (
              <div className="address-loading">加载地址中...</div>
            ) : userAddresses.length === 0 ? (
              <div className="no-address">
                <div className="no-address-notice">
                  ⚠️ 您还没有添加收货地址
                </div>
                <div className="no-address-hint">
                  请先到个人中心添加收货地址才能购买商品
                </div>
              </div>
            ) : (
              <div className="address-selector">
                {userAddresses.map((address) => {
                  // 确保地址ID类型一致
                  const addressId = typeof address.addressId === 'bigint' ? Number(address.addressId) : address.addressId;
                  return (
                    <div
                      key={address.addressId}
                      className={`address-option ${selectedAddressId === addressId ? 'selected' : ''}`}
                      onClick={() => setSelectedAddressId(addressId)}
                  >
                    <div className="address-header">
                      <span className="address-name">{address.name}</span>
                      <span className="address-phone">{address.phone}</span>
                      {address.isDefault && <span className="default-tag">默认</span>}
                    </div>
                    <div className="address-detail">
                      {address.province} {address.city} {address.district} {address.detail}
                    </div>
                  </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* 购买数量选择 */}
          <div className="quantity-section">
            <div className="quantity-label">购买数量:</div>
            <div className="quantity-controls">
              <button
                className="quantity-btn"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                -
              </button>
              <input
                type="number"
                value={quantity}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 1;
                  setQuantity(Math.max(1, Math.min(maxQuantity, value)));
                }}
                min="1"
                max={maxQuantity}
                className="quantity-input"
              />
              <button
                className="quantity-btn"
                onClick={() => setQuantity(Math.min(maxQuantity, quantity + 1))}
                disabled={quantity >= maxQuantity}
              >
                +
              </button>
            </div>
            <div className="quantity-hint">
              最多可购买 {maxQuantity} 件
            </div>
          </div>

          {/* 价格计算 */}
          <div className="price-calculation">
            <div className="calc-row">
              <span>单价:</span>
              <span>{formatPrice(product.price)} 积分</span>
            </div>
            <div className="calc-row">
              <span>数量:</span>
              <span>{quantity} 件</span>
            </div>
            <div className="calc-row total">
              <span>总计:</span>
              <span className="total-price">{formatPrice(totalPrice)} 积分</span>
            </div>
          </div>
        </div>

        {/* 地址选择器 */}
        {showAddressSelector && (
          <div className="address-selector-section">
            <AddressSelector
              onAddressChange={handleAddressChange}
              className="purchase-address-selector"
            />
          </div>
        )}

        {/* 当前默认地址显示 */}
        {currentDefaultAddress && !showAddressSelector && (
          <div className="current-address-display">
            <h5>📍 收货地址</h5>
            <div className="address-info">
              <div className="address-header">
                <span className="recipient-name">{currentDefaultAddress.name}</span>
                <span className="recipient-phone">{currentDefaultAddress.phone}</span>
                <span className="default-badge">默认</span>
              </div>
              <div className="address-detail">
                {currentDefaultAddress.province} {currentDefaultAddress.city} {currentDefaultAddress.district} {currentDefaultAddress.detail}
              </div>
              <button
                className="btn-change-address"
                onClick={() => setShowAddressSelector(true)}
              >
                更换地址
              </button>
            </div>
          </div>
        )}

        <div className="modal-actions">
          <button
            className="cancel-btn"
            onClick={onClose}
            disabled={isPurchasing}
          >
            取消
          </button>
          <button
            className="purchase-btn"
            onClick={handlePurchase}
            disabled={isPurchasing || !canAfford || quantity <= 0 || quantity > maxQuantity || !currentDefaultAddress}
          >
            {isPurchasing ? '⏳ 购买中...' :
             !currentDefaultAddress ? '请先设置默认地址' :
             `💰 确认购买 (${formatPrice(totalPrice)} 积分)`}
          </button>
        </div>

        {/* 购买说明 */}
        <div className="purchase-notice">
          <h5>📋 购买说明</h5>
          <div className="notice-items">
            <div className="notice-item">• 购买成功后积分立即扣除，无法撤销</div>
            <div className="notice-item">• 系统将自动使用您的默认收货地址发货</div>
            <div className="notice-item">• 如需更换收货地址，请先在个人中心设置新的默认地址</div>
          </div>


        </div>
      </div>
    </div>
  );
}
