// src/utils/imageValidation.js
// 图片验证工具函数

/**
 * 图片规格标准配置
 */
export const IMAGE_STANDARDS = {
  // 文件大小限制 (字节)
  maxSize: 5 * 1024 * 1024,    // 5MB
  minSize: 10 * 1024,          // 10KB
  
  // 尺寸限制 (像素)
  maxWidth: 2000,
  maxHeight: 2000,
  minWidth: 300,
  minHeight: 300,
  
  // 推荐尺寸
  recommended: {
    main: { width: 800, height: 800 },      // 主图
    detail: { width: 1200, height: 1200 },  // 详情图
    thumb: { width: 300, height: 300 }      // 缩略图
  },
  
  // 格式限制
  allowedFormats: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
  
  // 数量限制（优化Gas费用）
  maxImages: 5,
  minImages: 1,
  
  // 质量建议
  quality: {
    main: 85,      // 主图质量
    detail: 90,    // 详情图质量
    thumb: 75      // 缩略图质量
  }
};

/**
 * 图片规格说明文本
 */
export const IMAGE_GUIDELINES = {
  title: "📸 商品图片规格要求",
  requirements: [
    {
      category: "📏 尺寸要求",
      items: [
        "推荐尺寸：800×800像素（1:1正方形比例）",
        "最小尺寸：300×300像素",
        "最大尺寸：2000×2000像素",
        "详情图可使用1200×1200像素获得更好效果"
      ]
    },
    {
      category: "📦 文件要求",
      items: [
        "支持格式：JPEG、PNG、WebP",
        "文件大小：10KB - 5MB",
        "推荐大小：主图≤150KB，详情图≤300KB",
        "图片数量：1-5张（优化Gas费用）"
      ]
    },
    {
      category: "🎨 质量建议",
      items: [
        "使用高清晰度图片展示商品细节",
        "确保图片光线充足，色彩真实",
        "避免过度压缩导致画质模糊",
        "建议使用纯色背景突出商品"
      ]
    },
    {
      category: "⚡ 性能优化",
      items: [
        "系统会自动压缩和优化图片",
        "WebP格式加载速度更快",
        "合理的图片大小提升用户体验",
        "支持响应式显示适配各种设备"
      ]
    }
  ],
  tips: [
    "💡 提示：上传前系统会自动检查图片规格",
    "🔧 建议：使用专业图片编辑软件优化图片质量",
    "📱 注意：考虑移动端用户的加载速度",
    "🎯 目标：平衡图片质量与加载性能"
  ]
};

/**
 * 验证单个图片文件
 * @param {File} file - 图片文件
 * @returns {Promise<{valid: boolean, error?: string, warnings?: string[], info?: object}>}
 */
export const validateImageFile = async (file) => {
  const result = {
    valid: false,
    error: null,
    warnings: [],
    info: {}
  };

  try {
    // 1. 检查文件类型
    if (!IMAGE_STANDARDS.allowedFormats.includes(file.type)) {
      result.error = `不支持的文件格式。支持格式：${IMAGE_STANDARDS.allowedFormats.join(', ')}`;
      return result;
    }

    // 2. 检查文件大小
    if (file.size < IMAGE_STANDARDS.minSize) {
      result.error = `文件太小。最小大小：${formatFileSize(IMAGE_STANDARDS.minSize)}`;
      return result;
    }

    if (file.size > IMAGE_STANDARDS.maxSize) {
      result.error = `文件太大。最大大小：${formatFileSize(IMAGE_STANDARDS.maxSize)}`;
      return result;
    }

    // 3. 检查图片尺寸
    const dimensions = await getImageDimensions(file);
    result.info.dimensions = dimensions;
    result.info.fileSize = file.size;
    result.info.fileType = file.type;

    if (dimensions.width < IMAGE_STANDARDS.minWidth || dimensions.height < IMAGE_STANDARDS.minHeight) {
      result.error = `图片尺寸太小。最小尺寸：${IMAGE_STANDARDS.minWidth}×${IMAGE_STANDARDS.minHeight}像素`;
      return result;
    }

    if (dimensions.width > IMAGE_STANDARDS.maxWidth || dimensions.height > IMAGE_STANDARDS.maxHeight) {
      result.error = `图片尺寸太大。最大尺寸：${IMAGE_STANDARDS.maxWidth}×${IMAGE_STANDARDS.maxHeight}像素`;
      return result;
    }

    // 4. 生成警告和建议
    const aspectRatio = dimensions.width / dimensions.height;
    if (Math.abs(aspectRatio - 1) > 0.1) {
      result.warnings.push("建议使用1:1正方形比例的图片以获得最佳显示效果");
    }

    if (file.size > 300 * 1024) {
      result.warnings.push("文件较大，建议压缩以提升加载速度");
    }

    if (dimensions.width < IMAGE_STANDARDS.recommended.main.width) {
      result.warnings.push(`建议使用${IMAGE_STANDARDS.recommended.main.width}×${IMAGE_STANDARDS.recommended.main.height}像素以获得更好的显示效果`);
    }

    // 5. 验证通过
    result.valid = true;
    return result;

  } catch (error) {
    result.error = `图片验证失败：${error.message}`;
    return result;
  }
};

/**
 * 验证多个图片文件
 * @param {FileList|File[]} files - 图片文件列表
 * @returns {Promise<{valid: boolean, results: object[], totalErrors: number, totalWarnings: number}>}
 */
export const validateImageFiles = async (files) => {
  const fileArray = Array.from(files);
  
  // 检查数量限制
  if (fileArray.length < IMAGE_STANDARDS.minImages) {
    return {
      valid: false,
      error: `至少需要${IMAGE_STANDARDS.minImages}张图片`,
      results: []
    };
  }

  if (fileArray.length > IMAGE_STANDARDS.maxImages) {
    return {
      valid: false,
      error: `最多只能上传${IMAGE_STANDARDS.maxImages}张图片`,
      results: []
    };
  }

  // 验证每个文件
  const results = await Promise.all(
    fileArray.map(async (file, index) => {
      const validation = await validateImageFile(file);
      return {
        index,
        file,
        ...validation
      };
    })
  );

  const totalErrors = results.filter(r => !r.valid).length;
  const totalWarnings = results.reduce((sum, r) => sum + (r.warnings?.length || 0), 0);

  return {
    valid: totalErrors === 0,
    results,
    totalErrors,
    totalWarnings,
    summary: {
      total: fileArray.length,
      valid: results.filter(r => r.valid).length,
      invalid: totalErrors,
      warnings: totalWarnings
    }
  };
};

/**
 * 获取图片尺寸
 * @param {File} file - 图片文件
 * @returns {Promise<{width: number, height: number}>}
 */
const getImageDimensions = (file) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('无法读取图片尺寸'));
    };
    
    img.src = url;
  });
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * 生成图片预览信息
 * @param {object} validation - 验证结果
 * @returns {string} 预览信息文本
 */
export const generateImagePreviewInfo = (validation) => {
  if (!validation.info) return '';
  
  const { dimensions, fileSize, fileType } = validation.info;
  const sizeText = formatFileSize(fileSize);
  const typeText = fileType.split('/')[1].toUpperCase();
  
  return `${dimensions.width}×${dimensions.height} • ${sizeText} • ${typeText}`;
};
