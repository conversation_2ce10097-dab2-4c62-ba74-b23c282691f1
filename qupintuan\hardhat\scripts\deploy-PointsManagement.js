// scripts/deploy-PointsManagement.js
// 部署可升级的 PointsManagement 合约 v2.0 (完整初始化版本)
require("dotenv").config();
const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🚀 部署 PointsManagement v1.0 (标准部署)");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取部署者
    const signers = await ethers.getSigners();
    if (!signers || signers.length === 0) {
      throw new Error("没有找到签名者，请检查 .env 文件中的 PRIVATE_KEY 配置");
    }

    const deployer = signers[0];
    console.log("📝 部署者地址:", deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

    // 2. 读取环境变量
    const usdtTokenAddress = process.env.USDT_TOKEN_ADDRESS;
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;
    const marketplaceAddr = process.env.PRODUCT_MANAGEMENT_ADDRESS;
    const merchantManagerAddr = process.env.MERCHANT_MANAGEMENT_ADDRESS;
    const groupBuyRoomAddr = process.env.GROUPBUY_ROOM_ADDRESS;

    console.log("\n📋 初始化参数:");
    console.log("   • USDT Token:", usdtTokenAddress);
    console.log("   • Timelock:", timelockAddr);
    console.log("   • Marketplace (ProductManagement):", marketplaceAddr || "❌ 未配置");
    console.log("   • MerchantManager:", merchantManagerAddr || "❌ 未配置");
    console.log("   • GroupBuyRoom:", groupBuyRoomAddr || "❌ 未配置");

    // 3. 验证必需参数
    if (!usdtTokenAddress) {
      throw new Error("请在 .env 文件中设置 USDT_TOKEN_ADDRESS");
    }
    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
    }

    // 4. 检查是否为最小化部署模式
    const isMinimalDeploy = !marketplaceAddr || !merchantManagerAddr || !groupBuyRoomAddr;

    if (isMinimalDeploy) {
      console.log("\n⚠️ 检测到最小化部署模式:");
      console.log("   • 部分依赖合约地址未配置");
      console.log("   • 将使用临时地址进行初始化");
      console.log("   • 需要后续运行配置脚本完成设置");
    }

    // 5. 获取合约工厂
    const PointsManagement = await ethers.getContractFactory("PointsManagement");

    // 6. 准备初始化参数
    const tempAddress = "******************************************";
    const finalMarketplaceAddr = marketplaceAddr || tempAddress;
    const finalMerchantManagerAddr = merchantManagerAddr || tempAddress;
    const finalGroupBuyRoomAddr = groupBuyRoomAddr || tempAddress;

    // 7. 部署可升级合约
    console.log("\n⏳ 部署 PointsManagement 代理合约...");
    const pointsManagement = await upgrades.deployProxy(
      PointsManagement,
      [
        usdtTokenAddress,
        timelockAddr,
        finalMarketplaceAddr,
        finalMerchantManagerAddr,
        finalGroupBuyRoomAddr
      ],
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    await pointsManagement.waitForDeployment();
    const proxyAddress = await pointsManagement.getAddress();

    console.log("✅ PointsManagement 代理合约部署成功");
    console.log("   📍 代理地址:", proxyAddress);

    // 6. 获取实现合约地址
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("   🏗️ 实现合约地址:", implementationAddress);

    // 7. 验证初始化配置
    console.log("\n🔍 验证初始化配置:");
    const isConfigured = await pointsManagement.isSystemConfigured();
    console.log("   • 系统配置状态:", isConfigured ? "✅ 完整配置" : "❌ 配置不完整");

    if (isConfigured) {
      const config = await pointsManagement.getSystemConfiguration();
      console.log("   • USDT Token:", config._usdtToken);
      console.log("   • Timelock:", config._timelock);
      console.log("   • Marketplace:", config._marketplaceAddress);
      console.log("   • MerchantManager:", config._merchantManager);
      console.log("   • GroupBuyRoom:", config._groupBuyRoomAddress);
    }

    // 8. 检查合约所有者
    const owner = await pointsManagement.owner();
    console.log("\n🔒 权限信息:");
    console.log("   • 当前所有者:", owner);
    console.log("   • 部署者:", deployer.address);
    console.log("   • 所有权匹配:", owner === deployer.address ? "✅" : "❌");

    // 9. 暂时保留所有权给部署者（用于后续配置操作）
    console.log("\n⚠️ 暂时保留所有权给部署者...");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("• 当前所有者:", owner);
    console.log("• 目标所有者:", timelockAddr);
    console.log("• 状态: 等待配置操作完成后转移");

    console.log("\n💡 重要提醒:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("• 所有权暂时保留给部署者");
    console.log("• 完成配置操作后需要手动转移所有权");
    console.log("• 运行以下脚本完成所有权转移:");
    console.log("  npx hardhat run scripts/transfer-PointsManagement-ownership.js --network bscTestnet");

    // 10. 测试BNB接收功能
    console.log("\n💸 测试BNB接收功能...");
    const initialBalance = await pointsManagement.getBNBBalance();
    console.log("💰 初始BNB余额:", ethers.formatEther(initialBalance), "BNB");

    // 发送测试BNB
    console.log("💸 发送测试BNB到合约...");
    const testTx = await deployer.sendTransaction({
      to: proxyAddress,
      value: ethers.parseEther("0.01") // 0.01 BNB
    });

    await testTx.wait();
    console.log("📋 测试转账哈希:", testTx.hash);

    // 检查转账后余额
    const newBalance = await pointsManagement.getBNBBalance();
    console.log("💰 转账后BNB余额:", ethers.formatEther(newBalance), "BNB");

    if (newBalance > initialBalance) {
      console.log("🎉 BNB接收功能测试成功!");
    } else {
      console.log("⚠️ BNB接收功能可能有问题");
    }

    console.log(`\n🎉 PointsManagement v1.0 部署完成！`);
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    if (isMinimalDeploy) {
      console.log("✅ 基础配置已完成（USDT + Timelock）");
      console.log("⚠️ 依赖合约地址使用临时值");
      console.log("📝 需要后续配置完整功能");
    } else {
      console.log("✅ 所有依赖已在初始化时配置");
      console.log("✅ 无需手动配置步骤");
      console.log("✅ 系统可立即使用");
    }
    console.log("✅ 合约由 Timelock 安全控制");

    console.log("\n📋 v1.0 功能特性:");
    console.log("   • ✅ UUPS升级模式");
    console.log("   • ✅ BNB接收功能");
    console.log("   • ✅ 积分管理功能");
    console.log("   • ✅ 暂停/恢复功能");
    console.log("   • ✅ Timelock权限控制");
    console.log("   • ✅ 灵活的初始化模式");

    console.log("\n🔧 环境变量更新:");
    console.log(`POINTS_MANAGEMENT_ADDRESS=${proxyAddress}`);

    // 11. 保存部署信息
    const deploymentInfo = {
      proxyAddress,
      implementationAddress,
      deployer: deployer.address,
      owner: timelockAddr,
      initializationParameters: {
        usdtToken: usdtTokenAddress,
        timelock: timelockAddr,
        marketplace: marketplaceAddr || tempAddress + " (临时)",
        merchantManager: merchantManagerAddr || tempAddress + " (临时)",
        groupBuyRoom: groupBuyRoomAddr || tempAddress + " (临时)"
      },
      isFullyConfigured: isConfigured && !isMinimalDeploy,
      needsConfiguration: isMinimalDeploy,
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      version: "v1.0.0",
      features: [
        "UUPS_UPGRADEABLE",
        "MINIMAL_INITIALIZATION",
        "REQUIRES_CONFIGURATION",
        "TIMELOCK_PROTECTED",
        "BNB_RECEIVE"
      ],
      securityLevel: "HIGH",
      status: isMinimalDeploy ? "DEPLOYED_NEEDS_CONFIGURATION" : "FULLY_DEPLOYED_AND_CONFIGURED"
    };

    const fs = require('fs');
    fs.writeFileSync(
      './pointsmanagement-deployment.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("\n💾 部署信息已保存到 pointsmanagement-deployment.json");

    // 12. 更新合约地址配置
    try {
      const contractAddresses = JSON.parse(fs.readFileSync('../contract-addresses.json', 'utf8'));
      contractAddresses["97"].PointsManagement = proxyAddress;

      fs.writeFileSync(
        '../contract-addresses.json',
        JSON.stringify(contractAddresses, null, 2)
      );
      console.log("📝 contract-addresses.json 已更新");
    } catch (error) {
      console.log("⚠️ 无法更新 contract-addresses.json:", error.message);
    }

    if (isMinimalDeploy) {
      console.log("\n🔧 下一步操作:");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("1. 更新 .env 文件中的 POINTS_MANAGEMENT_ADDRESS");
      console.log("2. 部署其他依赖合约:");
      console.log("   - MerchantManagement");
      console.log("   - ProductManagement");
      console.log("   - GroupBuyRoom");
      console.log("3. 运行配置脚本:");
      console.log("   npx hardhat run scripts/configure-PointsManagement.js --network bscTestnet");

      console.log("\n⚠️ 重要提醒:");
      console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
      console.log("• 当前合约使用临时地址，功能受限");
      console.log("• 必须完成配置后才能正常使用");
      console.log("• 配置需要通过Timelock执行");
    } else {
      console.log("\n🧪 建议测试:");
      console.log("   1. 验证商城购买功能");
      console.log("   2. 测试积分生成和兑换");
      console.log("   3. 检查所有权限配置");
    }

  } catch (error) {
    console.error("❌ 部署过程中发生错误:", error);

    if (error.message.includes("missing required parameter")) {
      console.log("\n💡 解决建议:");
      console.log("   • 检查 .env 文件中的所有必需地址");
      console.log("   • 确保所有依赖合约已正确部署");
    } else if (error.message.includes("Invalid")) {
      console.log("\n💡 解决建议:");
      console.log("   • 验证所有合约地址格式正确");
      console.log("   • 确认合约地址对应正确的合约");
    } else if (error.message.includes("insufficient funds")) {
      console.log("\n💡 解决建议:");
      console.log("   • 部署者账户BNB余额不足");
    }

    process.exit(1);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});