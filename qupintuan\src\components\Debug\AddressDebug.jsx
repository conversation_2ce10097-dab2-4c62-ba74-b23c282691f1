// src/components/Debug/AddressDebug.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

export default function AddressDebug() {
  const { address: account } = useAccount();
  const [debugInfo, setDebugInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const debugAddresses = async () => {
    if (!account) {
      alert('请先连接钱包');
      return;
    }

    setIsLoading(true);
    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement;
      const productAddress = CONTRACT_ADDRESSES[97].ProductManagement;

      console.log('🔍 开始调试地址信息...');

      // 1. 获取用户地址
      const addresses = await publicClient.readContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'getMyAddresses',
        args: [],
        account
      });

      // 2. 检查授权状态
      const isAuthorized = await publicClient.readContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'authorizedContracts',
        args: [productAddress]
      });

      // 3. 尝试获取第一个地址（如果存在）
      let firstAddressDetails = null;
      if (addresses && addresses.length > 0) {
        try {
          // 注意：这个调用可能会失败，因为前端不是授权的合约
          firstAddressDetails = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getAddress',
            args: [account, 0],
            account
          });
        } catch (error) {
          firstAddressDetails = { error: error.message };
        }
      }

      const info = {
        userAccount: account,
        addressCount: addresses ? addresses.length : 0,
        addresses: addresses || [],
        isProductManagementAuthorized: isAuthorized,
        firstAddressDetails,
        contractAddresses: {
          AddressManagement: addressAddress,
          ProductManagement: productAddress
        }
      };

      setDebugInfo(info);
      console.log('🔍 调试信息:', info);

    } catch (error) {
      console.error('❌ 调试失败:', error);
      setDebugInfo({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>地址调试工具</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={debugAddresses} 
          disabled={!account || isLoading}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: account && !isLoading ? 'pointer' : 'not-allowed'
          }}
        >
          {isLoading ? '调试中...' : '调试地址信息'}
        </button>
      </div>

      {!account && (
        <div style={{ color: 'red' }}>请先连接钱包</div>
      )}

      {debugInfo && (
        <div style={{ marginTop: '20px' }}>
          <h4>调试结果:</h4>
          <pre style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px'
          }}>
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
          
          {debugInfo.error && (
            <div style={{ color: 'red', marginTop: '10px' }}>
              错误: {debugInfo.error}
            </div>
          )}
          
          {debugInfo.addressCount === 0 && !debugInfo.error && (
            <div style={{ color: 'orange', marginTop: '10px' }}>
              ⚠️ 用户没有地址记录，需要先添加收货地址
            </div>
          )}
          
          {debugInfo.addressCount > 0 && (
            <div style={{ color: 'green', marginTop: '10px' }}>
              ✅ 用户有 {debugInfo.addressCount} 个地址
            </div>
          )}
          
          {debugInfo.isProductManagementAuthorized === false && (
            <div style={{ color: 'red', marginTop: '10px' }}>
              ❌ ProductManagement 未被授权访问 AddressManagement
            </div>
          )}
          
          {debugInfo.isProductManagementAuthorized === true && (
            <div style={{ color: 'green', marginTop: '10px' }}>
              ✅ ProductManagement 已被授权访问 AddressManagement
            </div>
          )}
        </div>
      )}
    </div>
  );
}
