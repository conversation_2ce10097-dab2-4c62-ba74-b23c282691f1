// src/components/Common/IPFSImageUpload/index.jsx
import React, { useState, useRef, useCallback, useImperativeHandle } from 'react';
import { toast } from 'react-hot-toast';
import { uploadToIPFS, uploadMultipleToIPFS, getIPFSImageUrl } from '@/utils/ipfsUpload';
import { validateImageFile, IMAGE_STANDARDS } from '@/utils/imageValidation';
import './index.css';

const IPFSImageUpload = React.forwardRef(({
  maxImages = IMAGE_STANDARDS.maxImages,
  onImagesChange,
  onUploadProgress,
  onUploadComplete,
  onUploadError,
  initialImages = [],
  disabled = false,
  showPreview = true,
  allowDragDrop = true,
  uploadOptions = {},
  previewOnly = false, // 仅预览模式，不自动上传
  autoUpload = true    // 是否自动上传
}, ref) => {
  // 状态管理
  const [images, setImages] = useState(initialImages);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [dragOver, setDragOver] = useState(false);
  
  const fileInputRef = useRef(null);

  // 处理图片变化
  const handleImagesChange = useCallback((newImages) => {
    setImages(newImages);
    if (onImagesChange) {
      onImagesChange(newImages);
    }
  }, [onImagesChange]);

  // 验证并处理文件
  const processFiles = async (files) => {
    const fileArray = Array.from(files);
    
    // 检查数量限制
    if (images.length + fileArray.length > maxImages) {
      toast.error(`最多只能上传 ${maxImages} 张图片`);
      return;
    }

    // 验证每个文件
    const validFiles = [];
    for (const file of fileArray) {
      const validation = await validateImageFile(file);
      
      if (!validation.valid) {
        toast.error(`${file.name}: ${validation.error}`);
        continue;
      }
      
      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => {
          toast(`⚠️ ${file.name}: ${warning}`, {
            duration: 3000,
            icon: '⚠️',
            style: {
              background: '#fff3cd',
              color: '#856404',
              border: '1px solid #ffeaa7'
            }
          });
        });
      }
      
      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      return;
    }

    // 根据模式处理文件
    if (previewOnly || !autoUpload) {
      // 仅预览模式：只创建预览，不上传
      await createPreviews(validFiles);
    } else {
      // 自动上传模式：创建预览并上传
      await uploadFiles(validFiles);
    }
  };

  // 创建预览（不上传）
  const createPreviews = async (files) => {
    const newImages = [...images];

    for (const file of files) {
      const preview = URL.createObjectURL(file);
      const imageData = {
        id: `preview_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
        file,
        preview,
        uploading: false,
        uploaded: false,
        hash: null,
        url: null,
        service: null,
        success: false
      };

      newImages.push(imageData);
    }

    // 更新状态
    setImages(newImages);

    // 使用 setTimeout 避免在渲染期间调用父组件的 setState
    setTimeout(() => {
      if (onImagesChange) {
        onImagesChange(newImages);
      }
    }, 0);
  };

  // 手动上传所有预览图片
  const uploadAllPreviews = async () => {
    const previewImages = images.filter(img => !img.uploaded && img.file);
    if (previewImages.length === 0) return [];

    setUploading(true);
    const results = [];

    try {
      for (const imageData of previewImages) {
        const result = await uploadToIPFS(imageData.file, uploadOptions);

        // 更新图片状态
        setImages(currentImages => {
          const updatedImages = [...currentImages];
          const imageIndex = updatedImages.findIndex(img => img.id === imageData.id);

          if (imageIndex !== -1) {
            updatedImages[imageIndex] = {
              ...updatedImages[imageIndex],
              uploading: false,
              uploaded: true,
              hash: result.hash,
              url: result.url,
              service: result.service,
              success: true,
              error: null
            };
          }

          return updatedImages;
        });

        results.push(result);
      }

      return results;
    } finally {
      setUploading(false);
    }
  };

  // 暴露上传方法给父组件
  useImperativeHandle(ref, () => ({
    uploadAllPreviews
  }), [images]);

  // 上传文件到 IPFS
  const uploadFiles = async (files) => {
    setUploading(true);

    try {
      // 创建预览图片
      const initialImages = [...images];
      const uploadingImages = [];

      for (const file of files) {
        const preview = URL.createObjectURL(file);
        const imageData = {
          id: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
          file,
          preview,
          uploading: true,
          progress: 0,
          hash: null,
          url: null
        };

        uploadingImages.push(imageData);
        initialImages.push(imageData);
      }

      // 设置初始状态（包含上传中的图片）
      setImages(initialImages);
      if (onImagesChange) {
        onImagesChange(initialImages);
      }

      // 逐个上传到 IPFS (避免并发限制)
      const results = { success: [], errors: [] };

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const imageData = uploadingImages[i];

        try {
          // 上传图片到 IPFS
          const result = await uploadToIPFS(file, uploadOptions);

          // 使用函数式更新确保状态同步
          setImages(currentImages => {
            const updatedImages = [...currentImages];
            const imageIndex = updatedImages.findIndex(img => img.id === imageData.id);

            if (imageIndex !== -1) {
              updatedImages[imageIndex] = {
                ...updatedImages[imageIndex],
                uploading: false,
                hash: result.hash,
                url: result.url,
                service: result.service,
                success: true,
                error: null // 清除可能的错误状态
              };

              // 图片上传完成，状态已更新
            }

            // 同时通知父组件
            if (onImagesChange) {
              onImagesChange(updatedImages);
            }

            return updatedImages;
          });

          results.success.push({
            index: i,
            file: file.name,
            ...result
          });

          if (onUploadProgress) {
            onUploadProgress(i, 100);
          }

        } catch (error) {
          // 图片上传失败 - 使用函数式更新
          setImages(currentImages => {
            const updatedImages = [...currentImages];
            const imageIndex = updatedImages.findIndex(img => img.id === imageData.id);

            if (imageIndex !== -1) {
              updatedImages[imageIndex] = {
                ...updatedImages[imageIndex],
                uploading: false,
                error: error.message,
                success: false
              };
            }

            // 同时通知父组件
            if (onImagesChange) {
              onImagesChange(updatedImages);
            }

            return updatedImages;
          });

          results.errors.push({
            index: i,
            file: file.name,
            error: error.message
          });
        }

        // 添加延迟避免API限制
        if (i < files.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 通知上传完成
      if (onUploadComplete) {
        onUploadComplete(results);
      }

      // 显示结果
      if (results.success.length > 0) {
        toast.success(`✅ 成功上传 ${results.success.length} 张图片到 IPFS`);
      }

      if (results.errors.length > 0) {
        toast.error(`❌ ${results.errors.length} 张图片上传失败`);
      }

    } catch (error) {
      // 上传失败
      toast.error(`上传失败: ${error.message}`);
      
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  // 移除图片
  const removeImage = (index) => {
    const newImages = [...images];
    const removedImage = newImages[index];
    
    // 清理预览URL
    if (removedImage.preview && removedImage.preview.startsWith('blob:')) {
      URL.revokeObjectURL(removedImage.preview);
    }
    
    newImages.splice(index, 1);
    handleImagesChange(newImages);
    
    toast.success('图片已移除');
  };

  // 文件选择处理
  const handleFileSelect = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = '';
  };

  // 拖拽处理
  const handleDragOver = (e) => {
    e.preventDefault();
    if (allowDragDrop && !disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (!allowDragDrop || disabled) return;
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // 点击上传
  const handleUploadClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="ipfs-image-upload">
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {/* 图片网格 */}
      <div className="image-grid">
        {showPreview && images.map((image, index) => (
          <div key={image.id || index} className="image-item">
            <div className="image-container">
              <img 
                src={image.preview || image.url || getIPFSImageUrl(image.hash)} 
                alt={`图片 ${index + 1}`}
                onError={(e) => {
                  // 图片加载失败时的备用处理
                  e.target.src = 'https://via.placeholder.com/200x200/f0f0f0/666?text=加载失败';
                }}
              />
              
              {/* 上传状态覆盖层 */}
              {image.uploading && (
                <div className="upload-overlay">
                  <div className="upload-spinner"></div>
                  <div className="upload-text">上传中...</div>
                  {uploadProgress[image.id] && (
                    <div className="upload-progress">
                      {Math.round(uploadProgress[image.id])}%
                    </div>
                  )}
                </div>
              )}
              
              {/* 错误状态 */}
              {image.error && (
                <div className="error-overlay">
                  <div className="error-icon">❌</div>
                  <div className="error-text">上传失败</div>
                </div>
              )}
              
              {/* 成功状态 */}
              {image.success && image.hash && !image.uploading && (
                <div className="success-badge">
                  <span className="success-icon">✅</span>
                  <span className="service-badge">{image.service}</span>
                </div>
              )}
            </div>
            
            {/* 移除按钮 */}
            {!disabled && (
              <button
                type="button"
                className="remove-btn"
                onClick={() => removeImage(index)}
                title="移除图片"
              >
                ✕
              </button>
            )}
            
            {/* 封面标识 */}
            {index === 0 && images.length > 1 && (
              <div className="cover-badge">封面</div>
            )}
          </div>
        ))}

        {/* 上传按钮 */}
        {images.length < maxImages && !disabled && (
          <div
            className={`upload-placeholder ${dragOver ? 'drag-over' : ''} ${uploading ? 'uploading' : ''}`}
            onClick={handleUploadClick}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="upload-content">
              {uploading ? (
                <>
                  <div className="upload-spinner"></div>
                  <div className="upload-text">上传中...</div>
                </>
              ) : (
                <>
                  <div className="upload-icon">📷</div>
                  <div className="upload-text">
                    {allowDragDrop ? '点击或拖拽上传' : '点击上传'}
                  </div>
                  <div className="upload-hint">
                    建议 {IMAGE_STANDARDS.recommended.main.width}×{IMAGE_STANDARDS.recommended.main.height}px
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 上传提示 */}
      <div className="upload-tips">
        <div className="tip-row">
          <span className="tip-item">📏 推荐尺寸: {IMAGE_STANDARDS.recommended.main.width}×{IMAGE_STANDARDS.recommended.main.height}px</span>
          <span className="tip-item">📦 最大: {(IMAGE_STANDARDS.maxSize / 1024 / 1024).toFixed(1)}MB</span>
        </div>
        <div className="tip-row">
          <span className="tip-item">🎯 格式: JPG, PNG, WebP</span>
          <span className="tip-item">📊 数量: 最多 {maxImages} 张</span>
        </div>
        <div className="tip-row">
          <span className="tip-item">🔗 存储: IPFS 去中心化存储</span>
          <span className="tip-item">⚡ 网关: 全球加速访问</span>
        </div>
      </div>

      {/* 上传状态 */}
      {uploading && (
        <div className="upload-status">
          <div className="status-content">
            <div className="loading-spinner"></div>
            <span className="status-text">正在上传到 IPFS...</span>
          </div>
        </div>
      )}
    </div>
  );
});

export default IPFSImageUpload;
