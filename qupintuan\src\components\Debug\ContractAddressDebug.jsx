// src/components/Debug/ContractAddressDebug.jsx
// 专门调试合约地址状态的组件
import { useState } from 'react';
import { useAccount } from 'wagmi';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

export default function ContractAddressDebug() {
  const { address: account } = useAccount();
  const [debugResult, setDebugResult] = useState(null);
  const [isDebugging, setIsDebugging] = useState(false);

  const debugContractAddressState = async () => {
    if (!account) {
      alert('请先连接钱包');
      return;
    }

    setIsDebugging(true);
    setDebugResult(null);

    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const addressMgmtAddr = CONTRACT_ADDRESSES[97].AddressManagement;
      const productMgmtAddr = CONTRACT_ADDRESSES[97].ProductManagement;

      console.log('🔍 开始深度调试合约地址状态...');

      const result = {
        userAccount: account,
        contractAddresses: {
          AddressManagement: addressMgmtAddr,
          ProductManagement: productMgmtAddr
        },
        tests: {}
      };

      // 测试1: 获取用户地址
      console.log('📋 测试1: 获取用户地址...');
      try {
        const userAddresses = await publicClient.readContract({
          address: addressMgmtAddr,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        });

        result.tests.getMyAddresses = {
          success: true,
          addressCount: userAddresses.length,
          addresses: userAddresses.map(addr => ({
            addressId: addr.addressId.toString(),
            user: addr.user,
            name: addr.name,
            isDefault: addr.isDefault
          }))
        };

        console.log('✅ getMyAddresses 成功:', userAddresses.length, '个地址');

      } catch (error) {
        result.tests.getMyAddresses = {
          success: false,
          error: error.message
        };
        console.log('❌ getMyAddresses 失败:', error.message);
      }

      // 测试2: 检查授权状态
      console.log('📋 测试2: 检查授权状态...');
      try {
        const isAuthorized = await publicClient.readContract({
          address: addressMgmtAddr,
          abi: ABIS.AddressManagement,
          functionName: 'authorizedContracts',
          args: [productMgmtAddr]
        });

        result.tests.authorization = {
          success: true,
          isAuthorized
        };

        console.log('✅ 授权检查成功:', isAuthorized);

      } catch (error) {
        result.tests.authorization = {
          success: false,
          error: error.message
        };
        console.log('❌ 授权检查失败:', error.message);
      }

      // 测试3: 尝试直接调用 getAddress（这应该失败，因为前端不是授权的合约）
      console.log('📋 测试3: 尝试直接调用 getAddress...');
      try {
        const addressInfo = await publicClient.readContract({
          address: addressMgmtAddr,
          abi: ABIS.AddressManagement,
          functionName: 'getAddress',
          args: [account, 0],
          account
        });

        result.tests.directGetAddress = {
          success: true,
          addressInfo: {
            addressId: addressInfo.addressId.toString(),
            user: addressInfo.user,
            name: addressInfo.name
          }
        };

        console.log('✅ 直接 getAddress 成功（意外）:', addressInfo);

      } catch (error) {
        result.tests.directGetAddress = {
          success: false,
          error: error.message,
          expected: true // 这个失败是预期的
        };
        console.log('❌ 直接 getAddress 失败（预期）:', error.message);
      }

      // 测试4: 模拟 buyProduct 调用
      console.log('📋 测试4: 模拟 buyProduct 调用...');
      try {
        const simulationResult = await publicClient.simulateContract({
          address: productMgmtAddr,
          abi: ABIS.ProductManagement,
          functionName: 'buyProduct',
          args: [1, 1, 0], // productId=1, quantity=1, addressId=0
          account
        });

        result.tests.buyProductSimulation = {
          success: true,
          result: 'Simulation passed'
        };

        console.log('✅ buyProduct 模拟成功');

      } catch (error) {
        result.tests.buyProductSimulation = {
          success: false,
          error: error.message
        };
        console.log('❌ buyProduct 模拟失败:', error.message);

        // 分析具体错误
        if (error.message.includes('Invalid address ID')) {
          result.tests.buyProductSimulation.analysis = {
            issue: 'Invalid address ID',
            possibleCauses: [
              '用户在合约中没有地址ID为0的地址',
              '地址数组为空',
              '地址存储结构不一致',
              'getAddress 函数内部验证失败'
            ]
          };
        }
      }

      // 测试5: 检查产品信息
      console.log('📋 测试5: 检查产品信息...');
      try {
        const productInfo = await publicClient.readContract({
          address: productMgmtAddr,
          abi: ABIS.ProductManagement,
          functionName: 'products',
          args: [1] // 产品ID 1
        });

        result.tests.productInfo = {
          success: true,
          product: {
            productId: productInfo[0].toString(),
            merchant: productInfo[1],
            name: productInfo[2],
            description: productInfo[3],
            price: productInfo[4].toString(),
            stock: productInfo[5].toString(),
            isActive: productInfo[6]
          }
        };

        console.log('✅ 产品信息获取成功');

      } catch (error) {
        result.tests.productInfo = {
          success: false,
          error: error.message
        };
        console.log('❌ 产品信息获取失败:', error.message);
      }

      // 测试6: 检查用户积分
      console.log('📋 测试6: 检查用户积分...');
      try {
        const pointsAddr = CONTRACT_ADDRESSES[97].PointsManagement;
        const userPoints = await publicClient.readContract({
          address: pointsAddr,
          abi: ABIS.PointsManagement,
          functionName: 'groupBuyPointsNonExchangeable',
          args: [account]
        });

        result.tests.userPoints = {
          success: true,
          points: userPoints.toString()
        };

        console.log('✅ 用户积分获取成功:', userPoints.toString());

      } catch (error) {
        result.tests.userPoints = {
          success: false,
          error: error.message
        };
        console.log('❌ 用户积分获取失败:', error.message);
      }

      setDebugResult(result);
      console.log('🎯 调试完成，结果:', result);

    } catch (error) {
      console.error('❌ 调试过程中发生错误:', error);
      setDebugResult({
        error: error.message,
        userAccount: account
      });
    } finally {
      setIsDebugging(false);
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>合约地址状态深度调试</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={debugContractAddressState} 
          disabled={!account || isDebugging}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: '#dc3545', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: account && !isDebugging ? 'pointer' : 'not-allowed'
          }}
        >
          {isDebugging ? '调试中...' : '🔍 深度调试合约状态'}
        </button>
      </div>

      {!account && (
        <div style={{ color: 'red' }}>请先连接钱包</div>
      )}

      {debugResult && (
        <div style={{ marginTop: '20px' }}>
          <h4>调试结果:</h4>
          <pre style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '15px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '12px',
            maxHeight: '600px'
          }}>
            {JSON.stringify(debugResult, null, 2)}
          </pre>
          
          {debugResult.tests && (
            <div style={{ marginTop: '20px' }}>
              <h4>关键发现:</h4>
              
              {debugResult.tests.getMyAddresses?.success && (
                <div style={{ color: 'green', marginBottom: '10px' }}>
                  ✅ 用户有 {debugResult.tests.getMyAddresses.addressCount} 个地址
                </div>
              )}
              
              {debugResult.tests.authorization?.success && (
                <div style={{ color: debugResult.tests.authorization.isAuthorized ? 'green' : 'red', marginBottom: '10px' }}>
                  {debugResult.tests.authorization.isAuthorized ? '✅' : '❌'} ProductManagement 授权状态: {debugResult.tests.authorization.isAuthorized ? '已授权' : '未授权'}
                </div>
              )}
              
              {debugResult.tests.buyProductSimulation?.success === false && (
                <div style={{ color: 'red', marginBottom: '10px' }}>
                  ❌ buyProduct 模拟失败: {debugResult.tests.buyProductSimulation.error}
                  {debugResult.tests.buyProductSimulation.analysis && (
                    <div style={{ marginTop: '5px', fontSize: '12px' }}>
                      可能原因: {debugResult.tests.buyProductSimulation.analysis.possibleCauses.join(', ')}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
