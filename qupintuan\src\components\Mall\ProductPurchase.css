/* src/components/Mall/ProductPurchase.css */
.product-purchase {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.purchase-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.purchase-modal {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 模态框头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 50%;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 模态框内容 */
.modal-content {
  padding: 24px;
}

/* 商品摘要 */
.product-summary {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.product-summary .product-image {
  width: 80px;
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
  background: #e5e7eb;
  flex-shrink: 0;
}

.product-summary .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #9ca3af;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
}

.product-description {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.product-price {
  color: #dc2626;
  font-weight: 600;
}

.product-stock {
  color: #6b7280;
}

/* 用户积分信息 */
.user-points {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 8px;
}

.points-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.points-label {
  color: #374151;
  font-weight: 500;
}

.points-value {
  color: #0369a1;
  font-weight: 600;
  font-size: 16px;
}

.insufficient-notice {
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  padding: 8px;
  background: #fef2f2;
  border-radius: 6px;
}

/* 收货地址选择 */
.address-section {
  margin-bottom: 24px;
}

.address-label {
  margin-bottom: 12px;
  color: #374151;
  font-weight: 500;
}

.address-loading {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
}

.no-address {
  padding: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.no-address-notice {
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 8px;
}

.no-address-hint {
  color: #6b7280;
  font-size: 14px;
}

.address-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address-option {
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.address-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.address-option.selected {
  border-color: #10b981;
  background: #f0fdf4;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.address-name {
  font-weight: 600;
  color: #1f2937;
}

.address-phone {
  color: #6b7280;
  font-size: 14px;
}

.default-tag {
  background: #10b981;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.address-detail {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

/* 数量选择 */
.quantity-section {
  margin-bottom: 24px;
}

.quantity-label {
  margin-bottom: 12px;
  color: #374151;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
}

.quantity-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.quantity-input {
  width: 80px;
  height: 36px;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
}

.quantity-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.quantity-hint {
  color: #6b7280;
  font-size: 12px;
}

/* 价格计算 */
.price-calculation {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.calc-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.calc-row:last-child {
  margin-bottom: 0;
}

.calc-row.total {
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
  font-size: 16px;
  font-weight: 600;
}

.total-price {
  color: #dc2626;
  font-size: 18px;
}

/* 模态框操作 */
.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn,
.purchase-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.cancel-btn:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.purchase-btn {
  background: #10b981;
  color: white;
}

.purchase-btn:hover:not(:disabled) {
  background: #059669;
}

.purchase-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 购买说明 */
.purchase-notice {
  padding: 16px 24px 24px;
  background: #f8fafc;
}

.purchase-notice h5 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.notice-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.notice-item {
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-purchase {
    padding: 16px;
  }

  .purchase-modal {
    max-height: 95vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-content {
    padding: 20px;
  }

  .product-summary {
    flex-direction: column;
    gap: 12px;
  }

  .product-summary .product-image {
    width: 100%;
    aspect-ratio: 1;
  }

  .modal-actions {
    padding: 16px 20px;
    flex-direction: column;
  }

  .purchase-notice {
    padding: 12px 20px 20px;
  }
}

@media (max-width: 480px) {
  .product-purchase {
    padding: 12px;
  }

  .modal-header {
    padding: 12px 16px;
  }

  .modal-content {
    padding: 16px;
  }

  .quantity-controls {
    justify-content: center;
  }

  .modal-actions {
    padding: 12px 16px;
  }

  .purchase-notice {
    padding: 10px 16px 16px;
  }
}

/* 地址选择器样式 */
.address-selector-section {
  margin: 20px 0;
}

.purchase-address-selector {
  margin: 0;
}

.current-address-display {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.current-address-display h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.current-address-display .address-info {
  position: relative;
}

.current-address-display .address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.current-address-display .recipient-name {
  font-weight: 600;
  color: #333;
}

.current-address-display .recipient-phone {
  color: #666;
  font-size: 14px;
}

.current-address-display .default-badge {
  background: #52c41a;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.current-address-display .address-detail {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.btn-change-address {
  background: #1890ff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-change-address:hover {
  background: #40a9ff;
}
