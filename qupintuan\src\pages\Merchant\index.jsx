// src/pages/Merchant/index.jsx
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import ProductManagement from '@/components/Merchant/ProductManagement'
import SalesPointsManagement from '@/components/Merchant/SalesPointsManagement'

import './index.css'

export default function MerchantDashboard() {
  const { address: account, isConnected } = useAccount()

  // 状态管理
  const [activeTab, setActiveTab] = useState('products')
  const [merchantStatus, setMerchantStatus] = useState({
    isRegistered: false,
    isVerified: false,
    isActive: false,
    loading: true
  })

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 检查商家状态
  const checkMerchantStatus = async () => {
    if (!account || !isConnected) {
      setMerchantStatus({
        isRegistered: false,
        isVerified: false,
        isActive: false,
        loading: false
      })
      return
    }

    try {
      // 使用与个人中心相同的API
      const { isMerchant } = await import('@/apis/mallApi');
      const merchantData = await isMerchant({ userAddress: account });

      // 同时检查合约中的isMerchant状态
      const { readContract } = await import('wagmi/actions');
      const { config } = await import('@/wagmi.config');
      const { getContractAddress } = await import('@/contracts/addresses');
      const { ABIS } = await import('@/contracts/index');

      const mmAddress = getContractAddress(97, 'MerchantManagement');
      const mmABI = ABIS.MerchantManagement;

      const contractIsMerchant = await readContract(config, {
        address: mmAddress,
        abi: mmABI,
        functionName: 'isMerchant',
        args: [account],
      });

      // 详细状态检查
      const finalIsActive = merchantData.isVerified && contractIsMerchant;

      setMerchantStatus({
        isRegistered: merchantData.isRegistered,
        isVerified: merchantData.isVerified && contractIsMerchant, // 必须两个都为true
        isActive: finalIsActive, // 必须两个都为true
        loading: false
      })

    } catch (error) {
      setMerchantStatus({
        isRegistered: false,
        isVerified: false,
        isActive: false,
        loading: false
      })
    }
  }

  useEffect(() => {
    checkMerchantStatus()
  }, [account, isConnected])

  // 如果未连接钱包
  if (!isConnected) {
    return (
      <div className="merchant-dashboard">
        <div className="dashboard-header">
          <h1>🏪 商家后台</h1>
          <p>管理您的商品和销售积分</p>
        </div>
        <div className="connect-prompt">
          <div className="prompt-card">
            <h3>🔐 请连接钱包</h3>
            <p>连接钱包后即可访问商家后台功能</p>
          </div>
        </div>
      </div>
    )
  }

  // 如果正在加载
  if (merchantStatus.loading) {
    return (
      <div className="merchant-dashboard">
        <div className="dashboard-header">
          <h1>🏪 商家后台</h1>
          <p>管理您的商品和销售积分</p>
        </div>
        <div className="loading-state">
          <div className="loading-card">
            <div className="loading-spinner">🔄</div>
            <p>正在验证商家身份...</p>
          </div>
        </div>
      </div>
    )
  }

  // 运行调试工具
  const runDebugTool = async () => {
    try {
      const { runMerchantStatusDebug } = await import('@/debug/merchantStatusChecker');
      await runMerchantStatusDebug(account);
      alert('调试完成！请查看浏览器控制台获取详细信息。');
    } catch (error) {
      console.error('❌ 调试工具运行失败:', error);
      alert('调试工具运行失败，请查看控制台错误信息。');
    }
  };

  // 如果不是激活的商家，拒绝访问
  if (!merchantStatus.isActive) {
    return (
      <div className="merchant-dashboard">
        <div className="dashboard-header">
          <h1>🏪 商家后台</h1>
          <p>管理您的商品和销售积分</p>
        </div>
        <div className="access-denied">
          <div className="denied-card">
            <h3>⚠️ 访问受限</h3>
            {!merchantStatus.isRegistered ? (
              <div className="denied-content">
                <p>您还未申请成为商家</p>
                <p>请前往 <strong>个人中心 → 商家入驻</strong> 提交申请</p>
              </div>
            ) : !merchantStatus.isVerified ? (
              <div className="denied-content">
                <p>您的商家申请正在审核中</p>
                <p>请耐心等待平台审核，审核通过后即可使用商家后台</p>
              </div>
            ) : (
              <div className="denied-content">
                <p>您的商家账户未激活</p>
                <p>请联系平台管理员激活您的商家账户</p>
              </div>
            )}

            {/* 调试工具 */}
            <div className="debug-section" style={{ marginTop: '20px', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
              <h4>🔧 调试工具</h4>
              <p>如果您认为这是错误，请点击下方按钮运行调试工具：</p>
              <button
                onClick={runDebugTool}
                style={{
                  background: '#17a2b8',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                🔍 运行状态调试
              </button>
              <button
                onClick={() => window.open('/debug-merchant-status.html', '_blank')}
                style={{
                  background: '#6c757d',
                  color: 'white',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                📋 打开调试页面
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 激活的商家可以访问后台
  return (
    <div className="merchant-dashboard">
      <div className="dashboard-header">
        <h1>🏪 商家后台</h1>
        <p>管理您的商品和销售积分</p>
        <div className="header-info">
          <div className="merchant-badge">
            <span className="badge-icon">✅</span>
            <span>认证商家</span>
          </div>
          {/* 调试信息 */}
          <div className="debug-info">
            <span className="debug-label">状态:</span>
            <span className="debug-value">
              注册: {merchantStatus.isRegistered ? '✅' : '❌'} |
              认证: {merchantStatus.isVerified ? '✅' : '❌'} |
              激活: {merchantStatus.isActive ? '✅' : '❌'}
            </span>
          </div>
        </div>
      </div>

      {/* 导航标签 */}
      <div className="dashboard-nav">
        <div className="nav-tabs">
          <button
            className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}
            onClick={() => setActiveTab('products')}
          >
            📦 商品管理
          </button>
          <button
            className={`tab-btn ${activeTab === 'points' ? 'active' : ''}`}
            onClick={() => setActiveTab('points')}
          >
            💰 销售积分
          </button>

        </div>
      </div>

      {/* 内容区域 */}
      <div className="dashboard-content">
        {activeTab === 'products' && (
          <div className="content-section">
            <ProductManagement />
          </div>
        )}

        {activeTab === 'points' && (
          <div className="content-section">
            <SalesPointsManagement />
          </div>
        )}


      </div>
    </div>
  )
}
