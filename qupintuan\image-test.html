<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片显示测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.success {
            background: #27ae60;
        }
        .test-button.success:hover {
            background: #229954;
        }
        .image-test {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .image-item {
            width: 200px;
            height: 200px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .image-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px;
            font-size: 12px;
            text-align: center;
        }
        .status {
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            margin: 5px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .placeholder-image {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f3f4f6;
            color: #9ca3af;
        }
        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 8px;
        }
        .placeholder-text {
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <h1>🖼️ 图片显示测试工具</h1>
    
    <div class="test-card">
        <h3 class="test-title">🎯 测试您的商品图片</h3>
        
        <div class="status warning">
            点击下面的按钮测试您的商品图片是否能正常显示
        </div>
        
        <button class="test-button" onclick="testProductImage()">
            🧪 测试商品图片
        </button>
        
        <button class="test-button success" onclick="openProductList()">
            📦 打开商品列表页面
        </button>
        
        <div class="image-test" id="image-test-container">
            <!-- 测试图片将在这里显示 -->
        </div>
        
        <div id="test-results"></div>
    </div>

    <div class="test-card">
        <h3 class="test-title">📋 测试步骤</h3>
        
        <div class="status warning">
            请按以下步骤进行测试：
        </div>
        
        <ol>
            <li><strong>点击 "🧪 测试商品图片" 按钮</strong>
                <ul>
                    <li>查看图片是否能正常显示</li>
                    <li>绿色边框 = 加载成功</li>
                    <li>红色边框 = 加载失败</li>
                </ul>
            </li>
            <li><strong>点击 "📦 打开商品列表页面" 按钮</strong>
                <ul>
                    <li>会在新标签页打开商品列表</li>
                    <li>查看商品卡片是否显示图片</li>
                </ul>
            </li>
            <li><strong>查看控制台调试信息</strong>
                <ul>
                    <li>按 F12 打开开发者工具</li>
                    <li>切换到 Console 标签</li>
                    <li>查找图片相关的调试信息</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-card">
        <h3 class="test-title">🔍 如果图片不显示</h3>
        
        <div class="status error">
            可能的原因和解决方案：
        </div>
        
        <ul>
            <li><strong>网络问题</strong>：IPFS网关可能被墙或不稳定</li>
            <li><strong>浏览器缓存</strong>：按 Ctrl+Shift+R 强制刷新</li>
            <li><strong>CORS问题</strong>：某些网关可能有跨域限制</li>
            <li><strong>图片哈希问题</strong>：上传的图片可能有问题</li>
        </ul>
        
        <p><strong>如果测试失败，请告诉我：</strong></p>
        <ol>
            <li>哪些网关显示绿色边框（成功）</li>
            <li>哪些网关显示红色边框（失败）</li>
            <li>控制台有什么错误信息</li>
        </ol>
    </div>

    <script>
        function testProductImage() {
            const container = document.getElementById('image-test-container');
            const results = document.getElementById('test-results');
            
            container.innerHTML = '';
            results.innerHTML = '<div class="status warning">🧪 正在测试图片加载...</div>';
            
            // 您的商品图片哈希
            const testHash = 'QmcV8FxpcxsVySGa5qUU81S6AfiFHaWM6Jq4u5U9ThG789';
            
            // 测试的IPFS网关
            const gateways = [
                { name: 'Pinata', url: 'https://gateway.pinata.cloud/ipfs' },
                { name: 'dweb.link', url: 'https://dweb.link/ipfs' },
                { name: 'Cloudflare', url: 'https://cloudflare-ipfs.com/ipfs' },
                { name: 'IPFS.io', url: 'https://ipfs.io/ipfs' }
            ];
            
            console.log('🧪 开始测试商品图片:', testHash);
            
            let successCount = 0;
            let totalCount = gateways.length;
            
            gateways.forEach((gateway, index) => {
                const imageUrl = `${gateway.url}/${testHash}`;
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                
                const img = document.createElement('img');
                const label = document.createElement('div');
                label.className = 'image-label';
                label.textContent = gateway.name;
                
                img.onload = function() {
                    console.log(`✅ ${gateway.name} 加载成功:`, imageUrl);
                    imageItem.style.borderColor = '#27ae60';
                    label.style.background = 'rgba(39, 174, 96, 0.8)';
                    successCount++;
                    updateResults();
                };
                
                img.onerror = function() {
                    console.log(`❌ ${gateway.name} 加载失败:`, imageUrl);
                    imageItem.style.borderColor = '#e74c3c';
                    label.style.background = 'rgba(231, 76, 60, 0.8)';
                    
                    // 显示占位图片
                    imageItem.innerHTML = `
                        <div class="placeholder-image">
                            <div class="placeholder-icon">❌</div>
                            <div class="placeholder-text">加载失败</div>
                        </div>
                        <div class="image-label" style="background: rgba(231, 76, 60, 0.8);">${gateway.name}</div>
                    `;
                    updateResults();
                };
                
                img.src = imageUrl;
                
                imageItem.appendChild(img);
                imageItem.appendChild(label);
                container.appendChild(imageItem);
            });
            
            function updateResults() {
                const tested = container.children.length;
                if (tested === totalCount) {
                    if (successCount > 0) {
                        results.innerHTML = `
                            <div class="status success">
                                ✅ 测试完成！${successCount}/${totalCount} 个网关可用
                                <br>绿色边框的网关可以正常加载图片
                            </div>
                        `;
                    } else {
                        results.innerHTML = `
                            <div class="status error">
                                ❌ 所有网关都无法加载图片！
                                <br>可能是网络问题或图片哈希无效
                            </div>
                        `;
                    }
                }
            }
        }

        function openProductList() {
            const url = 'http://localhost:5174/product';
            window.open(url, '_blank');
            console.log('📦 打开商品列表页面:', url);
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            console.log('🖼️ 图片测试工具已加载');
            console.log('📋 请点击测试按钮开始测试');
        });
    </script>
</body>
</html>
