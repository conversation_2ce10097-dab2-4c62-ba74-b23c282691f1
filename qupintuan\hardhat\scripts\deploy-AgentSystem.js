// scripts/deploy-AgentSystem.js
// 部署可升级的 AgentSystem 合约
require("dotenv").config();
const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🔄 部署可升级的 AgentSystem 合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 尝试获取签名者
    let deployer;
    try {
      const signers = await ethers.getSigners();
      if (!signers || signers.length === 0) {
        throw new Error("No signers found");
      }
      deployer = signers[0];
    } catch (error) {
      // 如果获取签名者失败，尝试直接使用私钥创建钱包
      console.log("⚠️ 无法从 Hardhat 获取签名者，尝试直接使用私钥...");

      const privateKey = process.env.PRIVATE_KEY;
      if (!privateKey) {
        throw new Error("没有找到 PRIVATE_KEY 环境变量");
      }

      let formattedKey = privateKey;
      if (!formattedKey.startsWith("0x")) {
        formattedKey = "0x" + formattedKey;
      }

      deployer = new ethers.Wallet(formattedKey, ethers.provider);
      console.log("✅ 使用私钥创建钱包成功");
    }
    console.log("📝 部署者地址:", deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

    // 从环境变量获取地址
    const systemAdmin = process.env.SYSTEM_ADMIN_ADDRESS || deployer.address;
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;

    console.log("📋 配置信息:");
    console.log("   • 系统管理员:", systemAdmin);
    console.log("   • Timelock地址:", timelockAddr);

    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
    }

    // 获取合约工厂
    const AgentSystem = await ethers.getContractFactory("AgentSystem");

    // 部署可升级合约
    console.log("⏳ 部署 AgentSystem 代理合约...");
    const agentSystem = await upgrades.deployProxy(
      AgentSystem,
      [systemAdmin, timelockAddr], // 传递正确的参数：系统管理员地址和Timelock地址
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    // 等待部署完成
    await agentSystem.waitForDeployment();
    const proxyAddress = await agentSystem.getAddress();

    console.log("✅ AgentSystem 部署成功!");
    console.log("📍 代理地址:", proxyAddress);

    // 获取实现地址
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);

    // 验证部署结果
    console.log("\n🔍 验证部署结果:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const systemAdminAddr = await agentSystem.systemAdmin();
    const timelockContract = await agentSystem.timelock();

    // 检查角色权限
    const ADMIN_ROLE = await agentSystem.ADMIN_ROLE();
    const hasAdminRole = await agentSystem.hasRole(ADMIN_ROLE, systemAdmin);
    const hasTimelockAdminRole = await agentSystem.hasRole(ADMIN_ROLE, timelockAddr);

    console.log("🔧 系统管理员:", systemAdminAddr);
    console.log("🔒 Timelock合约:", timelockContract);
    console.log("👤 系统管理员角色权限:", hasAdminRole ? "✅ 有" : "❌ 无");
    console.log("🔒 Timelock管理员权限:", hasTimelockAdminRole ? "✅ 有" : "❌ 无");
    console.log("✅ 系统管理员配置:", systemAdminAddr === systemAdmin ? "正确" : "错误");
    console.log("✅ Timelock配置:", timelockContract === timelockAddr ? "正确" : "错误");

    // 授予Timelock管理员角色
    console.log("\n🔄 配置Timelock权限...");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const hasTimelockAdminRoleCheck = await agentSystem.hasRole(ADMIN_ROLE, timelockAddr);

    if (!hasTimelockAdminRoleCheck) {
      console.log("🔖 授予Timelock管理员权限...");
      const grantRoleTx = await agentSystem.grantRole(ADMIN_ROLE, timelockAddr);
      await grantRoleTx.wait();
      console.log("✅ Timelock管理员权限已授予");
    } else {
      console.log("✅ Timelock已有管理员权限");
    }

    // 验证最终权限配置
    console.log("\n🔍 验证最终权限配置...");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const finalSystemAdminRole = await agentSystem.hasRole(ADMIN_ROLE, systemAdmin);
    const finalTimelockRole = await agentSystem.hasRole(ADMIN_ROLE, timelockAddr);
    const finalSystemAdmin = await agentSystem.systemAdmin();

    console.log("👤 系统管理员角色权限:", finalSystemAdminRole ? "✅ 有" : "❌ 无");
    console.log("🔒 Timelock管理员权限:", finalTimelockRole ? "✅ 有" : "❌ 无");
    console.log("🔧 系统管理员地址:", finalSystemAdmin);

    if (finalSystemAdminRole && finalTimelockRole && finalSystemAdmin === systemAdmin) {
      console.log("✅ 权限配置验证成功");
    } else {
      console.log("❌ 权限配置验证失败");
    }

    console.log("\n📋 部署完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("✅ 可升级的 AgentSystem 部署成功!");
    console.log("📍 代理地址:", proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);
    console.log("👤 所有者:", timelockAddr);
    console.log("🔒 升级控制: 只有Timelock可以升级");
    console.log("");
    console.log("🆕 功能特性:");
    console.log("   • ✅ 支持UUPS升级模式");
    console.log("   • ✅ Timelock控制升级");
    console.log("   • ✅ 角色权限管理");
    console.log("   • ✅ 代理用户注册");
    console.log("");
    console.log("🔧 环境变量更新:");
    console.log(`AGENT_SYSTEM_ADDRESS=${proxyAddress}`);

    // 保存部署信息
    const deploymentInfo = {
      proxyAddress,
      implementationAddress,
      deployer: deployer.address,
      owner: timelockAddr,
      systemAdmin,
      timelock: timelockAddr,
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      features: ["UUPS_UPGRADEABLE", "TIMELOCK_PROTECTED", "ROLE_BASED_ACCESS"],
      securityLevel: "HIGH"
    };

    const fs = require('fs');
    fs.writeFileSync(
      './agentsystem-deployment.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("💾 部署信息已保存到 agentsystem-deployment.json");

  } catch (error) {
    console.error("❌ 部署失败:", error);

    if (error.message.includes("insufficient funds")) {
      console.log("💡 提示: 部署者账户BNB余额不足");
    } else if (error.message.includes("ADDRESS_MANAGEMENT_ADDRESS")) {
      console.log("💡 提示: 请先部署 AddressManagement 合约");
    } else if (error.message.includes("PRIVATE_KEY")) {
      console.log("💡 提示: 请检查 .env 文件中的 PRIVATE_KEY 配置");
    }

    process.exit(1);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
