/* src/components/Common/IPFSImageUpload/index.css */

.ipfs-image-upload {
  width: 100%;
}

/* 图片网格 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

/* 图片项 */
.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.image-item:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

/* 图片容器 */
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 上传状态覆盖层 */
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.upload-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

.upload-text {
  font-weight: 500;
  margin-bottom: 4px;
}

.upload-progress {
  font-size: 10px;
  opacity: 0.8;
}

/* 错误状态覆盖层 */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(220, 53, 69, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.error-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.error-text {
  font-weight: 500;
}

/* 成功状态标识 */
.success-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.success-icon {
  font-size: 12px;
}

.service-badge {
  font-weight: 500;
  text-transform: uppercase;
}

/* 移除按钮 */
.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #dc3545;
  color: white;
  border: 2px solid white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.remove-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

/* 封面标识 */
.cover-badge {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

/* 上传占位符 */
.upload-placeholder {
  aspect-ratio: 1;
  border: 2px dashed #dee2e6;
  border-radius: 12px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.upload-placeholder:hover {
  border-color: #007bff;
  background: #e7f3ff;
  transform: translateY(-2px);
}

.upload-placeholder.drag-over {
  border-color: #28a745;
  background: #e8f5e8;
  border-style: solid;
}

.upload-placeholder.uploading {
  cursor: not-allowed;
  opacity: 0.7;
}

.upload-content {
  text-align: center;
  padding: 16px;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 11px;
  color: #6c757d;
  line-height: 1.3;
}

/* 上传提示 */
.upload-tips {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.tip-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.tip-row:last-child {
  margin-bottom: 0;
}

.tip-item {
  font-size: 12px;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 上传状态 */
.upload-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }
  
  .upload-content {
    padding: 12px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
  
  .upload-text {
    font-size: 12px;
  }
  
  .upload-hint {
    font-size: 10px;
  }
  
  .tip-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .upload-status {
    bottom: 10px;
    right: 10px;
    left: 10px;
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
  }
  
  .upload-tips {
    padding: 8px;
  }
  
  .tip-item {
    font-size: 11px;
  }
}
