# 测试环境配置
VITE_APP_ENV=test
VITE_APP_DOMAIN=https://test.qupintuan.fun

# 区块链网络配置 - BSC测试网
VITE_CHAIN_ID=97  # BSC测试网
VITE_NETWORK_NAME=BSC Testnet

# RPC节点配置
VITE_BSC_RPC_URL=https://bsc-testnet.public.blastapi.io
VITE_BSC_TESTNET_RPC_URL=https://bsc-testnet.public.blastapi.io
VITE_RPC_URL_TESTNET=https://bsc-testnet.public.blastapi.io

# 合约地址 (从hardhat/.env复制)
VITE_QPT_TOKEN_ADDRESS=******************************************
VITE_USDT_TOKEN_ADDRESS=******************************************
VITE_QPT_TOKEN_ADDRESS_TESTNET=******************************************
VITE_USDT_ADDRESS_TESTNET=******************************************
VITE_PLATFORM_ADDRESS=******************************************

# 管理员地址
VITE_SYSTEM_ADMIN_ADDRESS=******************************************
VITE_BUYBACK_ADMIN_ADDRESS=******************************************

# —— 多签合约地址 ——
VITE_MULTISIGWALLET_ADDRESS=******************************************
#你的 Timelock 合约地址
VITE_MYTIMELOCK_ADDRESS=******************************************
#  你的 FeeSplitManager 合约地址
VITE_FEESPLITMANAGER_ADDRESS=******************************************
#  你的 AddressManagement 合约地址
VITE_ADDRESSMANAGEMENT_ADDRESS=******************************************
#  你的 AgentSystem 合约地址
VITE_AGENTSYSTEM_ADDRESS=******************************************
# 你的 PointsManagement 合约地址
VITE_POINTS_MANAGEMENT_ADDRESS=******************************************
# 你的 MerchantManagement 合约地址
VITE_MERCHANT_MANAGEMENT_ADDRESS=******************************************
# 你的OrderManagement地址
VITE_ORDER_MANAGEMENT_ADDRESS=******************************************
# 你的 ProductManagement 合约地址
VITE_PRODUCT_MANAGEMENT_ADDRESS=******************************************
# 你的 QPTLocker 合约地址
VITE_QPTLOCKER_ADDRESS=0xcca28B4d5005819107948fe279893435a2A1728e
# 你的 NodeStaking 合约地址
VITE_NODE_STAKING_ADDRESS=0x04bD703Dd859f3ED9b643a72d538EAACdf6e45f5
# 你的 QPTBuyBack 合约地址
VITE_QPT_BUYBACK_ADDRESS=0x23449063C3Fc3cc6361ef93Af4b602E0fC420dCE
# 你的 GroupBuyRoom 合约地址
VITE_GROUPBUY_ROOM_ADDRESS=0x8580424D25C3B5dAdb4D7a0bcB991C6e8d720551
# —— 查询合约地址 ——
VITE_SIMPLE_QUERY_ADDRESS=0x7713b0C625054643D73F294d101e742078fF69f6

