{"proxyAddress": "0x29bC33F518d741DC45c1857B6ee8f3F529E594cd", "implementationAddress": "0x9D4CD62F76F5DF6D0e2C124C3627756498BCCf13", "deployer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "owner": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "initializationParameters": {"usdtToken": "0x2c6431CA0A2e7F5D2015179BdD7241fA1714DE86", "timelock": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "marketplace": "0x0000000000000000000000000000000000000001 (临时)", "merchantManager": "0x0000000000000000000000000000000000000001 (临时)", "groupBuyRoom": "0x0000000000000000000000000000000000000001 (临时)"}, "isFullyConfigured": false, "needsConfiguration": true, "timestamp": "2025-08-01T06:36:32.141Z", "network": "bscTestnet", "version": "v1.0.0", "features": ["UUPS_UPGRADEABLE", "MINIMAL_INITIALIZATION", "REQUIRES_CONFIGURATION", "TIMELOCK_PROTECTED", "BNB_RECEIVE"], "securityLevel": "HIGH", "status": "DEPLOYED_NEEDS_CONFIGURATION"}