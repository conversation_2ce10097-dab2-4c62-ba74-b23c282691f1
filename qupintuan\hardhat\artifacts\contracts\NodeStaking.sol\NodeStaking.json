{"_format": "hh-sol-artifact-1", "contractName": "NodeStaking", "sourceName": "contracts/NodeStaking.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "BNBReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "BNBWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "day", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalBalance", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "effectiveNodes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "rewardPerNode", "type": "uint256"}], "name": "DailyRewardCalculated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "DividendReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyUSDTWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "activationTime", "type": "uint256"}], "name": "NodeStaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "NodeUnstaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "day", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "MAX_NODES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "calculateDailyReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint8", "name": "operation", "type": "uint8"}], "name": "checkNodeOperationEligibility", "outputs": [{"internalType": "bool", "name": "canOperate", "type": "bool"}, {"internalType": "string", "name": "reason", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentDay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "dailyRewardPerNode", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getBNBBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "userList", "type": "address[]"}], "name": "getBatchUserNodeStatus", "outputs": [{"components": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "lastClaimDate", "type": "uint256"}, {"internalType": "uint256", "name": "requiredStakeAmount", "type": "uint256"}, {"internalType": "bool", "name": "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "canClaimToday", "type": "bool"}, {"internalType": "uint256", "name": "dailyReward", "type": "uint256"}, {"internalType": "uint256", "name": "totalActiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "maxNodes", "type": "uint256"}, {"internalType": "uint256", "name": "availableSlots", "type": "uint256"}, {"internalType": "bool", "name": "canStakeNode", "type": "bool"}, {"internalType": "uint256", "name": "currentTime", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "daysSinceLaunch", "type": "uint256"}, {"internalType": "uint256", "name": "totalDividends", "type": "uint256"}, {"internalType": "uint256", "name": "activationTime", "type": "uint256"}], "internalType": "struct NodeStaking.UserNodeStatus[]", "name": "nodeStatuses", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentRequiredStake", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDailyRewardPerNode", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDividendDetails", "outputs": [{"components": [{"internalType": "uint256", "name": "totalDividends", "type": "uint256"}, {"internalType": "uint256", "name": "todayNewDividends", "type": "uint256"}, {"internalType": "uint256", "name": "availableDividendPool", "type": "uint256"}, {"internalType": "uint256", "name": "totalEffectiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "rewardPerNode", "type": "uint256"}, {"internalType": "uint256", "name": "lastCalculateTime", "type": "uint256"}, {"internalType": "bool", "name": "canCalculateToday", "type": "bool"}], "internalType": "struct NodeStaking.DividendDetails", "name": "dividendDetails", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getNodeActivationTime", "outputs": [{"internalType": "uint256", "name": "activationTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNodeAddresses", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "page", "type": "uint256"}, {"internalType": "uint256", "name": "pageSize", "type": "uint256"}], "name": "getNodeAddressesPaginated", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSystemNodeStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalActiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "maxNodes", "type": "uint256"}, {"internalType": "uint256", "name": "availableSlots", "type": "uint256"}, {"internalType": "uint256", "name": "totalDividends", "type": "uint256"}, {"internalType": "uint256", "name": "currentRequiredStake", "type": "uint256"}, {"internalType": "uint256", "name": "initialStakeAmount", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "daysSinceLaunch", "type": "uint256"}, {"internalType": "uint256", "name": "currentTime", "type": "uint256"}, {"internalType": "uint256", "name": "dailyRewardPerNode", "type": "uint256"}], "internalType": "struct NodeStaking.SystemNodeStats", "name": "systemStats", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTotalDividends", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserNodeStatus", "outputs": [{"components": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "lastClaimDate", "type": "uint256"}, {"internalType": "uint256", "name": "requiredStakeAmount", "type": "uint256"}, {"internalType": "bool", "name": "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "canClaimToday", "type": "bool"}, {"internalType": "uint256", "name": "dailyReward", "type": "uint256"}, {"internalType": "uint256", "name": "totalActiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "maxNodes", "type": "uint256"}, {"internalType": "uint256", "name": "availableSlots", "type": "uint256"}, {"internalType": "bool", "name": "canStakeNode", "type": "bool"}, {"internalType": "uint256", "name": "currentTime", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "daysSinceLaunch", "type": "uint256"}, {"internalType": "uint256", "name": "totalDividends", "type": "uint256"}, {"internalType": "uint256", "name": "activationTime", "type": "uint256"}], "internalType": "struct NodeStaking.UserNodeStatus", "name": "nodeStatus", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getVersion", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "hasClaimedTodayReward", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialStakeAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_qptLocker", "type": "address"}, {"internalType": "address", "name": "_usdtToken", "type": "address"}, {"internalType": "address", "name": "_timelock", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isNodeActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "lastClaimDay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "nodeActivationTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "q<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "contract IQPTLock", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "receiveDividendFund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_timelock", "type": "address"}], "name": "setTimelock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakeNode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "startTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "timelock", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalEffectiveNodes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unstakeNode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "usdtToken", "outputs": [{"internalType": "contract IERC20Upgradeable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawBNB", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdrawUSDT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}