// src/components/MerchantApplication/index.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { isMerchant, getMerchantInfo, registerMerchant, submitMerchantVerification } from '@/apis/mallApi';
import { checkUserRegistered } from '@/apis/agentSystemApi';
import { merchantTracker } from '@/utils/merchantRegistrationTracker';
import { merchantApplicationStorage } from '@/utils/merchantApplicationStorage';
import { uploadToCloudinary } from '@/utils/cloudinaryUpload';
import './index.css';

export default function MerchantApplication() {
  const { address: account } = useAccount();

  // 状态管理
  const [isRegisteredUser, setIsRegisteredUser] = useState(false);
  const [isMerchantUser, setIsMerchantUser] = useState(false);
  const [merchantInfo, setMerchantInfo] = useState(null);
  const [merchantStatus, setMerchantStatus] = useState({ isRegistered: false, isVerified: false });
  const [isLoading, setIsLoading] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const [showApplicationForm, setShowApplicationForm] = useState(false);

  // 审核资料表单状态
  const [applicationData, setApplicationData] = useState({
    name: '',
    description: '',
    category: '',
    contactPhone: '',
    contactEmail: '',
    address: '',
    businessLicense: '',
    logo: ''
  });
  const [applicationErrors, setApplicationErrors] = useState({});

  // 检查用户状态
  const checkUserStatus = async () => {
    if (!account) return;

    setIsChecking(true);
    try {
      // 检查用户是否已注册代理系统
      const registered = await checkUserRegistered({ userAddress: account });
      setIsRegisteredUser(registered);

      let merchant = false;
      let info = null;

      if (registered) {
        // 检查商家状态
        const merchantStatus = await isMerchant({ userAddress: account });

        // 设置商家状态
        setIsMerchantUser(merchantStatus.isVerified); // 只有已认证的才算是商家

        if (merchantStatus.isRegistered) {
          // 获取商家信息
          info = await getMerchantInfo({ merchantAddress: account });
          setMerchantInfo(info);
        }

        // 保存完整的商家状态供后续使用
        setMerchantStatus(merchantStatus);
      }



    } catch (error) {
      console.error('❌ [MerchantApplication] 检查用户状态失败:', error);
      toast.error(`检查用户状态失败: ${error.message}`);
    } finally {
      setIsChecking(false);
    }
  };

  // 移除表单验证，一键注册不需要验证

  // 一键注册商家身份
  const handleQuickRegister = async () => {
    setIsLoading(true);
    try {
      // 检查用户权限（防止被限制的用户注册商家账户）
      const { checkUserPermission, PERMISSION_TYPES } = await import('@/utils/permissionManager');
      const permission = await checkUserPermission(account, PERMISSION_TYPES.ALL_OPERATIONS);
      if (!permission.allowed) {
        toast.error(permission.reason, {
          duration: 8000,
          position: 'top-center',
        });
        return;
      }

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        ...ethersSigner
      };

      // 使用默认信息注册商家身份
      const defaultName = `商家_${account.slice(-6)}`;
      const defaultDescription = '商家身份已注册，详细信息将在审核申请时补充';

      // 调用注册API
      const result = await registerMerchant({
        name: defaultName,
        description: defaultDescription,
        logo: '',
        contactPhone: '',
        contactEmail: '',
        businessLicense: '',
        address: '',
        category: 'other',
        signer
      });

      // 添加到商家跟踪器
      merchantTracker.addMerchant(account);

      // 清理可能存在的过期申请信息
      try {
        // 获取商家在合约中的创建时间
        const { getMerchantInfo } = await import('@/apis/mallApi');
        const merchantInfo = await getMerchantInfo({ merchantAddress: account });
        if (merchantInfo && merchantInfo.createTime) {
          merchantApplicationStorage.cleanExpiredApplication(account, merchantInfo.createTime);
        }
      } catch (cleanError) {
        // 清理过期申请信息失败（静默处理）
      }

      // 成功提示
      toast.success('🎉 商家身份注册成功！请点击"提交审核申请"按钮继续', {
        duration: 4000,
        position: 'top-center',
      });

      // 重新检查状态
      await checkUserStatus();

    } catch (error) {
      console.error('❌ [MerchantApplication] 商家身份注册失败:', error);
      toast.error(`商家身份注册失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 显示审核资料表单
  const handleShowApplicationForm = () => {
    setShowApplicationForm(true);
  };

  // 隐藏审核资料表单
  const handleHideApplicationForm = () => {
    setShowApplicationForm(false);
    setApplicationData({
      name: '',
      description: '',
      category: '',
      contactPhone: '',
      contactEmail: '',
      address: '',
      businessLicense: '',
      logo: ''
    });
    setApplicationErrors({});
  };

  // 处理审核资料表单输入
  const handleApplicationInputChange = (field, value) => {
    setApplicationData(prev => ({
      ...prev,
      [field]: value
    }));

    // 清除对应字段的错误
    if (applicationErrors[field]) {
      setApplicationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 验证审核资料表单
  const validateApplicationForm = () => {
    const errors = {};

    if (!applicationData.name.trim()) {
      errors.name = '商家名称不能为空';
    } else if (applicationData.name.length > 100) {
      errors.name = '商家名称不能超过100个字符';
    }

    if (!applicationData.description.trim()) {
      errors.description = '商家描述不能为空';
    } else if (applicationData.description.length > 500) {
      errors.description = '商家描述不能超过500个字符';
    }

    if (!applicationData.category.trim()) {
      errors.category = '请选择经营类目';
    }

    if (!applicationData.contactPhone.trim()) {
      errors.contactPhone = '联系电话不能为空';
    } else if (!/^1[3-9]\d{9}$/.test(applicationData.contactPhone)) {
      errors.contactPhone = '请输入有效的手机号码';
    }

    if (!applicationData.contactEmail.trim()) {
      errors.contactEmail = '联系邮箱不能为空';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(applicationData.contactEmail)) {
      errors.contactEmail = '请输入有效的邮箱地址';
    }

    if (!applicationData.address.trim()) {
      errors.address = '经营地址不能为空';
    } else if (applicationData.address.length > 200) {
      errors.address = '经营地址不能超过200个字符';
    }

    setApplicationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 提交审核资料
  const handleSubmitApplication = async (e) => {
    e.preventDefault();

    if (!validateApplicationForm()) {
      toast.error('请检查表单信息');
      return;
    }

    setIsLoading(true);
    try {
      // 保存申请详细信息到本地存储
      const saved = merchantApplicationStorage.saveApplication(account, applicationData);
      if (!saved) {
        throw new Error('保存申请信息失败');
      }

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        ...ethersSigner
      };

      // 调用提交审核申请API
      const result = await submitMerchantVerification({ signer });

      // 申请提交成功

      // 成功提示
      toast.success('🎉 审核资料提交成功！等待管理员审核', {
        duration: 4000,
        position: 'top-center',
      });

      // 隐藏表单并重新检查状态
      setShowApplicationForm(false);
      await checkUserStatus();

    } catch (error) {
      console.error('❌ [MerchantApplication] 提交审核资料失败:', error);
      toast.error(`提交审核资料失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 移除表单处理和图片上传函数，一键注册不需要

  // 格式化地址显示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // 监听账户变化
  useEffect(() => {
    checkUserStatus();
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  // 如果没有连接钱包
  if (!account) {
    return (
      <div className="merchant-application">
        <div className="application-card">
          <div className="application-header">
            <h3>🏪 商家入驻申请</h3>
            <p>请先连接钱包以查看申请状态</p>
          </div>
        </div>
      </div>
    );
  }

  // 加载中状态
  if (isChecking) {
    return (
      <div className="merchant-application">
        <div className="application-card">
          <div className="application-header">
            <h3>🏪 商家入驻申请</h3>
            <p>🔄 正在检查申请状态...</p>
          </div>
        </div>
      </div>
    );
  }

  // 用户未注册代理系统
  if (!isRegisteredUser) {
    return (
      <div className="merchant-application">
        <div className="application-card">
          <div className="application-header">
            <h3>🏪 商家入驻申请</h3>
            <p>申请商家需要先注册代理系统</p>
          </div>

          <div className="requirement-notice">
            <div className="notice-icon">⚠️</div>
            <div className="notice-content">
              <h4>申请条件</h4>
              <p>您需要先完成代理系统注册才能申请成为商家</p>
              <p>请前往上方的"用户注册"完成注册</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 已注册商家但未提交审核申请
  if (merchantStatus.isRegistered && !merchantStatus.isVerified && merchantInfo) {
    // 检查是否已提交审核申请
    const hasSubmittedVerification = merchantStatus.hasSubmittedVerification;

    if (hasSubmittedVerification) {
      // 已提交审核申请，等待审核
      return (
        <div className="merchant-application">
          <div className="application-card merchant-info">
            <div className="application-header">
              <h3>📋 商家申请信息</h3>
              <p>您的商家申请资料已提交，请耐心等待管理员审核</p>
            </div>

            <div className="merchant-details">
              <div className="info-section">
                <h4 className="section-title">📊 商家信息</h4>
                <div className="info-row">
                  <span className="label">商家地址：</span>
                  <span className="value">{formatAddress(account)}</span>
                </div>
                <div className="info-row">
                  <span className="label">身份状态：</span>
                  <span className="status registered">✅ 已注册</span>
                </div>
                <div className="info-row">
                  <span className="label">申请状态：</span>
                  <span className="status pending">⏳ 审核中</span>
                </div>
                <div className="info-row">
                  <span className="label">注册时间：</span>
                  <span className="value">{new Date(merchantInfo.createTime * 1000).toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div className="notice-section">
              <div className="notice-icon">ℹ️</div>
              <div className="notice-content">
                <h4>审核说明</h4>
                <p>管理员正在审核您的商家申请，审核通过后您将可以：</p>
                <ul>
                  <li>创建和管理商品</li>
                  <li>查看销售数据</li>
                  <li>管理订单和积分</li>
                </ul>
                <p>如有疑问，请联系平台客服。</p>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // 已注册商家身份，但未提交审核申请
      if (showApplicationForm) {
        // 显示审核资料填写表单
        return (
          <div className="merchant-application">
            <div className="application-card">
              <div className="application-header">
                <h3>📝 填写审核资料</h3>
                <p>请填写详细的商家信息，提交给管理员审核</p>
              </div>

              <form className="application-form" onSubmit={handleSubmitApplication}>
                {/* 基本信息 */}
                <div className="form-section">
                  <h4 className="section-title">📋 商家基本信息</h4>

                  <div className="form-group">
                    <label className="form-label">商家名称 *</label>
                    <input
                      type="text"
                      className={`form-input ${applicationErrors.name ? 'error' : ''}`}
                      placeholder="请输入商家名称（最多100字符）"
                      value={applicationData.name}
                      onChange={(e) => handleApplicationInputChange('name', e.target.value)}
                      maxLength={100}
                    />
                    {applicationErrors.name && (
                      <div className="error-message">{applicationErrors.name}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">商家描述 *</label>
                    <textarea
                      className={`form-textarea ${applicationErrors.description ? 'error' : ''}`}
                      placeholder="请输入商家描述，包括主营业务、特色服务等（最多500字符）"
                      value={applicationData.description}
                      onChange={(e) => handleApplicationInputChange('description', e.target.value)}
                      maxLength={500}
                      rows={4}
                    />
                    {applicationErrors.description && (
                      <div className="error-message">{applicationErrors.description}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">经营类目 *</label>
                    <select
                      className={`form-select ${applicationErrors.category ? 'error' : ''}`}
                      value={applicationData.category}
                      onChange={(e) => handleApplicationInputChange('category', e.target.value)}
                    >
                      <option value="">请选择经营类目</option>
                      <option value="electronics">数码电子</option>
                      <option value="clothing">服装鞋帽</option>
                      <option value="food">食品饮料</option>
                      <option value="home">家居用品</option>
                      <option value="beauty">美妆护肤</option>
                      <option value="sports">运动户外</option>
                      <option value="books">图书文具</option>
                      <option value="health">健康保健</option>
                      <option value="other">其他</option>
                    </select>
                    {applicationErrors.category && (
                      <div className="error-message">{applicationErrors.category}</div>
                    )}
                  </div>
                </div>

                {/* 联系信息 */}
                <div className="form-section">
                  <h4 className="section-title">📞 联系信息</h4>

                  <div className="form-group">
                    <label className="form-label">联系电话 *</label>
                    <input
                      type="tel"
                      className={`form-input ${applicationErrors.contactPhone ? 'error' : ''}`}
                      placeholder="请输入手机号码"
                      value={applicationData.contactPhone}
                      onChange={(e) => handleApplicationInputChange('contactPhone', e.target.value)}
                      maxLength={11}
                    />
                    {applicationErrors.contactPhone && (
                      <div className="error-message">{applicationErrors.contactPhone}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">联系邮箱 *</label>
                    <input
                      type="email"
                      className={`form-input ${applicationErrors.contactEmail ? 'error' : ''}`}
                      placeholder="请输入邮箱地址"
                      value={applicationData.contactEmail}
                      onChange={(e) => handleApplicationInputChange('contactEmail', e.target.value)}
                      maxLength={100}
                    />
                    {applicationErrors.contactEmail && (
                      <div className="error-message">{applicationErrors.contactEmail}</div>
                    )}
                  </div>

                  <div className="form-group">
                    <label className="form-label">经营地址 *</label>
                    <textarea
                      className={`form-textarea ${applicationErrors.address ? 'error' : ''}`}
                      placeholder="请输入详细的经营地址（最多200字符）"
                      value={applicationData.address}
                      onChange={(e) => handleApplicationInputChange('address', e.target.value)}
                      maxLength={200}
                      rows={3}
                    />
                    {applicationErrors.address && (
                      <div className="error-message">{applicationErrors.address}</div>
                    )}
                  </div>
                </div>

                {/* 可选信息 */}
                <div className="form-section">
                  <h4 className="section-title">📄 可选信息</h4>

                  <div className="form-group">
                    <label className="form-label">营业执照号（可选）</label>
                    <input
                      type="text"
                      className="form-input"
                      placeholder="请输入营业执照注册号"
                      value={applicationData.businessLicense}
                      onChange={(e) => handleApplicationInputChange('businessLicense', e.target.value)}
                      maxLength={30}
                    />
                    <div className="input-hint">
                      提供营业执照号有助于加快审核速度
                    </div>
                  </div>
                </div>

                {/* 提交按钮 */}
                <div className="form-actions">
                  <button
                    type="button"
                    className="cancel-button"
                    onClick={handleHideApplicationForm}
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className={`submit-button primary-action ${isLoading ? 'loading' : ''}`}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <span className="loading-spinner">⏳</span>
                        <span className="button-text">提交中...</span>
                      </>
                    ) : (
                      <>
                        <span className="submit-icon">🚀</span>
                        <span className="button-text">提交审核申请</span>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        );
      } else {
        // 显示填写资料按钮
        return (
          <div className="merchant-application">
            <div className="application-card merchant-info">
              <div className="application-header">
                <h3>✅ 商家身份已注册</h3>
                <p>您已成功注册商家身份，现在可以提交审核申请</p>
              </div>

              <div className="merchant-details">
                <div className="info-section">
                  <h4 className="section-title">📊 商家信息</h4>
                  <div className="info-row">
                    <span className="label">商家地址：</span>
                    <span className="value">{formatAddress(account)}</span>
                  </div>
                  <div className="info-row">
                    <span className="label">身份状态：</span>
                    <span className="status registered">✅ 已注册</span>
                  </div>
                  <div className="info-row">
                    <span className="label">申请状态：</span>
                    <span className="status pending-submit">📝 待提交申请资料</span>
                  </div>
                  <div className="info-row">
                    <span className="label">注册时间：</span>
                    <span className="value">{new Date(merchantInfo.createTime * 1000).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="notice-section">
                <div className="notice-icon">📝</div>
                <div className="notice-content">
                  <h4>下一步：填写审核资料</h4>
                  <p>您已成功注册商家身份，现在需要填写详细的审核资料：</p>
                  <ul>
                    <li>• 点击下方按钮填写商家详细信息</li>
                    <li>• 包括联系方式、经营类目、资质证明等</li>
                    <li>• 填写完成后提交给管理员审核</li>
                    <li>• 审核通过后即可开始经营</li>
                  </ul>
                </div>
              </div>

              <div className="form-actions full-width">
                <button
                  type="button"
                  className="submit-button primary-action"
                  onClick={handleShowApplicationForm}
                >
                  <span className="submit-icon">📝</span>
                  <span className="button-text">填写审核资料</span>
                </button>
              </div>
            </div>
          </div>
        );
      }
    }
  }

  // 已经是认证商家
  if (isMerchantUser && merchantInfo) {
    return (
      <div className="merchant-application">
        <div className="application-card merchant-info">
          <div className="application-header">
            <h3>✅ 商家信息</h3>
            <p>您已通过商家认证，可以使用完整的商家管理功能</p>
          </div>

          <div className="merchant-details">
            <div className="info-section">
              <h4 className="section-title">📊 基本信息</h4>
              <div className="info-row">
                <span className="label">商家地址：</span>
                <span className="value">{formatAddress(account)}</span>
              </div>
              <div className="info-row">
                <span className="label">商家名称：</span>
                <span className="value">{merchantInfo.name}</span>
              </div>
              <div className="info-row">
                <span className="label">商家描述：</span>
                <span className="value">{merchantInfo.description}</span>
              </div>
              <div className="info-row">
                <span className="label">状态：</span>
                <span className={`status ${merchantInfo.isActive ? 'active' : 'inactive'}`}>
                  {merchantInfo.isActive ? '✅ 已激活' : '⏳ 待审核'}
                </span>
              </div>
              <div className="info-row">
                <span className="label">认证状态：</span>
                <span className={`status ${merchantInfo.isVerified ? 'verified' : 'unverified'}`}>
                  {merchantInfo.isVerified ? '✅ 已认证' : '⏳ 未认证'}
                </span>
              </div>
            </div>

            <div className="info-section">
              <h4 className="section-title">📈 经营数据</h4>
              <div className="info-row">
                <span className="label">总销售额：</span>
                <span className="value">{merchantInfo.totalSales} USDT</span>
              </div>
              <div className="info-row">
                <span className="label">总订单数：</span>
                <span className="value">{merchantInfo.totalOrders} 单</span>
              </div>
              <div className="info-row">
                <span className="label">总积分：</span>
                <span className="value">{merchantInfo.totalPoints} 分</span>
              </div>
              <div className="info-row">
                <span className="label">已兑换积分：</span>
                <span className="value">{merchantInfo.exchangedPoints} 分</span>
              </div>
            </div>
          </div>

          {/* 商家管理后台入口 */}
          <div className="form-actions full-width">
            <button
              type="button"
              className="submit-button primary-action full-width-button"
              onClick={() => window.location.href = '/merchant'}
            >
              <span className="submit-icon">🏪</span>
              <span className="button-text">进入商家后台</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 显示商家注册按钮
  return (
    <div className="merchant-application">
      <div className="application-card">
        <div className="application-header">
          <h3>🏪 商家身份注册</h3>
          <p>点击下方按钮注册商家身份，开启您的商家之旅</p>
        </div>



        <div className="registration-info">
          <div className="info-section">
            <h4 className="section-title">📋 注册流程说明</h4>
            <div className="flow-steps">
              <div className="flow-step">
                <span className="step-icon">🏪</span>
                <div className="step-content">
                  <h5>第一步：注册商家身份</h5>
                  <p>在区块链上创建商家身份记录，获得商家资格</p>
                </div>
              </div>
              <div className="flow-step">
                <span className="step-icon">📝</span>
                <div className="step-content">
                  <h5>第二步：提交审核申请</h5>
                  <p>填写详细的商家信息和经营资料，提交给管理员审核</p>
                </div>
              </div>
              <div className="flow-step">
                <span className="step-icon">⏳</span>
                <div className="step-content">
                  <h5>第三步：等待审核通过</h5>
                  <p>管理员将在1-3个工作日内完成审核，审核通过后即可开始经营</p>
                </div>
              </div>
              <div className="flow-step">
                <span className="step-icon">🚀</span>
                <div className="step-content">
                  <h5>第四步：开始商家经营</h5>
                  <p>创建商品、管理订单、查看销售数据、兑换销售积分</p>
                </div>
              </div>
            </div>
          </div>

          <div className="info-section">
            <h4 className="section-title">✨ 商家权益</h4>
            <div className="benefits-list">
              <div className="benefit-item">
                <span className="benefit-icon">📦</span>
                <span className="benefit-text">创建和管理商品</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">📋</span>
                <span className="benefit-text">处理用户订单</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">💰</span>
                <span className="benefit-text">获得销售积分</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">🔄</span>
                <span className="benefit-text">兑换积分为USDT</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">📊</span>
                <span className="benefit-text">查看销售数据统计</span>
              </div>
              <div className="benefit-item">
                <span className="benefit-icon">🎯</span>
                <span className="benefit-text">参与平台推广分成</span>
              </div>
            </div>
          </div>
        </div>

        {/* 注册按钮 */}
        <div className="form-actions full-width">
          <button
            type="button"
            className={`submit-button primary-action ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
            onClick={handleQuickRegister}
          >
            {isLoading ? (
              <>
                <span className="loading-spinner">⏳</span>
                <span className="button-text">注册中...</span>
              </>
            ) : (
              <>
                <span className="submit-icon">🚀</span>
                <span className="button-text">一键注册商家身份</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
