{"network": "bscTestnet", "timestamp": "2025-08-01T07:51:51.377Z", "deployer": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "contract": {"name": "GroupBuyRoomMinimal", "proxyAddress": "0x8580424D25C3B5dAdb4D7a0bcB991C6e8d720551", "implementationAddress": "0xCF37BA4cF9c86fe110f0041671A24e94F4D73fd6", "owner": "0x012F049B3947C45C400AF5909c5b629BEac0C0b3", "timelock": "0x69bf4d498BBd3580f126F3442FFAE9aAaC4c3400", "usdtToken": "0x2c6431CA0A2e7F5D2015179BdD7241fA1714DE86"}, "features": ["UUPS_UPGRADEABLE", "STORAGE_VALIDATION", "TIMELOCK_CONTROLLED", "GROUP_BUY_FUNCTIONALITY"]}