import React from "react";
import { useState, useEffect, useMemo } from "react";
import { useAccount } from "wagmi";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import UserRegistration from "@/components/UserRegistration";
import MerchantApplication from "@/components/MerchantApplication";
import AddressManagement from "@/components/Profile/AddressManagement";
import OrderHistory from "@/components/Profile/OrderHistory";
import ReferralRewards from "@/components/Profile/ReferralRewards";
import SettingsPage from "@/pages/Profile/Settings";
import UserInfo from "@/components/Profile/UserInfo";
import SettingsEntry from "@/components/Profile/SettingsEntry";
import { isMerchant } from "@/apis/mallApi";
import "./index.css";

export default function ProfileHome() {
  const { address: account, isConnected } = useAccount();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAdmin } = useIsAdmin();

  // 标签页状态
  const [activeTab, setActiveTab] = useState("profile");

  // 商家状态
  const [merchantStatus, setMerchantStatus] = useState({
    isRegistered: false,
    isVerified: false,
    isLoading: true,
  });

  // 标签页配置
  const tabs = useMemo(
    () => [
      { id: "profile", label: "👤 个人信息", icon: "👤" },
      { id: "orders", label: "📦 我的订单", icon: "📦" },
      { id: "rewards", label: "🎁 推荐奖励", icon: "🎁" },
      { id: "merchant", label: "🏪 商家中心", icon: "🏪" },
      { id: "settings", label: "⚙️ 设置管理", icon: "⚙️" },
    ],
    []
  );

  // 检查商家状态
  const checkMerchantStatus = async () => {
    if (!account || !isConnected) {
      setMerchantStatus({
        isRegistered: false,
        isVerified: false,
        isLoading: false,
      });
      return;
    }

    try {
      const merchantData = await isMerchant({ userAddress: account });
      const isRegistered = merchantData.isRegistered;
      const isVerified = merchantData.isVerified;

      setMerchantStatus({
        isRegistered,
        isVerified,
        isLoading: false,
      });
    } catch (error) {
      console.error("❌ [ProfileHome] 检查商家状态失败:", error);
      setMerchantStatus({
        isRegistered: false,
        isVerified: false,
        isLoading: false,
      });
    }
  };

  // 跳转到商家后台
  const goToMerchantDashboard = () => {
    navigate("/merchant");
  };

  // 初始化标签页（从URL参数获取）
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab && tabs.some((t) => t.id === tab)) {
      setActiveTab(tab);
    }
  }, [searchParams, tabs]);

  // 组件挂载时检查商家状态
  useEffect(() => {
    checkMerchantStatus();
  }, [account, isConnected]); // eslint-disable-line react-hooks/exhaustive-deps

  // 渲染内容区域
  function renderTabContent() {
    switch (activeTab) {
      case "profile":
        return (
          <div className="profile-section">
            <h2 className="profile-title">个人信息</h2>
            <UserInfo />

            {/* 用户注册组件 - 始终显示，组件内部会根据注册状态显示不同内容 */}
            <div className="registration-section">
              <h3 className="section-subtitle">代理系统</h3>
              <UserRegistration />
            </div>
          </div>
        );
      case "orders":
        return (
          <div className="profile-section">
            <h2 className="profile-title">我的订单</h2>
            <OrderHistory />
          </div>
        );
      case "rewards":
        return (
          <div className="profile-section">
            <h2 className="profile-title">推荐奖励</h2>
            <ReferralRewards />
          </div>
        );
      case "merchant":
        return (
          <div className="profile-section">
            <h2 className="profile-title">趣拼团商家中心</h2>
            <MerchantApplication />
          </div>
        );
      case "settings":
        return (
          <div className="profile-section">
            <h2 className="profile-title">设置管理</h2>
            <SettingsPage />
          </div>
        );
      default:
        return null;
    }
  }

  return (
    <div className="profile-home">
      {/* 页面头部 */}
      <div className="profile-header">
        <h1>👤 个人中心</h1>
        <p>管理您的账户信息和代理系统设置</p>
      </div>

      {/* 钱包连接状态 */}
      <div className="wallet-status">
        {isConnected ? (
          <div className="wallet-connected">
            <span className="status-indicator connected">🟢</span>
            <span>
              钱包已连接：{account?.slice(0, 6)}...{account?.slice(-4)}
            </span>
            {/* 调试信息 */}
            <div className="debug-info">
              <span className="debug-label">商家状态:</span>
              <span className="debug-value">
                注册: {merchantStatus.isRegistered ? "✅" : "❌"} | 认证:{" "}
                {merchantStatus.isVerified ? "✅" : "❌"} | 加载:{" "}
                {merchantStatus.isLoading ? "🔄" : "✅"}
              </span>
            </div>
          </div>
        ) : (
          <div className="wallet-disconnected">
            <span className="status-indicator disconnected">🔴</span>
            <span>请连接钱包以使用个人中心功能</span>
          </div>
        )}
      </div>

      {/* 导航标签页 */}
      <div className="profile-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? "active" : ""}`}
            onClick={() => {
              setActiveTab(tab.id);
              navigate(`?tab=${tab.id}`);
            }}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* 功能模块 */}
      <div className="profile-modules">{renderTabContent()}</div>
    </div>
  );
}
