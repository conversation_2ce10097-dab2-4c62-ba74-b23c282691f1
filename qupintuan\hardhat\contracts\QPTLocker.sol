// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts/utils/Address.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IAgentSystem.sol";
import "./interfaces/IQPTLock.sol";
import "./StorageValidator.sol";

error InsufficientQPTBalance(uint256 currentBalance, uint256 requiredBalance);

/**
 * @title QPTLocker - 修复版本
 * @notice 修复了 rewardWinner 函数中的奖励查找逻辑
 * @dev 主要修复：
 *      1. RoomLock 结构体添加 usdtAmount 字段
 *      2. lockForRoom 函数保存 USDT 金额
 *      3. rewardWinner 函数使用正确的键查找奖励
 */
contract QPTLocker is Initializable, StorageValidator, OwnableUpgradeable, PausableUpgradeable, ReentrancyGuardUpgradeable, IQPTLock, UUPSUpgradeable {
    using SafeERC20 for IERC20;
    using Address for address;

    // 合约版本
    string public constant CONTRACT_VERSION = "1.4.0-lockdays-management";

    IERC20 public qptToken;
    IAgentSystem public agentSystem;

    address public groupBuyRoomAddress;
    address public nodeStakingContract;
    address public timelock;

    // 修复后的 RoomLock 结构体，添加 usdtAmount 字段
    struct RoomLock {
        address creator;
        uint256 amount;        // QPT 锁仓数量
        uint256 usdtAmount;    // 对应的 USDT 金额（新增 - 修复关键）
        uint256 unlockTime;
        bool isSuccess;
        bool isClaimed;
    }

    struct AmountMapping {
        uint256 usdtAmount;
        uint256 lockAmount;
        uint256 rewardAmount;
    }

    // 推荐奖励历史记录结构
    struct ReferrerRewardInfo {
        uint256 roomId;
        address winner;
        uint256 amount;
        uint256 timestamp;
        bool claimed;
    }

    // 房间锁仓信息
    mapping(uint256 => RoomLock) public roomLocks;
    
    // 金额映射
    mapping(uint256 => AmountMapping) public amountMappings;
    
    // 代理等级对应的锁仓天数
    mapping(uint8 => uint256) public lockDays;
    
    // 节点锁仓信息
    mapping(address => uint256) public lockedNodeQPT;
    
    // 推荐人待领取奖励
    mapping(address => mapping(uint256 => uint256)) public referrerPendingRewards;
    
    // 推荐人已领取奖励记录
    mapping(address => mapping(uint256 => bool)) public referrerClaimedRewards;
    
    // 推荐奖励历史记录
    mapping(address => ReferrerRewardInfo[]) public referrerRewardHistory;
    
    // 奖励是否已分发
    mapping(uint256 => bool) public hasRewarded;

    // QPT领取统计
    uint256 public totalClaimedQPT;  // 总已领取的QPT数量
    mapping(address => uint256) public userClaimedQPT;  // 用户已领取的QPT统计

    // 事件定义
    event RoomQPTLocked(uint256 indexed roomId, address indexed creator, uint256 amount);
    event RoomQPTUnlockable(uint256 indexed roomId, address indexed creator, uint256 amount);
    event RoomMarkedSuccess(uint256 indexed roomId, address indexed creator, uint256 unlockTime);
    event NodeQPTLocked(address indexed user, uint256 amount);
    event NodeQPTUnlocked(address indexed user, uint256 amount);
    event QPTLocked(address indexed user, uint256 amount);
    event QPTUnlocked(address indexed user, uint256 amount);
    event RewardDistributed(uint256 indexed roomId, address indexed winner, address indexed referrer, uint256 amount);
    event ReferrerRewardPending(uint256 indexed roomId, address indexed winner, address indexed referrer, uint256 amount);
    event ReferrerRewardClaimed(uint256 indexed roomId, address indexed referrer, uint256 amount);
    event ForcedUnlocked(uint256 indexed roomId, uint256 unlockTime);
    event GroupBuyRoomAddressUpdated(address indexed groupBuyRoomAddress);
    event NodeStakingContractUpdated(address indexed nodeStakingContract);
    event TimelockUpdated(address indexed timelock);
    event EmergencyQPTWithdrawn(address indexed to, uint256 amount);
    event BNBReceived(address indexed sender, uint256 amount);
    event BNBWithdrawn(address indexed to, uint256 amount);
    event RoomQPTUnlocked(uint256 indexed roomId, address indexed user, uint256 amount);
    event LockDaysUpdated(uint8 indexed level, uint256 indexed lockDays);
    // QPT收集记录事件已移除

    modifier onlyGroupBuyRoom() {
        require(msg.sender == groupBuyRoomAddress, "Only GroupBuyRoom");
        _;
    }

    modifier onlyNodeStaking() {
        require(msg.sender == nodeStakingContract, "Only NodeStaking");
        _;
    }

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    function initialize(
        address _qptToken,
        address _agentSystem,
        address _timelock
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(_qptToken != address(0), "Invalid QPT token");
        require(_agentSystem != address(0), "Invalid AgentSystem");
        require(_timelock != address(0), "Invalid timelock");

        qptToken = IERC20(_qptToken);
        agentSystem = IAgentSystem(_agentSystem);
        timelock = _timelock;

        _initAmountMappings();
        _initLockDays();
    }

    function _initLockDays() internal {
        lockDays[0] = 5;
        lockDays[1] = 4;
        lockDays[2] = 3;
        lockDays[3] = 2;
        lockDays[4] = 1;
    }

    function _initAmountMappings() internal {
        // 初始化锁仓和奖励数量
        amountMappings[30e6] = AmountMapping(30e6, 100 * 1e18, 15e17);
        amountMappings[50e6] = AmountMapping(50e6, 150 * 1e18, 25e17);
        amountMappings[100e6] = AmountMapping(100e6, 200 * 1e18, 5e18);
        amountMappings[200e6] = AmountMapping(200e6, 300 * 1e18, 10e18);
        amountMappings[500e6] = AmountMapping(500e6, 400 * 1e18, 25e18);
        amountMappings[1000e6] = AmountMapping(1000e6, 500 * 1e18, 50e18);
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证QPT锁定合约状态
        if (address(qptToken) == address(0)) return false;
        if (address(agentSystem) == address(0)) return false;
        if (groupBuyRoomAddress == address(0)) return false;
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            address(qptToken),
            address(agentSystem),
            groupBuyRoomAddress,
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // QPTLocker的存储修复逻辑
        emit StorageFixed(address(this), "QPTLocker storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    /// @notice 授权升级
    /// @param newImplementation 新实现地址
    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    /// @notice 设置 Timelock 地址
    /// @param _timelock Timelock 地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Zero address");
        timelock = _timelock;
        emit TimelockUpdated(_timelock);
    }

    /// @notice 设置 GroupBuyRoom 合约地址
    /// @param _groupBuyRoomAddress GroupBuyRoom 合约地址
    function setGroupBuyRoomAddress(address _groupBuyRoomAddress) external onlyOwner {
        require(_groupBuyRoomAddress != address(0), "Zero address");
        groupBuyRoomAddress = _groupBuyRoomAddress;
        emit GroupBuyRoomAddressUpdated(_groupBuyRoomAddress);
    }

    /// @notice 设置 NodeStaking 合约地址
    /// @param _nodeStakingContract NodeStaking 合约地址
    function setNodeStakingContract(address _nodeStakingContract) external onlyOwner {
        require(_nodeStakingContract != address(0), "Zero address");
        nodeStakingContract = _nodeStakingContract;
        emit NodeStakingContractUpdated(_nodeStakingContract);
    }

    // 回购合约地址设置函数已移除

    /// @notice 为拼团房间锁仓 QPT - 修复版本
    /// @param roomId 拼团房间 ID
    /// @param usdtAmount USDT 金额
    function lockForRoom(uint256 roomId, uint256 usdtAmount) external override(IQPTLock) nonReentrant whenNotPaused {
        AmountMapping storage amtMap = amountMappings[usdtAmount];
        require(amtMap.lockAmount > 0, "Invalid amount");
        require(roomLocks[roomId].creator == address(0), "Room already locked");

        uint256 amount = amtMap.lockAmount;
        uint256 bal = qptToken.balanceOf(msg.sender);
        if (bal < amount) revert InsufficientQPTBalance(bal, amount);
        qptToken.safeTransferFrom(msg.sender, address(this), amount);

        // 修复：保存 USDT 金额
        roomLocks[roomId] = RoomLock({
            creator: msg.sender,
            amount: amount,
            usdtAmount: usdtAmount,  // 新增：保存 USDT 金额
            unlockTime: block.timestamp + 1 days,
            isSuccess: false,
            isClaimed: false
        });
        emit RoomQPTLocked(roomId, msg.sender, amount);
    }

    /// @notice 奖励拼团赢家 - 修复版本
    /// @param roomId 拼团房间 ID
    /// @param winner 赢家地址
    function rewardWinner(uint256 roomId, address winner) external onlyGroupBuyRoom nonReentrant whenNotPaused {
        require(!hasRewarded[roomId], "Reward already distributed");
        RoomLock storage room = roomLocks[roomId];
        require(room.creator != address(0), "Invalid room");
        require(room.isSuccess, "Room not successful");

        // 修复：使用 USDT 金额查找奖励
        AmountMapping storage am = amountMappings[room.usdtAmount];
        uint256 reward = am.rewardAmount;
        require(reward > 0, "Invalid reward");

        hasRewarded[roomId] = true;

        // 给赢家发奖励
        qptToken.safeTransfer(winner, reward);

        // 统计已领取的QPT
        totalClaimedQPT += reward;
        userClaimedQPT[winner] += reward;

        emit RewardDistributed(roomId, winner, address(0), reward);

        // 如果有邀请人，记录待领取奖励（不直接转账）
        address ref = getReferrer(winner);
        if (ref != address(0)) {
            referrerPendingRewards[ref][roomId] = reward;

            // 添加到推荐奖励历史记录
            referrerRewardHistory[ref].push(ReferrerRewardInfo({
                roomId: roomId,
                winner: winner,
                amount: reward,
                timestamp: block.timestamp,
                claimed: false
            }));

            emit ReferrerRewardPending(roomId, winner, ref, reward);
        }
    }

    /// @notice 获取拼团房间信息 - 修复版本
    /// @param roomId 拼团房间 ID
    /// @return creator 房主地址
    /// @return amount 锁仓数量
    /// @return unlockTime 解锁时间
    /// @return isSuccess 是否成功
    /// @return isClaimed 是否已领取
    function getRoomInfo(uint256 roomId) external view override(IQPTLock) returns (
        address creator,
        uint256 amount,
        uint256 unlockTime,
        bool isSuccess,
        bool isClaimed
    ) {
        RoomLock storage room = roomLocks[roomId];
        return (
            room.creator,
            room.amount,
            room.unlockTime,
            room.isSuccess,
            room.isClaimed
        );
    }

    /// @notice 获取房间的 USDT 金额
    /// @param roomId 拼团房间 ID
    /// @return USDT 金额
    function getRoomUSDTAmount(uint256 roomId) external view returns (uint256) {
        return roomLocks[roomId].usdtAmount;
    }

    /// @notice 获取用户的推荐人
    /// @param user 用户地址
    /// @return 推荐人地址
    function getReferrer(address user) public view returns (address) {
        return agentSystem.getInviter(user);
    }

    /// @notice 获取用户的代理等级
    /// @param user 用户地址
    /// @return 代理等级
    function getAgentLevel(address user) public view returns (uint8) {
        return agentSystem.getLevel(user);
    }

    /// @notice 强制解锁拼团房间的 QPT
    /// @param roomId 拼团房间 ID
    function forceUnlock(uint256 roomId) external override(IQPTLock) onlyGroupBuyRoom {
        RoomLock storage room = roomLocks[roomId];
        room.unlockTime = block.timestamp;
        emit RoomQPTUnlockable(roomId, room.creator, room.amount);
        emit ForcedUnlocked(roomId, block.timestamp);
    }

    /// @notice 标记拼团房间为成功
    /// @param roomId 拼团房间 ID
    function markRoomSuccess(uint256 roomId) external onlyGroupBuyRoom {
        RoomLock storage room = roomLocks[roomId];
        require(room.creator != address(0), "Invalid room");
        require(!room.isSuccess, "Already marked");

        room.isSuccess = true;
        uint8 level = getAgentLevel(room.creator);
        require(level <= 4, "Invalid level");
        room.unlockTime = block.timestamp + lockDays[level] * 1 days;
        emit RoomMarkedSuccess(roomId, room.creator, room.unlockTime);
    }

    /// @notice 领取已解锁的 QPT
    /// @param roomId 拼团房间 ID
    function claimLockedQPT(uint256 roomId) external override(IQPTLock) nonReentrant whenNotPaused {
        RoomLock storage room = roomLocks[roomId];
        require(room.creator != address(0), "Invalid room");
        require(room.creator == msg.sender, "Not creator");
        require(!room.isClaimed, "Already claimed");
        require(block.timestamp >= room.unlockTime, "Still locked");

        room.isClaimed = true;
        qptToken.safeTransfer(msg.sender, room.amount);
        emit RoomQPTUnlocked(roomId, msg.sender, room.amount);
    }

    /// @notice 房主是否已为指定房间锁仓
    /// @param roomId 拼团房间 ID
    /// @return 是否已锁仓
    function hasLocked(uint256 roomId) external view override(IQPTLock) returns (bool) {
        return roomLocks[roomId].creator != address(0);
    }

    /// @notice 锁定节点 QPT
    /// @param user 用户地址
    /// @param amount 锁定数量
    function lockNodeQPT(address user, uint256 amount) external override(IQPTLock) onlyNodeStaking nonReentrant whenNotPaused {
        qptToken.safeTransferFrom(user, address(this), amount);
        lockedNodeQPT[user] += amount;
        emit NodeQPTLocked(user, amount);
    }

    /// @notice 解锁节点 QPT
    /// @param user 用户地址
    function unlockNodeQPT(address user) external override(IQPTLock) onlyNodeStaking whenNotPaused {
        uint256 amount = lockedNodeQPT[user];
        require(amount > 0, "No QPT locked");
        lockedNodeQPT[user] = 0;
        qptToken.safeTransfer(user, amount);
        emit NodeQPTUnlocked(user, amount);
    }

    /// @notice 获取用户锁定的QPT数量
    /// @param user 用户地址
    /// @return 锁定的QPT数量
    function getUserLockedAmount(address user) external view override(IQPTLock) returns (uint256) {
        return lockedNodeQPT[user];
    }

    // 回购相关函数已移除：lockQPT, unlockQPT, collectToBuyback, transferSubsidy

    /// @notice 推荐人领取奖励
    /// @param roomId 拼团房间 ID
    function claimReferrerReward(uint256 roomId) external nonReentrant whenNotPaused {
        uint256 reward = referrerPendingRewards[msg.sender][roomId];
        require(reward > 0, "No pending reward");
        require(!referrerClaimedRewards[msg.sender][roomId], "Already claimed");

        // 标记为已领取
        referrerClaimedRewards[msg.sender][roomId] = true;
        referrerPendingRewards[msg.sender][roomId] = 0;

        // 更新历史记录中的领取状态
        _updateRewardHistoryClaimedStatus(msg.sender, roomId);

        // 转账给推荐人
        qptToken.safeTransfer(msg.sender, reward);

        // 统计已领取的QPT
        totalClaimedQPT += reward;
        userClaimedQPT[msg.sender] += reward;

        emit ReferrerRewardClaimed(roomId, msg.sender, reward);
    }

    /// @notice 获取推荐人待领取奖励
    /// @param referrer 推荐人地址
    /// @param roomId 房间ID
    /// @return 待领取奖励数量
    function getReferrerPendingReward(address referrer, uint256 roomId) external view returns (uint256) {
        return referrerPendingRewards[referrer][roomId];
    }

    /// @notice 检查推荐人是否已领取奖励
    /// @param referrer 推荐人地址
    /// @param roomId 房间ID
    /// @return 是否已领取
    function hasReferrerClaimed(address referrer, uint256 roomId) external view returns (bool) {
        return referrerClaimedRewards[referrer][roomId];
    }

    /// @notice 内部函数：更新历史记录中的领取状态
    /// @param referrer 推荐人地址
    /// @param roomId 房间ID
    function _updateRewardHistoryClaimedStatus(address referrer, uint256 roomId) internal {
        ReferrerRewardInfo[] storage history = referrerRewardHistory[referrer];
        for (uint256 i = 0; i < history.length; i++) {
            if (history[i].roomId == roomId && !history[i].claimed) {
                history[i].claimed = true;
                break;
            }
        }
    }

    /// @notice 设置指定等级的锁仓天数
    /// @param level 代理等级 (0-4)
    /// @param lockDaysCount 锁仓天数
    function setLockDays(uint8 level, uint256 lockDaysCount) external onlyOwner {
        require(level <= 4, "Invalid level");
        require(lockDaysCount <= 365, "Days too long"); // 最多1年
        lockDays[level] = lockDaysCount;
        emit LockDaysUpdated(level, lockDaysCount);
    }

    /// @notice 批量设置所有等级的锁仓天数
    /// @param lockDaysArray 等级0-4的锁仓天数数组
    function setBatchLockDays(uint256[5] calldata lockDaysArray) external onlyOwner {
        for (uint8 i = 0; i < 5; i++) {
            require(lockDaysArray[i] <= 365, "Days too long");
            lockDays[i] = lockDaysArray[i];
            emit LockDaysUpdated(i, lockDaysArray[i]);
        }
    }

    /// @notice 重新初始化锁仓天数（恢复默认值）
    function reinitializeLockDays() external onlyOwner {
        lockDays[0] = 5;
        lockDays[1] = 4;
        lockDays[2] = 3;
        lockDays[3] = 2;
        lockDays[4] = 1;

        for (uint8 i = 0; i < 5; i++) {
            emit LockDaysUpdated(i, lockDays[i]);
        }
    }

    /// @notice 暂停合约
    function pause() external onlyOwner {
        _pause();
    }

    /// @notice 恢复合约
    function unpause() external onlyOwner {
        _unpause();
    }

    /// @notice 紧急提取 QPT（仅所有者，用于合约迁移或紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量
    function withdrawQPT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= qptToken.balanceOf(address(this)), "Insufficient QPT balance");
        qptToken.safeTransfer(to, amount);
        emit EmergencyQPTWithdrawn(to, amount);
    }

    /// @notice 接收BNB转账，用于支付Gas费
    /// @dev 合约需要BNB来执行主动转账操作
    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 备用接收函数
    fallback() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 查询合约BNB余额
    /// @return 合约当前BNB余额（wei）
    function getBNBBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice 管理员提取BNB（紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量（wei）
    function withdrawBNB(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount <= address(this).balance, "Insufficient balance");

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");

        emit BNBWithdrawn(to, amount);
    }

    /// @notice 获取推荐人奖励历史记录数量
    /// @param referrer 推荐人地址
    /// @return 历史记录数量
    function getReferrerRewardCount(address referrer) external view returns (uint256) {
        return referrerRewardHistory[referrer].length;
    }

    /// @notice 获取推荐人的所有奖励历史记录
    /// @param referrer 推荐人地址
    /// @return 奖励历史记录数组
    function getReferrerRewardHistory(address referrer) external view returns (ReferrerRewardInfo[] memory) {
        return referrerRewardHistory[referrer];
    }

    /// @notice 获取推荐人指定索引的奖励记录
    /// @param referrer 推荐人地址
    /// @param index 记录索引
    /// @return 奖励记录信息
    function getReferrerRewardByIndex(address referrer, uint256 index) external view returns (ReferrerRewardInfo memory) {
        require(index < referrerRewardHistory[referrer].length, "Index out of bounds");
        return referrerRewardHistory[referrer][index];
    }

    /// @notice 获取QPT分发统计信息（前端兼容性函数）
    /// @return totalDistributed 总分发数量
    /// @return totalLocked 总锁定数量
    /// @return totalClaimed 总领取数量
    function getDistributionStats() external view returns (
        uint256 totalDistributed,
        uint256 totalLocked,
        uint256 totalClaimed
    ) {
        // 计算总锁定数量（所有房间的锁定QPT）
        totalLocked = qptToken.balanceOf(address(this));

        // 计算总分发数量（当前锁定 + 已领取）
        totalDistributed = totalLocked + totalClaimedQPT;
        totalClaimed = totalClaimedQPT; // 返回实际已领取的QPT数量

        return (totalDistributed, totalLocked, totalClaimed);
    }

    // 档位确定函数已移除

    // QPT收集统计查询函数已移除

    /// @notice 获取用户QPT领取统计
    /// @param user 用户地址
    /// @return amount 用户已领取的QPT总数量
    function getUserClaimedStats(address user) external view returns (uint256 amount) {
        return userClaimedQPT[user];
    }

    /// @notice 查询赢家奖励信息
    /// @param roomId 拼团房间ID
    /// @param winner 赢家地址
    /// @return winnerReward 赢家可领取的QPT奖励数量
    /// @return referrerReward 推荐人可领取的QPT奖励数量
    /// @return referrerAddress 推荐人地址
    /// @return isWinnerClaimed 赢家是否已领取
    /// @return isReferrerClaimed 推荐人是否已领取
    function getWinnerRewardInfo(uint256 roomId, address winner) external view returns (
        uint256 winnerReward,
        uint256 referrerReward,
        address referrerAddress,
        bool isWinnerClaimed,
        bool isReferrerClaimed
    ) {
        // 检查房间是否存在
        RoomLock storage room = roomLocks[roomId];
        require(room.creator != address(0), "Invalid room");

        // 检查是否已发放奖励
        if (!hasRewarded[roomId]) {
            return (0, 0, address(0), false, false);
        }

        // 获取奖励金额
        AmountMapping storage am = amountMappings[room.usdtAmount];
        uint256 reward = am.rewardAmount;

        // 获取推荐人信息
        address referrer = getReferrer(winner);

        // 赢家奖励（已发放）
        winnerReward = reward;
        isWinnerClaimed = true; // 赢家奖励在rewardWinner时直接发放

        // 推荐人奖励
        if (referrer != address(0)) {
            referrerReward = referrerPendingRewards[referrer][roomId];
            isReferrerClaimed = referrerClaimedRewards[referrer][roomId];
            referrerAddress = referrer;
        } else {
            referrerReward = 0;
            isReferrerClaimed = false;
            referrerAddress = address(0);
        }

        return (winnerReward, referrerReward, referrerAddress, isWinnerClaimed, isReferrerClaimed);
    }

    uint256[48] private __gap;
}
