// src/apis/groupBuy/queryOperations.js
// 查询模块：状态查询、权限检查等操作

/**
 * 获取用户权限信息
 * @param {Object} params - 参数对象
 * @param {number} chainId - 链ID
 * @param {string|number} roomId - 房间ID
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 返回用户权限信息
 */
export async function getUserPermissions({ chainId, roomId, userAddress }) {
  try {
    // 获取合约地址
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');

    // 获取合约 ABI
    const { ABIS } = await import('@/contracts/index.ts');
    const GroupBuyRoomABI = ABIS.GroupBuyRoom;

    // 创建公共客户端
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 获取房间信息
    const roomData = await publicClient.readContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator, tierAmount, participants] = roomData;

    // 检查用户权限
    const isCreator = creator.toLowerCase() === userAddress.toLowerCase();
    const isParticipant = participants.some(p => p.toLowerCase() === userAddress.toLowerCase());

    return {
      isCreator,
      isParticipant,
      canJoin: !isCreator && !isParticipant && participants.length < 8,
      canClose: isCreator && participants.length >= 8,
      canClaim: isParticipant || isCreator
    };
  } catch (error) {
    console.error('获取用户权限失败:', error);
    return {
      isCreator: false,
      isParticipant: false,
      canJoin: false,
      canClose: false,
      canClaim: false
    };
  }
}

/**
 * 获取用户支付金额
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链ID
 * @param {number} params.roomId - 房间ID
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<string>} 支付金额 (USDT格式)
 */
export async function getUserPaidAmount({ chainId, roomId, userAddress }) {
  try {
    // 恢复增强服务功能
    const { getUserPaidAmount } = await import('@/services/enhancedGroupBuyService');
    const paidAmount = await getUserPaidAmount(roomId, userAddress);
    return paidAmount;
  } catch (error) {
    console.error('获取用户支付金额失败:', error);
    return '0';
  }
}

/**
 * 检查角色领取状态
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链 ID
 * @param {string|number|bigint} params.roomId - 房间 ID
 * @param {string} params.userAddress - 用户地址
 * @param {string} params.role - 角色类型 ('creator', 'participant', 'winner')
 * @returns {Promise<boolean>} - 返回是否已领取
 */
export async function checkRoleClaimStatus({ chainId, roomId, userAddress, role }) {
  try {
    // 获取合约地址和 ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const contractABI = ABIS.GroupBuyRoom;

    // 创建公共客户端
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 使用 getUserClaimStatus 函数获取分角色的领取状态
    const [
      hasClaimedCreator,
      hasClaimedParticipant,
      hasClaimedWinner,
      canClaimCreator,
      canClaimParticipant,
      canClaimWinner
    ] = await publicClient.readContract({
      address: contractAddress,
      abi: contractABI,
      functionName: 'getUserClaimStatus',
      args: [BigInt(roomId), userAddress]
    });

    // 根据角色类型返回对应的领取状态
    let hasClaimed;
    switch (role) {
      case 'creator':
        hasClaimed = hasClaimedCreator;
        break;
      case 'winner':
        hasClaimed = hasClaimedWinner;
        break;
      case 'participant':
        hasClaimed = hasClaimedParticipant;
        break;
      default:
        throw new Error(`不支持的角色类型: ${role}`);
    }

    return hasClaimed;
  } catch (error) {
    // 如果是 ABI 函数不存在的错误，静默处理
    if (error.message?.includes('not found on ABI')) {
      return false; // 返回默认值
    }
    console.error(`检查${role}领取状态失败:`, error);
    return false;
  }
}

/**
 * 检查用户是否已领取奖励
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链 ID
 * @param {string|number|bigint} params.roomId - 房间 ID
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<boolean>} - 返回是否已领取
 * @throws {Error} 如果调用失败
 */
export async function checkClaimed({ chainId, roomId, userAddress }) {
  try {
    // 获取合约地址和 ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const contractABI = ABIS.GroupBuyRoom;

    // 创建公共客户端
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const hasClaimed = await publicClient.readContract({
      address: contractAddress,
      abi: contractABI,
      functionName: 'checkClaimed',
      args: [BigInt(roomId), userAddress]
    });

    return hasClaimed;
  } catch (error) {
    console.error('检查领取状态失败:', error);
    return false;
  }
}

/**
 * 检查系统费用是否已分配
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链 ID
 * @param {string|number|bigint} params.roomId - 房间 ID
 * @returns {Promise<boolean>} - 返回是否已分配
 * @throws {Error} 如果调用失败
 */
export async function checkSystemFeeDistributed({ chainId, roomId }) {
  try {
    // 获取合约地址和 ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const contractABI = ABIS.GroupBuyRoom;

    // 创建公共客户端
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const isDistributed = await publicClient.readContract({
      address: contractAddress,
      abi: contractABI,
      functionName: 'isSystemFeeDistributed',
      args: [BigInt(roomId)]
    });

    return isDistributed;
  } catch (error) {
    console.error('检查系统费用分配状态失败:', error);
    return false;
  }
}

/**
 * 获取赢家奖励数据
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链ID
 * @param {number} params.roomId - 房间ID
 * @param {string} params.winnerAddress - 赢家地址
 * @returns {Promise<{qptAmount: number, pointsAmount: number}>} 赢家奖励数据
 * @description QPT奖励从QPTLocker合约查询，积分奖励从GroupBuyRoom合约查询
 */
export async function getWinnerRewards({ chainId, roomId, winnerAddress }) {
  try {
    // 验证链ID
    if (chainId !== 97) {
      throw new Error(`不支持的链ID: ${chainId}`);
    }

    // 获取合约地址和 ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');

    const qptLockerAddress = getContractAddress(chainId, 'QPTLocker');
    const groupBuyAddress = getContractAddress(chainId, 'GroupBuyRoom');

    const qptLockerABI = ABIS.QPTLocker;
    const groupBuyABI = ABIS.GroupBuyRoom;

    // 创建公共客户端
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    let qptAmount = 0;
    let pointsAmount = 0;

    // 方法1：直接从QPTLocker合约获取正确的赢家奖励数量
    try {
      // 获取房间档位信息
      const roomData = await publicClient.readContract({
        address: groupBuyAddress,
        abi: groupBuyABI,
        functionName: 'rooms',
        args: [BigInt(roomId)]
      });

      const tier = roomData[1]; // 房间档位（USDT，6位小数）

      // 从QPTLocker合约查询正确的QPT奖励数量
      const amountMapping = await publicClient.readContract({
        address: qptLockerAddress,
        abi: qptLockerABI,
        functionName: 'amountMappings',
        args: [tier]
      });

      // amountMapping 返回 [usdtAmount, lockAmount, rewardAmount]
      qptAmount = Number(amountMapping[2]) / 1000000000000000000; // rewardAmount字段，转换为QPT格式

      // 获取积分奖励（从GroupBuyRoom合约）
      const tierPoints = await publicClient.readContract({
        address: groupBuyAddress,
        abi: groupBuyABI,
        functionName: 'tierPoints',
        args: [tier]
      }).catch(() => 0n);

      // 转换积分精度：tierPoints 存储的是原始值（考虑6位小数），需要除以10^6显示
      pointsAmount = Number(tierPoints) / 1000000;

    } catch (rewardInfoError) {
      // 如果 getRoomRewardInfo 不存在，尝试其他方法
      try {
        // 方法2：从QPTLocker查询QPT奖励
        const roomData = await publicClient.readContract({
          address: groupBuyAddress,
          abi: groupBuyABI,
          functionName: 'rooms',
          args: [BigInt(roomId)]
        });

        const tier = roomData[1]; // 房间档位

        // 查询QPTLocker中的奖励映射
        const amountMapping = await publicClient.readContract({
          address: qptLockerAddress,
          abi: qptLockerABI,
          functionName: 'amountMappings',
          args: [tier]
        }).catch(() => null);

        if (amountMapping) {
          qptAmount = Number(amountMapping[2]) / 1000000000000000000; // rewardAmount字段（索引2）
        }

        // 查询积分奖励
        const tierPoints = await publicClient.readContract({
          address: groupBuyAddress,
          abi: groupBuyABI,
          functionName: 'tierPoints',
          args: [tier]
        }).catch(() => 0n);

        // 转换积分精度：tierPoints 存储的是原始值（考虑6位小数），需要除以10^6显示
        pointsAmount = Number(tierPoints) / 1000000;

      } catch (fallbackError) {
        console.warn('备用方法查询赢家奖励失败:', fallbackError);
      }
    }

    return {
      qptAmount,
      pointsAmount
    };
  } catch (error) {
    console.error('获取赢家奖励数据失败:', error);
    return {
      qptAmount: 0,
      pointsAmount: 0
    };
  }
}
