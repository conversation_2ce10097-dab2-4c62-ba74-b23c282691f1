// 深入调试地址ID问题
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const ADDRESS_MANAGEMENT = "0xA27C195F6e80Dd8742a3beaD3e4871f31C813102";
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";
const USER_ADDRESS = "0xA2dD965E6AAE7Bea5cd24CfC05653B8c72c2F9EF";

// AddressManagement ABI
const ADDRESS_MANAGEMENT_ABI = [
  {
    "inputs": [],
    "name": "getMyAddresses",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "addressId", "type": "uint256"},
          {"internalType": "address", "name": "user", "type": "address"},
          {"internalType": "string", "name": "name", "type": "string"},
          {"internalType": "string", "name": "phone", "type": "string"},
          {"internalType": "string", "name": "province", "type": "string"},
          {"internalType": "string", "name": "city", "type": "string"},
          {"internalType": "string", "name": "district", "type": "string"},
          {"internalType": "string", "name": "detail", "type": "string"},
          {"internalType": "bool", "name": "isDefault", "type": "bool"},
          {"internalType": "uint256", "name": "createTime", "type": "uint256"}
        ],
        "internalType": "struct AddressInfo[]",
        "name": "",
        "type": "tuple[]"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "user", "type": "address"},
      {"internalType": "uint256", "name": "addressId", "type": "uint256"}
    ],
    "name": "getAddress",
    "outputs": [
      {
        "components": [
          {"internalType": "uint256", "name": "addressId", "type": "uint256"},
          {"internalType": "address", "name": "user", "type": "address"},
          {"internalType": "string", "name": "name", "type": "string"},
          {"internalType": "string", "name": "phone", "type": "string"},
          {"internalType": "string", "name": "province", "type": "string"},
          {"internalType": "string", "name": "city", "type": "string"},
          {"internalType": "string", "name": "district", "type": "string"},
          {"internalType": "string", "name": "detail", "type": "string"},
          {"internalType": "bool", "name": "isDefault", "type": "bool"},
          {"internalType": "uint256", "name": "createTime", "type": "uint256"}
        ],
        "internalType": "struct AddressInfo",
        "name": "",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
];

// ProductManagement ABI
const PRODUCT_MANAGEMENT_ABI = [
  {
    "inputs": [
      {"internalType": "uint256", "name": "productId", "type": "uint256"},
      {"internalType": "uint256", "name": "qty", "type": "uint256"},
      {"internalType": "uint256", "name": "addressId", "type": "uint256"}
    ],
    "name": "buyProduct",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

async function debugAddressIssue() {
  console.log("🔍 深入调试地址ID问题...");
  console.log("👤 用户地址:", USER_ADDRESS);
  console.log("📍 AddressManagement:", ADDRESS_MANAGEMENT);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT);

  try {
    // 创建公共客户端
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 获取用户地址列表
    console.log("\n🔍 步骤1：获取用户地址列表...");
    const addresses = await publicClient.readContract({
      address: ADDRESS_MANAGEMENT,
      abi: ADDRESS_MANAGEMENT_ABI,
      functionName: 'getMyAddresses',
      args: [],
      account: USER_ADDRESS
    });

    console.log("地址数量:", addresses.length);
    addresses.forEach((addr, index) => {
      console.log(`地址 ${index}:`, {
        addressId: addr.addressId.toString(),
        user: addr.user,
        name: addr.name,
        isDefault: addr.isDefault
      });
    });

    if (addresses.length === 0) {
      console.log("❌ 用户没有任何地址！这就是问题所在");
      return;
    }

    // 2. 尝试模拟 ProductManagement 调用 buyProduct
    console.log("\n🔍 步骤2：模拟 buyProduct 调用...");
    try {
      const simulation = await publicClient.simulateContract({
        address: PRODUCT_MANAGEMENT,
        abi: PRODUCT_MANAGEMENT_ABI,
        functionName: 'buyProduct',
        args: [1, 1, 0], // productId=1, quantity=1, addressId=0
        account: USER_ADDRESS
      });
      console.log("✅ buyProduct 模拟成功:", simulation);
    } catch (simError) {
      console.log("❌ buyProduct 模拟失败:", simError.message);
      
      // 分析错误原因
      if (simError.message.includes("Invalid address ID")) {
        console.log("\n🔍 分析：Invalid address ID 错误");
        console.log("可能原因：");
        console.log("1. 地址ID超出范围");
        console.log("2. AddressManagement.getAddress() 内部失败");
        console.log("3. 权限问题（虽然授权检查显示正常）");
        
        // 检查地址ID范围
        console.log("\n🔍 检查地址ID范围：");
        console.log("用户地址数量:", addresses.length);
        console.log("尝试使用的地址ID: 0");
        console.log("是否在有效范围内:", 0 < addresses.length ? "✅ 是" : "❌ 否");
      }
    }

    // 3. 检查合约内部状态
    console.log("\n🔍 步骤3：检查可能的问题...");
    
    // 检查第一个地址的详细信息
    const firstAddr = addresses[0];
    console.log("第一个地址详情:", {
      addressId: firstAddr.addressId.toString(),
      addressIdType: typeof firstAddr.addressId,
      user: firstAddr.user,
      name: firstAddr.name
    });

    // 检查地址ID是否与数组索引一致
    console.log("\n🔍 地址ID一致性检查：");
    addresses.forEach((addr, index) => {
      const addressIdAsNumber = Number(addr.addressId);
      console.log(`索引 ${index}: addressId=${addressIdAsNumber}, 一致性=${index === addressIdAsNumber ? '✅' : '❌'}`);
    });

  } catch (error) {
    console.error("❌ 调试过程失败:", error.message);
  }
}

// 运行调试
debugAddressIssue().catch(console.error);
