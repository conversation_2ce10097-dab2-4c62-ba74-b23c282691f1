// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

import "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol";
import "./StorageValidator.sol";

// 使用 SafeERC20 库
using SafeERC20Upgradeable for IERC20Upgradeable;

// 新增:定义 IMerchantManagement 接口
interface IMerchantManagement {
    function isMerchant(address) external view returns (bool);
    function getMerchantStatus(address) external view returns (
        bool isActive, uint256 totalSalesPoints, uint256 exchangedPoints
    );
    function updateMerchantExchangedPoints(address, uint256) external;
    function blacklist(address) external view returns (bool);
}

contract PointsManagement is
    Initializable,
    ContextUpgradeable,
    StorageValidator,
    OwnableUpgradeable,
    PausableUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    // —— 访问控制 —— //
    /// @notice 只有拼团合约能调用
    address public groupBuyRoomAddress;
    modifier onlyGroupBuyRoom() {
        require(msg.sender == groupBuyRoomAddress, "Only GroupBuyRoom");
        _;
    }

    // Timelock 控制
    address public timelock;

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    // 新增:onlyMarketplace 修饰符
    modifier onlyMarketplace() {
        require(msg.sender == marketplaceAddress, "Only marketplace");
        _;
    }

    // 新增:系统配置完整性检查修饰符
    modifier onlyWhenSystemConfigured() {
        require(
            address(usdtToken) != address(0) &&
            timelock != address(0) &&
            marketplaceAddress != address(0) &&
            merchantManager != address(0) &&
            groupBuyRoomAddress != address(0),
            "System not fully configured"
        );
        _;
    }

    // —— 状态变量 —— //
    IERC20Upgradeable public usdtToken;
    address public marketplaceAddress; // 商城合约地址

    // 修改:统一变量命名
    address public merchantManager; // MerchantManagement 合约地址

    // 积分精度：与USDT保持一致（6位小数）
    uint8 public constant POINTS_DECIMALS = 6;

    // 积分兑换参数（考虑6位精度）
    uint256 public constant MIN_EXCHANGE_POINTS = 1000000; // 1.0积分 = 1 * 10^6

    // 固定汇率:1 积分 = 0.3 USDT
    uint256 public constant POINTS_TO_USDT_NUMERATOR = 3;
    uint256 public constant POINTS_TO_USDT_DENOMINATOR = 10;

    // 积分类型
    mapping(address => uint256) public groupBuyPointsNonExchangeable; // 拼团积分（不可兑换）
    mapping(address => uint256) public salesPointsExchangeable;       // 销售积分（可兑换）

    // 积分生成记录
    struct PointsRecord {
        uint256 amount;
        uint256 timestamp;
        string source;
        string pointType;
    }

    // 用户积分记录
    mapping(address => PointsRecord[]) public userPointsRecords;
    uint256 public constant MAX_RECORDS_PER_USER = 100; // 每个用户最多保存100条记录

    // 兑换记录
    struct ExchangeRecord {
        uint256 points;           // 兑换积分数量
        uint256 usdtAmount;       // 兑换USDT数量
        uint256 timestamp;        // 兑换时间
        bool isExchanged;         // 是否已兑换
    }

    // 兑换记录映射
    mapping(address => ExchangeRecord[]) public exchangeRecords;

    // 兑换冷却时间（24小时）
    uint256 public constant EXCHANGE_COOLDOWN = 24 hours;

    // 每个用户最多保存100条兑换记录
    uint256 public constant MAX_EXCHANGE_RECORDS = 100;

    // 记录每个用户最后一次兑换的时间
    mapping(address => uint256) public lastExchangeTime;

    // 积分统计
    struct PointsStatistics {
        uint256 totalGroupBuyPoints;    // 拼团积分总量
        uint256 totalSalesPoints;       // 销售积分总量
        uint256 totalBurnedPoints;      // 销毁积分总量
        uint256 totalUSDTExchanged;     // 已兑换USDT总量
        uint256 totalUSDTBalance;       // 平台USDT余额
    }

    // 积分统计
    PointsStatistics public pointsStats;

    // 事件
    event PointsGenerated(
        address indexed user,
        uint256 amount,
        string source,
        string pointType
    );
    event PointsExchanged(address indexed user, uint256 points, uint256 usdtAmount);
    event PointsBurned(address indexed user, uint256 amount, string reason);
    event GroupBuyRoomUpdated(address indexed oldAddress, address indexed newAddress);
    event MarketplaceUpdated(address indexed oldAddress, address indexed newAddress);
    event USDTTokenUpdated(address indexed oldToken, address indexed newToken);
    event ExchangeFailed(address indexed user, string reason); // 新增:兑换失败事件
    event RecordOverflow(address indexed user, string recordType); // 新增:记录溢出事件
    event MerchantManagerUpdated(address indexed merchantManager); // 新增:MerchantManager 更新事件

    // 新增：积分转账事件
    event PointsTransferred(
        address indexed from,
        address indexed to,
        uint256 amount,
        string pointType
    );

    // BNB相关事件
    event BNBReceived(address indexed sender, uint256 amount);
    event BNBWithdrawn(address indexed to, uint256 amount);
    event EmergencyUSDTWithdrawn(address indexed to, uint256 amount);

    // 修改:完整初始化函数，一次性设置所有依赖
    function initialize(
        address _usdtToken,
        address _timelock,
        address _marketplaceAddress,
        address _merchantManager,
        address _groupBuyRoomAddress
    ) public initializer {
        __Context_init();
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        require(AddressUpgradeable.isContract(_usdtToken), "USDT token must be a contract");
        require(_timelock != address(0), "Invalid timelock");
        require(_marketplaceAddress != address(0), "Invalid marketplace");
        require(_merchantManager != address(0), "Invalid merchant manager");
        require(_groupBuyRoomAddress != address(0), "Invalid group buy room");

        usdtToken = IERC20Upgradeable(_usdtToken);
        timelock = _timelock;
        marketplaceAddress = _marketplaceAddress;
        merchantManager = _merchantManager;
        groupBuyRoomAddress = _groupBuyRoomAddress;

        emit MarketplaceUpdated(address(0), _marketplaceAddress);
        emit MerchantManagerUpdated(_merchantManager);
        emit GroupBuyRoomUpdated(address(0), _groupBuyRoomAddress);
    }

    /// @notice 设置Timelock地址（仅所有者）
    /// @param _timelock 新的Timelock地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    /// @notice 检查合约配置是否完整
    /// @return 配置状态，true表示完整配置，false表示配置不完整
    function isSystemConfigured() external view returns (bool) {
        return (
            address(usdtToken) != address(0) &&
            timelock != address(0) &&
            marketplaceAddress != address(0) &&
            merchantManager != address(0) &&
            groupBuyRoomAddress != address(0)
        );
    }

    /// @notice 获取所有配置地址（用于验证）
    /// @return _usdtToken USDT代币合约地址
    /// @return _timelock Timelock合约地址
    /// @return _marketplaceAddress 商城合约地址
    /// @return _merchantManager 商家管理合约地址
    /// @return _groupBuyRoomAddress 拼团房间合约地址
    function getSystemConfiguration() external view returns (
        address _usdtToken,
        address _timelock,
        address _marketplaceAddress,
        address _merchantManager,
        address _groupBuyRoomAddress
    ) {
        return (
            address(usdtToken),
            timelock,
            marketplaceAddress,
            merchantManager,
            groupBuyRoomAddress
        );
    }

    /// @notice 部署后由 Owner（多签/时锁）调用
    function setGroupBuyRoomAddress(address _gbr) external onlyOwner whenNotPaused {
        require(_gbr != address(0), "Zero address");
        require(AddressUpgradeable.isContract(_gbr), "GroupBuyRoom must be contract");
        address old = groupBuyRoomAddress;
        groupBuyRoomAddress = _gbr;
        emit GroupBuyRoomUpdated(old, _gbr);
    }

    function setMarketplaceAddress(address _marketplaceAddress) external onlyOwner whenNotPaused {
        require(_marketplaceAddress != address(0), "Zero address");
        require(AddressUpgradeable.isContract(_marketplaceAddress), "Marketplace must be contract");
        address oldAddress = marketplaceAddress;
        marketplaceAddress = _marketplaceAddress;
        emit MarketplaceUpdated(oldAddress, _marketplaceAddress);
    }

    // —— 核心:只允许 GroupBuyRoom 调用，并可暂停 ——
    function generatePoints(
        address user,
        uint256 amount,
        string memory source,
        string memory pointType
    ) external whenNotPaused onlyGroupBuyRoom {
        require(amount > 0, "Amount must be greater than 0");
        require(_isValidAddress(user), "Invalid user address");
        require(
    keccak256(bytes(pointType)) == keccak256(bytes("GROUPBUY")),
    "Only GROUPBUY points"
    );

        groupBuyPointsNonExchangeable[user] += amount;

        // 添加积分记录
        _addPointsRecord(user, PointsRecord({
            amount: amount,
            timestamp: block.timestamp,
            source: source,
            pointType: pointType
        }));

        // 更新积分统计
        pointsStats.totalGroupBuyPoints += amount;

        emit PointsGenerated(user, amount, source, pointType);
    }

    // 修改:generateSalesPoints 函数，添加 onlyMarketplace 修饰符
    function generateSalesPoints(
        address user,
        uint256 amount,
        string memory source
    ) external whenNotPaused onlyMarketplace {
        require(amount > 0, "Amount must be greater than 0");
        require(_isValidAddress(user), "Invalid user address");

        salesPointsExchangeable[user] += amount;

        // 添加积分记录
        _addPointsRecord(user, PointsRecord({
            amount: amount,
            timestamp: block.timestamp,
            source: source,
            pointType: "SALES"
        }));

        // 更新积分统计
        pointsStats.totalSalesPoints += amount;

        emit PointsGenerated(user, amount, source, "SALES");
    }

    /// @notice 转账拼团积分给其他用户
    /// @param to 接收方地址
    /// @param amount 转账数量
    function transferGroupBuyPoints(
        address to,
        uint256 amount
    ) external nonReentrant whenNotPaused {
        require(to != address(0), "Invalid recipient");
        require(to != msg.sender, "Cannot transfer to self");
        require(_isValidAddress(to), "Invalid recipient address");
        require(amount > 0, "Amount must be greater than 0");
        require(groupBuyPointsNonExchangeable[msg.sender] >= amount, "Insufficient group buy points");

        // 执行转账
        groupBuyPointsNonExchangeable[msg.sender] -= amount;
        groupBuyPointsNonExchangeable[to] += amount;

        // 记录转账记录（发送方）
        _addPointsRecord(msg.sender, PointsRecord({
            amount: amount,
            timestamp: block.timestamp,
            source: string(abi.encodePacked("Transfer to ", _addressToString(to))),
            pointType: "GROUPBUY_TRANSFER_OUT"
        }));

        // 记录转账记录（接收方）
        _addPointsRecord(to, PointsRecord({
            amount: amount,
            timestamp: block.timestamp,
            source: string(abi.encodePacked("Transfer from ", _addressToString(msg.sender))),
            pointType: "GROUPBUY_TRANSFER_IN"
        }));

        emit PointsTransferred(msg.sender, to, amount, "GROUPBUY");
    }

    /// @notice 商城购物扣除拼团积分（由商城合约调用）
    /// @param user 用户地址
    /// @param merchant 商家地址
    /// @param points 扣除积分数量
    function exchangeGoods(
        address user,
        address merchant,
        uint256 points
    ) external whenNotPaused onlyMarketplace onlyWhenSystemConfigured {
        require(user != address(0), "Invalid user");
        require(merchant != address(0), "Invalid merchant");
        require(points > 0, "Points must be greater than 0");
        require(groupBuyPointsNonExchangeable[user] >= points, "Insufficient group buy points");

        // 扣除用户拼团积分
        groupBuyPointsNonExchangeable[user] -= points;

        // 给商家增加销售积分
        salesPointsExchangeable[merchant] += points;

        // 记录用户消费记录
        _addPointsRecord(user, PointsRecord({
            amount: points,
            timestamp: block.timestamp,
            source: string(abi.encodePacked("Shopping at ", _addressToString(merchant))),
            pointType: "GROUPBUY_SHOPPING"
        }));

        // 记录商家销售积分
        _addPointsRecord(merchant, PointsRecord({
            amount: points,
            timestamp: block.timestamp,
            source: string(abi.encodePacked("Sale from ", _addressToString(user))),
            pointType: "SALES"
        }));

        // 更新统计
        pointsStats.totalSalesPoints += points;

        emit PointsGenerated(merchant, points, "Shopping", "SALES");
        emit PointsBurned(user, points, "SHOPPING");
    }

    /// @notice 只有商家销售积分（salesPoints）可兑换 USDT，拼团积分（groupBuyPoints）不可兑换
    function exchangeSalesPointsForUSDT(
        uint256 amount,
        address merchant
    ) external nonReentrant whenNotPaused {
        // 校验 merchantManager 已设置
        require(merchantManager != address(0), "Merchant manager not set");

        // 修改冷却时间校验逻辑:如果从未兑换过，则跳过冷却时间
        uint256 last = lastExchangeTime[msg.sender];
        require(last == 0 || block.timestamp >= last + EXCHANGE_COOLDOWN, "Exchange cooldown not passed");

        require(IMerchantManagement(merchantManager).isMerchant(merchant), "Not a merchant");
        require(!IMerchantManagement(merchantManager).blacklist(merchant), "Merchant is blacklisted");
        (bool isActive, uint256 totalSalesPoints, uint256 exchangedPoints) = IMerchantManagement(merchantManager).getMerchantStatus(merchant);
        require(isActive, "Merchant not active");
        require(amount >= MIN_EXCHANGE_POINTS, "Minimum exchange points is 1");
        require(salesPointsExchangeable[msg.sender] >= amount, "Insufficient sales points");

        // 检查积分来源
        if (totalSalesPoints < exchangedPoints + amount) {
            // 失败时不更新冷却时间
            emit ExchangeFailed(msg.sender, "Invalid points source");
            return;
        }

        // 使用固定汇率计算USDT数量
        uint256 usdtAmount = amount * POINTS_TO_USDT_NUMERATOR / POINTS_TO_USDT_DENOMINATOR; // = points * 3 / 10
        if (usdtToken.balanceOf(address(this)) < usdtAmount) {
            // 失败时不更新冷却时间
            emit ExchangeFailed(msg.sender, "Insufficient platform USDT balance");
            return;
        }

        // 所有校验通过后，更新冷却时间
        lastExchangeTime[msg.sender] = block.timestamp;

        // 扣除销售积分并销毁
        salesPointsExchangeable[msg.sender] -= amount;

        // 更新统计
        pointsStats.totalBurnedPoints += amount;
        pointsStats.totalUSDTExchanged += usdtAmount;

        // 记录兑换记录
        _addExchangeRecord(msg.sender, ExchangeRecord({
            points: amount,
            usdtAmount: usdtAmount,
            timestamp: block.timestamp,
            isExchanged: true
        }));

        // 先执行转账
        usdtToken.safeTransfer(msg.sender, usdtAmount);

        // 更新平台USDT余额统计，确保统计与实际余额一致
        pointsStats.totalUSDTBalance = usdtToken.balanceOf(address(this));

        // 同步更新商家的已兑换积分，捕获异常避免重入
        try IMerchantManagement(merchantManager).updateMerchantExchangedPoints(merchant, amount) {} catch {}

        emit PointsBurned(msg.sender, amount, "EXCHANGE");
        emit PointsExchanged(msg.sender, amount, usdtAmount);
    }

    function setUSDTToken(address _usdtToken) external onlyOwner whenNotPaused {
        require(_usdtToken != address(0), "Zero address");
        require(AddressUpgradeable.isContract(_usdtToken), "USDT token must be a contract");
        address oldToken = address(usdtToken);
        usdtToken = IERC20Upgradeable(_usdtToken);
        emit USDTTokenUpdated(oldToken, _usdtToken);
    }

    // 修改:统一变量命名
    function setMerchantManager(address _merchantManager) external onlyOwner whenNotPaused {
        require(_merchantManager != address(0), "Zero address");
        require(AddressUpgradeable.isContract(_merchantManager), "Must be contract");
        merchantManager = _merchantManager;
        emit MerchantManagerUpdated(_merchantManager);
    }

    /// @notice 紧急提取 USDT（仅所有者，用于合约迁移或紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量
    function withdrawUSDT(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "Zero address");
        require(amount <= usdtToken.balanceOf(address(this)), "Insufficient USDT balance");

        usdtToken.safeTransfer(to, amount);
        emit EmergencyUSDTWithdrawn(to, amount);
    }

    /// @notice 接收BNB转账，用于支付Gas费
    /// @dev 合约需要BNB来执行主动转账操作
    receive() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 备用接收函数
    fallback() external payable {
        emit BNBReceived(msg.sender, msg.value);
    }

    /// @notice 查询合约BNB余额
    /// @return 合约当前BNB余额（wei）
    function getBNBBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice 管理员提取BNB（紧急情况）
    /// @param to 接收地址
    /// @param amount 提取数量（wei）
    function withdrawBNB(address payable to, uint256 amount) external onlyOwner {
        require(to != address(0), "Invalid address");
        require(amount <= address(this).balance, "Insufficient balance");

        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");

        emit BNBWithdrawn(to, amount);
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证积分系统基本状态
        if (address(usdtToken) == address(0)) return false;
        if (groupBuyRoomAddress == address(0)) return false;
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            address(usdtToken),
            groupBuyRoomAddress,
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // PointsManagement的存储修复逻辑
        emit StorageFixed(address(this), "PointsManagement storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    /// @notice 获取积分精度
    /// @return 积分的小数位数（与USDT保持一致）
    function getPointsDecimals() external pure returns (uint8) {
        return POINTS_DECIMALS;
    }

    // 重写 _authorizeUpgrade 函数
    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    // 新增:验证地址是否有效
    function _isValidAddress(address account) internal view returns (bool) {
        return account != address(0) && !AddressUpgradeable.isContract(account);
    }

    // 新增:地址转字符串辅助函数
    function _addressToString(address addr) internal pure returns (string memory) {
        bytes32 value = bytes32(uint256(uint160(addr)));
        bytes memory alphabet = "0123456789abcdef";
        bytes memory str = new bytes(42);
        str[0] = '0';
        str[1] = 'x';
        for (uint256 i = 0; i < 20; i++) {
            str[2 + i * 2] = alphabet[uint8(value[i + 12] >> 4)];
            str[3 + i * 2] = alphabet[uint8(value[i + 12] & 0x0f)];
        }
        return string(str);
    }

    function _addPointsRecord(address user, PointsRecord memory rec) internal {
        PointsRecord[] storage arr = userPointsRecords[user];
        if (arr.length >= MAX_RECORDS_PER_USER) {
            // 弹出最旧
            for (uint i=0; i< arr.length-1; i++){
                arr[i] = arr[i+1];
            }
            arr.pop();
            emit RecordOverflow(user, "PointsRecord");
        }
        arr.push(rec);
    }

    function _addExchangeRecord(address user, ExchangeRecord memory rec) internal {
        ExchangeRecord[] storage arr = exchangeRecords[user];
        if (arr.length >= MAX_EXCHANGE_RECORDS) {
            // 去掉最旧
            for (uint i = 0; i < arr.length - 1; i++) {
                arr[i] = arr[i + 1];
            }
            arr.pop();
            emit RecordOverflow(user, "ExchangeRecord");
        }
        arr.push(rec);
    }

    /// @notice 计算用户总获得和总消费积分
    /// @param user 用户地址
    /// @return totalEarned 总获得积分
    /// @return totalSpent 总消费积分
    function _calculateUserTotals(address user) internal view returns (uint256 totalEarned, uint256 totalSpent) {
        PointsRecord[] storage records = userPointsRecords[user];

        for (uint256 i = 0; i < records.length; i++) {
            PointsRecord storage record = records[i];

            // 计算获得的积分
            if (
                keccak256(bytes(record.pointType)) == keccak256(bytes("GROUPBUY")) ||
                keccak256(bytes(record.pointType)) == keccak256(bytes("SALES")) ||
                keccak256(bytes(record.pointType)) == keccak256(bytes("GROUPBUY_TRANSFER_IN"))
            ) {
                totalEarned += record.amount;
            }

            // 计算消费的积分
            if (
                keccak256(bytes(record.pointType)) == keccak256(bytes("GROUPBUY_SHOPPING")) ||
                keccak256(bytes(record.pointType)) == keccak256(bytes("GROUPBUY_TRANSFER_OUT"))
            ) {
                totalSpent += record.amount;
            }
        }

        // 加上兑换记录中的消费
        ExchangeRecord[] storage exchanges = exchangeRecords[user];
        for (uint256 i = 0; i < exchanges.length; i++) {
            if (exchanges[i].isExchanged) {
                totalSpent += exchanges[i].points;
            }
        }
    }

    // 复杂查询函数已移至 UniversalQueryContract
}