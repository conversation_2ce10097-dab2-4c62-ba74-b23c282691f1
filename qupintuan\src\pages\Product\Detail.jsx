import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { getProductInfo } from '@/apis/mallApi';
import { checkUserRegistered } from '@/apis/agentSystemApi';
import { fixPriceDisplay, needsPriceFix } from '@/utils/priceDisplayFix';
import ProductPurchase from '@/components/Mall/ProductPurchase';
import './ProductDetail.css';

export default function ProductDetail() {
  const { productId } = useParams();
  const navigate = useNavigate();
  const { address: account } = useAccount();

  // 状态管理
  const [product, setProduct] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRegisteredUser, setIsRegisteredUser] = useState(false);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  // 加载商品信息
  const loadProduct = async () => {
    setIsLoading(true);
    try {
  

      const productInfo = await getProductInfo({ productId: Number(productId) });
      setProduct(productInfo);


    } catch (error) {
      console.error('❌ [ProductDetail] 加载商品信息失败:', error);
      toast.error(`加载商品信息失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 检查用户注册状态
  const checkUserStatus = async () => {
    if (!account) {
      setIsRegisteredUser(false);
      return;
    }

    try {
      const registered = await checkUserRegistered({ userAddress: account });
      setIsRegisteredUser(registered);
    } catch (error) {
      console.error('❌ [ProductDetail] 检查用户状态失败:', error);
      setIsRegisteredUser(false);
    }
  };

  // 处理购买按钮点击
  const handleBuyClick = () => {
    if (!account) {
      toast.error('请先连接钱包');
      return;
    }

    if (!isRegisteredUser) {
      toast.error('请先注册代理系统才能购买商品');
      return;
    }

    if (!product.isActive) {
      toast.error('商品已下架');
      return;
    }

    if (product.stock <= 0) {
      toast.error('商品库存不足');
      return;
    }

    setShowPurchaseModal(true);
  };

  // 处理购买成功
  const handlePurchaseSuccess = (result) => {
    // 重新加载商品信息以更新库存
    loadProduct();
    // 关闭购买模态框
    setShowPurchaseModal(false);
  };

  // 格式化地址显示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // 组件挂载时加载数据
  useEffect(() => {
    if (productId) {
      loadProduct();
    }
  }, [productId]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    checkUserStatus();
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  if (isLoading) {
    return (
      <div className="product-detail">
        <div className="loading-container">
          <div className="loading-spinner">🔄</div>
          <p>正在加载商品信息...</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="product-detail">
        <div className="error-container">
          <div className="error-icon">❌</div>
          <h3>商品不存在</h3>
          <p>该商品可能已被删除或不存在</p>
          <button className="back-btn" onClick={() => navigate('/product')}>
            ← 返回商城
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="product-detail">
      <div className="detail-header">
        <button className="back-btn" onClick={() => navigate('/product')}>
          ← 返回商城
        </button>
        <div className="product-status">
          {product.isActive ? (
            <span className="status-active">✅ 在售</span>
          ) : (
            <span className="status-inactive">⏸️ 已下架</span>
          )}
        </div>
      </div>

      <div className="detail-content">
        {/* 商品图片 */}
        <div className="product-images">
          {product.images && product.images.length > 0 ? (
            <div className="image-gallery">
              <img
                src={product.images[0].startsWith('http')
                  ? product.images[0]
                  : `https://gateway.pinata.cloud/ipfs/${product.images[0]}`}
                alt={product.name}
                className="main-image"
                onError={(e) => {
                  // 尝试备用网关
                  if (e.target.src.includes('pinata')) {
                    e.target.src = `https://cloudflare-ipfs.com/ipfs/${product.images[0]}`;
                  } else if (e.target.src.includes('cloudflare')) {
                    e.target.src = `https://dweb.link/ipfs/${product.images[0]}`;
                  } else {
                    e.target.style.display = 'none';
                  }
                }}
              />
            </div>
          ) : (
            <div className="placeholder-image">
              <div className="placeholder-icon">📦</div>
              <p>暂无商品图片</p>
            </div>
          )}
        </div>

        {/* 商品信息 */}
        <div className="product-info">
          <h1 className="product-name">{product.name}</h1>

          <div className="product-price">
            <span className="price-value">
              {needsPriceFix(product.price) ? fixPriceDisplay(product.price) : product.price}
            </span>
            <span className="price-unit">积分</span>
          </div>

          <div className="product-meta">
            <div className="meta-item">
              <span className="meta-label">库存:</span>
              <span className="meta-value">{product.stock} 件</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">销量:</span>
              <span className="meta-value">{product.sales} 件</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">商家:</span>
              <span className="meta-value">{formatAddress(product.merchant)}</span>
            </div>
          </div>

          <div className="product-description">
            <h3>商品描述</h3>
            <p>{product.description}</p>
          </div>

          {/* 购买按钮 */}
          <div className="purchase-section">
            {!account ? (
              <div className="purchase-notice">
                <p>请连接钱包后购买商品</p>
              </div>
            ) : !isRegisteredUser ? (
              <div className="purchase-notice">
                <p>请先注册代理系统才能购买商品</p>
                <button
                  className="register-btn"
                  onClick={() => navigate('/profile')}
                >
                  前往注册
                </button>
              </div>
            ) : (
              <div className="purchase-buttons">
                <button
                  className={`buy-btn ${!product.isActive || product.stock <= 0 ? 'disabled' : ''}`}
                  onClick={handleBuyClick}
                  disabled={!product.isActive || product.stock <= 0}
                >
                  {!product.isActive ? '商品已下架' :
                   product.stock <= 0 ? '库存不足' :
                   `💰 立即购买 (${needsPriceFix(product.price) ? fixPriceDisplay(product.price) : product.price} 积分)`}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 购买弹窗 */}
      {showPurchaseModal && (
        <ProductPurchase
          product={product}
          onPurchaseSuccess={handlePurchaseSuccess}
          onClose={() => setShowPurchaseModal(false)}
        />
      )}




    </div>
  );
}
