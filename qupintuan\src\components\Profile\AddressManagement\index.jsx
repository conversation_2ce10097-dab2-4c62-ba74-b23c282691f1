// src/components/Profile/AddressManagement/index.jsx
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import './index.css'

export default function AddressManagement() {
  const { address: account, isConnected } = useAccount()

  // 状态管理
  const [addresses, setAddresses] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 表单数据
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    isDefault: false
  })

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  })

  // 加载用户地址列表（带详细日志）
  const loadAddresses = async () => {
    console.log('🔍 [AddressManagement] 开始加载用户地址列表')
    console.log('📋 [AddressManagement] 用户账户:', account)
    console.log('📋 [AddressManagement] 连接状态:', isConnected)

    if (!account || !isConnected) {
      console.log('⚠️ [AddressManagement] 用户未连接，跳过加载')
      return
    }

    setIsLoading(true)
    try {
      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement
      console.log('📋 [AddressManagement] 合约地址:', addressAddress)

      // 尝试使用新的 getMyAddresses 函数
      console.log('🔍 [AddressManagement] 尝试使用 getMyAddresses 函数...')
      try {
        const userAddresses = await publicClient.readContract({
          address: addressAddress,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        })
        console.log('✅ [AddressManagement] getMyAddresses 调用成功')
        console.log('📋 [AddressManagement] 获取到的地址数量:', userAddresses ? userAddresses.length : 0)
        console.log('📋 [AddressManagement] 地址详情:', userAddresses)
        setAddresses(userAddresses || [])
      } catch (newFunctionError) {
        // 如果新函数不存在，回退到旧函数
        console.log('❌ [AddressManagement] getMyAddresses 调用失败:', newFunctionError.message)
        console.log('🔄 [AddressManagement] 尝试使用旧版本函数 getUserAddresses...')
        try {
          const userAddresses = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getUserAddresses',
            args: [account]
          })
          console.log('✅ [AddressManagement] getUserAddresses 调用成功')
          console.log('📋 [AddressManagement] 获取到的地址数量:', userAddresses ? userAddresses.length : 0)
          console.log('📋 [AddressManagement] 地址详情:', userAddresses)
          setAddresses(userAddresses || [])
        } catch (authError) {
          // 如果权限错误，说明合约需要更新
          console.error('❌ [AddressManagement] getUserAddresses 也失败:', authError.message)
          console.warn('⚠️ [AddressManagement] 合约权限限制，需要升级AddressManagement合约')
          toast.error('地址管理功能需要合约升级，请联系管理员')
          setAddresses([])
        }
      }
    } catch (error) {
      console.error('❌ [AddressManagement] 加载地址列表失败:', error)
      console.log('📋 [AddressManagement] 加载错误详情:', {
        message: error.message,
        code: error.code,
        data: error.data
      })
      toast.error('加载地址列表失败: ' + (error.message || '未知错误'))
      setAddresses([])
    } finally {
      console.log('🏁 [AddressManagement] 加载地址列表流程结束')
      setIsLoading(false)
    }
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    })
    setEditingAddress(null)
    setShowAddForm(false)
  }

  // 添加地址（带详细日志监控）
  const handleAddAddress = async () => {
    console.log('🚀 [AddressManagement] 开始添加地址流程')
    console.log('📋 [AddressManagement] 表单数据:', formData)
    console.log('📋 [AddressManagement] 用户账户:', account)

    if (!validateForm()) {
      console.log('❌ [AddressManagement] 表单验证失败')
      return
    }

    setIsSubmitting(true)
    try {
      // 记录添加前的地址数量
      console.log('🔍 [AddressManagement] 检查添加前的地址状态...')
      try {
        const beforeAddresses = await publicClient.readContract({
          address: CONTRACT_ADDRESSES[97].AddressManagement,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        })
        console.log('📋 [AddressManagement] 添加前地址数量:', beforeAddresses.length)
        console.log('📋 [AddressManagement] 添加前地址列表:', beforeAddresses)
      } catch (beforeError) {
        console.log('⚠️ [AddressManagement] 无法获取添加前地址状态:', beforeError.message)
      }

      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement
      console.log('📋 [AddressManagement] 合约地址:', addressAddress)

      console.log('📋 [AddressManagement] 准备提交交易...')
      console.log('📋 [AddressManagement] 交易参数:', {
        address: addressAddress,
        functionName: 'addAddress',
        args: [
          formData.name,
          formData.phone,
          formData.province,
          formData.city,
          formData.district,
          formData.detail,
          formData.isDefault
        ],
        account
      })

      // 如果是第一个地址，强制设为默认
      const isFirstAddress = addresses.length === 0;
      const shouldBeDefault = isFirstAddress || formData.isDefault;

      console.log('📋 [AddressManagement] 地址设置信息:', {
        isFirstAddress,
        originalIsDefault: formData.isDefault,
        finalIsDefault: shouldBeDefault,
        addressCount: addresses.length
      });

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'addAddress',
        args: [
          formData.name,
          formData.phone,
          formData.province,
          formData.city,
          formData.district,
          formData.detail,
          shouldBeDefault
        ],
        account
      })

      console.log('✅ [AddressManagement] 交易已提交:', hash)
      toast.success(`添加地址交易已提交: ${hash}`)

      // 等待交易确认
      console.log('⏳ [AddressManagement] 等待交易确认...')
      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        timeout: 120000 // 2分钟超时
      })

      console.log('📋 [AddressManagement] 交易收据:', receipt)
      console.log('📋 [AddressManagement] 交易状态:', receipt.status)

      if (receipt.status === 'success') {
        console.log('✅ [AddressManagement] 交易执行成功')

        // 检查是否有 AddressAdded 事件
        const addressAddedTopic = '0x9cc987676e7d63379f176ea50df0ae8d2d9d1141d1231d4ce15b5965f73c9430'
        const addressAddedLogs = receipt.logs.filter(log =>
          log.topics[0] === addressAddedTopic
        )

        console.log('📋 [AddressManagement] 所有日志:', receipt.logs)
        console.log('📋 [AddressManagement] AddressAdded 事件数量:', addressAddedLogs.length)

        if (addressAddedLogs.length > 0) {
          console.log('✅ [AddressManagement] 找到 AddressAdded 事件:', addressAddedLogs)

          // 解码事件数据
          addressAddedLogs.forEach((log, index) => {
            try {
              const addressId = parseInt(log.data, 16)
              console.log(`📋 [AddressManagement] AddressAdded 事件 ${index + 1}:`)
              console.log(`   - 用户: ${log.topics[1]}`)
              console.log(`   - 地址ID: ${addressId}`)
            } catch (decodeError) {
              console.log('❌ [AddressManagement] 解码事件失败:', decodeError)
            }
          })
        } else {
          console.log('⚠️ [AddressManagement] 交易成功但没有 AddressAdded 事件')
        }

        // 验证合约状态变化
        console.log('🔍 [AddressManagement] 验证合约状态变化...')
        await new Promise(resolve => setTimeout(resolve, 3000)) // 等待3秒确保状态同步

        try {
          const afterAddresses = await publicClient.readContract({
            address: addressAddress,
            abi: ABIS.AddressManagement,
            functionName: 'getMyAddresses',
            args: [],
            account
          })
          console.log('📋 [AddressManagement] 添加后地址数量:', afterAddresses.length)
          console.log('📋 [AddressManagement] 添加后地址列表:', afterAddresses)

          if (afterAddresses.length > 0) {
            console.log('✅ [AddressManagement] 地址确实已添加到合约中')
            toast.success('地址添加成功！')
          } else {
            console.log('❌ [AddressManagement] 交易成功但合约中仍然没有地址')
            toast.error('地址添加异常：请刷新页面重试')
          }
        } catch (afterError) {
          console.log('❌ [AddressManagement] 验证添加后状态失败:', afterError.message)
          toast.warning('地址可能已添加，请刷新页面检查')
        }

        resetForm()
        await loadAddresses()
      } else {
        console.log('❌ [AddressManagement] 交易执行失败')
        toast.error('交易执行失败')
      }

    } catch (error) {
      console.error('❌ [AddressManagement] 添加地址失败:', error)
      console.log('📋 [AddressManagement] 错误详情:', {
        message: error.message,
        code: error.code,
        data: error.data,
        stack: error.stack
      })

      // 详细的错误分析
      if (error.message.includes('User rejected')) {
        console.log('💡 [AddressManagement] 用户取消了交易')
        toast.error('您取消了交易')
      } else if (error.message.includes('insufficient funds')) {
        console.log('💡 [AddressManagement] 余额不足')
        toast.error('余额不足，无法支付Gas费用')
      } else if (error.message.includes('execution reverted')) {
        console.log('💡 [AddressManagement] 合约执行失败')
        if (error.message.includes('No contracts')) {
          toast.error('合约不允许此操作，请联系管理员')
        } else if (error.message.includes('Pausable: paused')) {
          toast.error('合约已暂停，请稍后重试')
        } else {
          toast.error('合约执行失败，请检查参数')
        }
      } else {
        console.log('💡 [AddressManagement] 其他错误:', error.message)
        toast.error(`添加地址失败: ${error.message}`)
      }
    } finally {
      console.log('🏁 [AddressManagement] 添加地址流程结束')
      setIsSubmitting(false)
    }
  }

  // 更新地址
  const handleUpdateAddress = async () => {
    if (!validateForm() || editingAddress === null) return

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'updateAddress',
        args: [
          editingAddress,
          formData.name,
          formData.phone,
          formData.province,
          formData.city,
          formData.district,
          formData.detail,
          formData.isDefault
        ],
        account
      })

      toast.success('更新地址交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash })

      toast.success('地址更新成功！')
      resetForm()
      await loadAddresses()

    } catch (error) {
      console.error('更新地址失败:', error)
      toast.error('更新地址失败: ' + (error.message || '未知错误'))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 删除地址
  const handleDeleteAddress = async (addressId) => {
    if (!confirm('确定要删除这个地址吗？')) return

    setIsSubmitting(true)
    try {
      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      const hash = await walletClient.writeContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'deleteAddress',
        args: [addressId],
        account
      })

      toast.success('删除地址交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash })

      toast.success('地址删除成功！')
      await loadAddresses()

    } catch (error) {
      console.error('删除地址失败:', error)
      toast.error('删除地址失败: ' + (error.message || '未知错误'))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 编辑地址
  const handleEditAddress = (address) => {
    setFormData({
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      detail: address.detail,
      isDefault: address.isDefault
    })
    setEditingAddress(Number(address.addressId))
    setShowAddForm(true)
  }

  // 表单验证
  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error('请输入收货人姓名')
      return false
    }
    if (!formData.phone.trim()) {
      toast.error('请输入联系电话')
      return false
    }
    if (!formData.province.trim()) {
      toast.error('请输入省份')
      return false
    }
    if (!formData.city.trim()) {
      toast.error('请输入城市')
      return false
    }
    if (!formData.district.trim()) {
      toast.error('请输入区县')
      return false
    }
    if (!formData.detail.trim()) {
      toast.error('请输入详细地址')
      return false
    }
    return true
  }

  // 格式化地址显示
  const formatAddress = (address) => {
    return `${address.province} ${address.city} ${address.district} ${address.detail}`
  }

  // 调试面板状态
  const [showDebugPanel, setShowDebugPanel] = useState(false)
  const [debugInfo, setDebugInfo] = useState(null)
  const [isDebugging, setIsDebugging] = useState(false)

  // 运行调试检查
  const runDebugCheck = async () => {
    console.log('🔍 [AddressManagement] 开始调试检查...')
    setIsDebugging(true)
    try {
      const addressAddress = CONTRACT_ADDRESSES[97].AddressManagement

      // 检查合约状态
      const isPaused = await publicClient.readContract({
        address: addressAddress,
        abi: ABIS.AddressManagement,
        functionName: 'paused',
        args: []
      })

      // 检查用户地址
      let userAddresses = []
      let getAddressesError = null
      try {
        userAddresses = await publicClient.readContract({
          address: addressAddress,
          abi: ABIS.AddressManagement,
          functionName: 'getMyAddresses',
          args: [],
          account
        })
      } catch (error) {
        getAddressesError = error.message
      }

      const debugResult = {
        timestamp: new Date().toISOString(),
        userAccount: account,
        contractAddress: addressAddress,
        contractPaused: isPaused,
        addressCount: userAddresses.length,
        addresses: userAddresses,
        getAddressesError: getAddressesError,
        isConnected: isConnected
      }

      console.log('📋 [AddressManagement] 调试结果:', debugResult)
      setDebugInfo(debugResult)
    } catch (error) {
      console.error('❌ [AddressManagement] 调试检查失败:', error)
      setDebugInfo({
        timestamp: new Date().toISOString(),
        error: error.message
      })
    } finally {
      setIsDebugging(false)
    }
  }

  useEffect(() => {
    loadAddresses()
  }, [account, isConnected])

  if (!isConnected) {
    return (
      <div className="address-management">
        <div className="section-header">
          <div className="header-content">
            <h3>📍 收货地址</h3>
            <p>管理您的收货地址信息</p>
          </div>
        </div>
        <div className="address-content">
          <div className="connect-prompt">
            <p>请连接钱包以管理收货地址</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="address-management">
      <div className="section-header">
        <div className="header-content">
          <h3>📍 收货地址</h3>
          <p>管理您的收货地址信息</p>
        </div>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={loadAddresses}
            disabled={isLoading}
          >
            {isLoading ? '🔄' : '🔄 刷新'}
          </button>
          <button
            className="debug-btn"
            onClick={() => setShowDebugPanel(!showDebugPanel)}
            style={{
              backgroundColor: showDebugPanel ? '#ff6b6b' : '#4ecdc4',
              color: 'white',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '4px',
              cursor: 'pointer',
              marginRight: '8px'
            }}
          >
            🔍 调试
          </button>
          <button
            className="add-btn"
            onClick={() => setShowAddForm(true)}
            disabled={addresses.length >= 20}
          >
            ➕ 添加地址
          </button>
        </div>
      </div>

      {/* 地址使用规则说明 */}
      <div className="address-rules">
        <div className="rules-header">
          <h4>📋 地址使用规则</h4>
        </div>
        <div className="rules-content">
          <div className="rule-item">
            <span className="rule-icon">🎯</span>
            <div className="rule-text">
              <strong>默认地址机制：</strong>
              购买商品时系统将自动使用您的默认收货地址，确保交易安全可靠。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">🔄</span>
            <div className="rule-text">
              <strong>地址切换：</strong>
              如需使用其他收货地址，请先将其设置为默认地址，然后再进行购买。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">⚡</span>
            <div className="rule-text">
              <strong>快速切换：</strong>
              设置默认地址只需与区块链交互一次，通常几秒钟即可完成。
            </div>
          </div>
          <div className="rule-item">
            <span className="rule-icon">✅</span>
            <div className="rule-text">
              <strong>首次添加：</strong>
              您的第一个地址将自动设置为默认地址，无需手动勾选。
            </div>
          </div>
        </div>
      </div>

      {/* 地址内容区域 */}
      <div className="address-content">
        <div className="address-list">
        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner">🔄</div>
            <p>正在加载地址列表...</p>
          </div>
        ) : addresses.length === 0 ? (
          <div className="empty-container">
            <div className="empty-icon">📍</div>
            <h3>暂无收货地址</h3>
            <p>添加您的第一个收货地址</p>
            <button
              className="add-first-btn"
              onClick={() => setShowAddForm(true)}
            >
              ➕ 添加地址
            </button>
          </div>
        ) : (
          addresses.map((address, index) => (
            <div key={index} className="address-card">
              <div className="address-header">
                <div className="address-info">
                  <span className="address-name">{address.name}</span>
                  <span className="address-phone">{address.phone}</span>
                  {address.isDefault && (
                    <span className="default-badge">默认</span>
                  )}
                </div>
                <div className="address-actions">
                  <button
                    className="edit-btn"
                    onClick={() => handleEditAddress(address)}
                  >
                    编辑
                  </button>
                  <button
                    className="delete-btn"
                    onClick={() => handleDeleteAddress(Number(address.addressId))}
                    disabled={isSubmitting}
                  >
                    删除
                  </button>
                </div>
              </div>
              <div className="address-detail">
                {formatAddress(address)}
              </div>
            </div>
          ))
        )}
        </div>
      </div>

      {/* 调试面板 */}
      {showDebugPanel && (
        <div style={{
          marginTop: '20px',
          padding: '16px',
          border: '2px solid #4ecdc4',
          borderRadius: '8px',
          backgroundColor: '#f8f9fa'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
            <h4 style={{ margin: 0, color: '#333' }}>🔍 地址管理调试面板</h4>
            <button
              onClick={runDebugCheck}
              disabled={isDebugging}
              style={{
                backgroundColor: '#4ecdc4',
                color: 'white',
                border: 'none',
                padding: '6px 12px',
                borderRadius: '4px',
                cursor: isDebugging ? 'not-allowed' : 'pointer',
                opacity: isDebugging ? 0.6 : 1
              }}
            >
              {isDebugging ? '检查中...' : '运行检查'}
            </button>
          </div>

          {debugInfo && (
            <div>
              <h5 style={{ margin: '8px 0', color: '#555' }}>调试结果:</h5>
              <pre style={{
                fontSize: '12px',
                backgroundColor: 'white',
                padding: '12px',
                borderRadius: '4px',
                border: '1px solid #ddd',
                overflow: 'auto',
                maxHeight: '300px',
                whiteSpace: 'pre-wrap'
              }}>
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          )}

          <div style={{ marginTop: '12px', fontSize: '14px', color: '#666' }}>
            <p><strong>使用说明:</strong></p>
            <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
              <li>点击"运行检查"查看当前合约状态</li>
              <li>添加地址时观察浏览器控制台日志</li>
              <li>检查 addressCount 是否在添加后增加</li>
              <li>如果有错误，查看 getAddressesError 字段</li>
            </ul>
          </div>
        </div>
      )}

      {/* 添加/编辑表单弹窗 */}
      {showAddForm && (
        <div className="form-overlay">
          <div className="form-modal">
            <div className="form-header">
              <h4>{editingAddress !== null ? '编辑地址' : '添加地址'}</h4>
              <button className="close-btn" onClick={resetForm}>✕</button>
            </div>

            <div className="form-content">
              <div className="form-row">
                <div className="form-group">
                  <label>收货人姓名 *</label>
                  <input
                    type="text"
                    placeholder="请输入收货人姓名"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    maxLength={64}
                  />
                </div>
                <div className="form-group">
                  <label>联系电话 *</label>
                  <input
                    type="tel"
                    placeholder="请输入联系电话"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    maxLength={32}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>省份 *</label>
                  <input
                    type="text"
                    placeholder="请输入省份"
                    value={formData.province}
                    onChange={(e) => setFormData(prev => ({ ...prev, province: e.target.value }))}
                    maxLength={64}
                  />
                </div>
                <div className="form-group">
                  <label>城市 *</label>
                  <input
                    type="text"
                    placeholder="请输入城市"
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                    maxLength={64}
                  />
                </div>
              </div>

              <div className="form-group">
                <label>区县 *</label>
                <input
                  type="text"
                  placeholder="请输入区县"
                  value={formData.district}
                  onChange={(e) => setFormData(prev => ({ ...prev, district: e.target.value }))}
                  maxLength={64}
                />
              </div>

              <div className="form-group">
                <label>详细地址 *</label>
                <textarea
                  placeholder="请输入详细地址"
                  value={formData.detail}
                  onChange={(e) => setFormData(prev => ({ ...prev, detail: e.target.value }))}
                  maxLength={256}
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isDefault || addresses.length === 0}
                    onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                    disabled={addresses.length === 0}
                  />
                  设为默认地址
                  {addresses.length === 0 && (
                    <span className="auto-default-tip">（首个地址自动设为默认）</span>
                  )}
                </label>
                {addresses.length > 0 && (
                  <div className="default-address-tip">
                    💡 购买商品时将使用默认地址，如需使用此地址购买请勾选此选项
                  </div>
                )}
              </div>
            </div>

            <div className="form-actions">
              <button className="cancel-btn" onClick={resetForm}>
                取消
              </button>
              <button
                className="submit-btn"
                onClick={editingAddress !== null ? handleUpdateAddress : handleAddAddress}
                disabled={isSubmitting}
              >
                {isSubmitting ? '提交中...' : editingAddress !== null ? '更新地址' : '添加地址'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
