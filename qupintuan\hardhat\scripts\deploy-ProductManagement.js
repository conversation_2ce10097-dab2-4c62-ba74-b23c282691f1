// scripts/deploy-ProductManagement.js
// 部署可升级的 ProductManagement 合约
require("dotenv").config();
const { ethers, upgrades } = require("hardhat");

async function main() {
  console.log("🔄 部署可升级的 ProductManagement 合约...");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取部署者
    const signers = await ethers.getSigners();
    if (!signers || signers.length === 0) {
      throw new Error("没有找到签名者，请检查 .env 文件中的 PRIVATE_KEY 配置");
    }

    const deployer = signers[0];
    console.log("📝 部署者地址:", deployer.address);
    console.log("💰 部署者余额:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "BNB");

    // 2. 从环境变量获取地址
    const merchantMgmtAddr = process.env.MERCHANT_MANAGEMENT_ADDRESS;
    const pointsMgmtAddr = process.env.POINTS_MANAGEMENT_ADDRESS;
    const addrMgmtAddr = process.env.ADDRESS_MANAGEMENT_ADDRESS;
    const orderMgmtAddr = process.env.ORDER_MANAGEMENT_ADDRESS;
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;

    console.log("📋 配置信息:");
    console.log("   • MerchantManagement地址:", merchantMgmtAddr);
    console.log("   • PointsManagement地址:", pointsMgmtAddr);
    console.log("   • AddressManagement地址:", addrMgmtAddr);
    console.log("   • OrderManagement地址:", orderMgmtAddr);
    console.log("   • Timelock地址:", timelockAddr);

    if (!merchantMgmtAddr) {
      throw new Error("请在 .env 文件中设置 MERCHANT_MANAGEMENT_ADDRESS");
    }
    if (!pointsMgmtAddr) {
      throw new Error("请在 .env 文件中设置 POINTS_MANAGEMENT_ADDRESS");
    }
    if (!addrMgmtAddr) {
      throw new Error("请在 .env 文件中设置 ADDRESS_MANAGEMENT_ADDRESS");
    }
    if (!orderMgmtAddr) {
      throw new Error("请在 .env 文件中设置 ORDER_MANAGEMENT_ADDRESS");
    }
    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS 或 TIMELOCK_ADDRESS");
    }

    // 3. 获取合约工厂
    const ProductManagement = await ethers.getContractFactory("ProductManagement");

    // 4. 部署可升级合约
    console.log("⏳ 部署 ProductManagement 代理合约...");
    const productManagement = await upgrades.deployProxy(
      ProductManagement,
      [merchantMgmtAddr, pointsMgmtAddr, addrMgmtAddr, orderMgmtAddr, timelockAddr], // 传递正确的5个参数
      {
        initializer: "initialize",
        kind: "uups"
      }
    );

    // 5. 等待部署完成
    await productManagement.waitForDeployment();
    const proxyAddress = await productManagement.getAddress();

    console.log("✅ ProductManagement 部署成功!");
    console.log("📍 代理地址:", proxyAddress);

    // 6. 获取实现地址
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);

    // 7. 验证部署结果
    console.log("\n🔍 验证部署结果:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const owner = await productManagement.owner();
    const merchantManagement = await productManagement.merchantManagement();
    const pointsManagement = await productManagement.pointsManagement();
    const addressManagement = await productManagement.addressManagement();
    const timelock = await productManagement.timelock();

    console.log("👤 合约所有者:", owner);
    console.log("🏪 MerchantManagement:", merchantManagement);
    console.log("🎯 PointsManagement:", pointsManagement);
    console.log("📋 AddressManagement:", addressManagement);
    console.log("🔒 Timelock合约:", timelock);
    console.log("✅ MerchantManagement配置:", merchantManagement === merchantMgmtAddr ? "正确" : "错误");
    console.log("✅ PointsManagement配置:", pointsManagement === pointsMgmtAddr ? "正确" : "错误");
    console.log("✅ AddressManagement配置:", addressManagement === addrMgmtAddr ? "正确" : "错误");
    console.log("✅ Timelock配置:", timelock === timelockAddr ? "正确" : "错误");

    // 8. 转移所有权给Timelock
    console.log("\n🔄 转移所有权给Timelock...");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const transferTx = await productManagement.transferOwnership(timelockAddr);
    await transferTx.wait();

    console.log("✅ 所有权已转移给Timelock!");

    // 验证所有权转移
    const newOwner = await productManagement.owner();
    if (newOwner === timelockAddr) {
      console.log("✅ 所有权转移验证成功");
    } else {
      console.log("❌ 所有权转移验证失败");
    }

    console.log("\n📋 部署完成总结:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("✅ 可升级的 ProductManagement 部署成功!");
    console.log("📍 代理地址:", proxyAddress);
    console.log("🔗 实现地址:", implementationAddress);
    console.log("👤 所有者:", timelockAddr);
    console.log("🔒 升级控制: 只有Timelock可以升级");
    console.log("");
    console.log("🆕 功能特性:");
    console.log("   • ✅ 支持UUPS升级模式");
    console.log("   • ✅ Timelock控制升级");
    console.log("   • ✅ 产品管理功能");
    console.log("   • ✅ 商家系统集成");
    console.log("   • ✅ 积分系统集成");
    console.log("   • ✅ 地址管理集成");
    console.log("   • ✅ pause/unpause() - 暂停功能");
    console.log("");
    console.log("🔧 环境变量更新:");
    console.log(`PRODUCT_MANAGEMENT_ADDRESS=${proxyAddress}`);

    // 9. 保存部署信息
    const deploymentInfo = {
      proxyAddress,
      implementationAddress,
      deployer: deployer.address,
      owner: timelockAddr,
      merchantManagement: merchantMgmtAddr,
      pointsManagement: pointsMgmtAddr,
      addressManagement: addrMgmtAddr,
      timelock: timelockAddr,
      timestamp: new Date().toISOString(),
      network: "bscTestnet",
      features: ["UUPS_UPGRADEABLE", "TIMELOCK_PROTECTED", "PRODUCT_MANAGEMENT"],
      securityLevel: "HIGH"
    };

    const fs = require('fs');
    fs.writeFileSync(
      './productmanagement-deployment.json',
      JSON.stringify(deploymentInfo, null, 2)
    );
    console.log("💾 部署信息已保存到 productmanagement-deployment.json");

  } catch (error) {
    console.error("❌ 部署失败:", error);

    if (error.message.includes("insufficient funds")) {
      console.log("💡 提示: 部署者账户BNB余额不足");
    } else if (error.message.includes("MERCHANT_MANAGEMENT_ADDRESS")) {
      console.log("💡 提示: 请先部署 MerchantManagement 合约");
    } else if (error.message.includes("POINTS_MANAGEMENT_ADDRESS")) {
      console.log("💡 提示: 请先部署 PointsManagement 合约");
    } else if (error.message.includes("ADDRESS_MANAGEMENT_ADDRESS")) {
      console.log("💡 提示: 请先部署 AddressManagement 合约");
    } else if (error.message.includes("TIMELOCK_ADDRESS")) {
      console.log("💡 提示: 请检查 Timelock 地址配置");
    }

    process.exit(1);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
