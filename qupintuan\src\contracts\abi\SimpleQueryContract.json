{"_format": "hh-sol-artifact-1", "contractName": "SimpleQueryContract", "sourceName": "contracts/SimpleQueryContract.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "contractName", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "AddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "contract_", "type": "address"}, {"indexed": false, "internalType": "string", "name": "fixType", "type": "string"}], "name": "StorageFixed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "contract_", "type": "address"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "StorageValidationFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "contract_", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "version", "type": "uint256"}], "name": "StorageValidationPassed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newImplementation", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UpgradeAuthorized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERFORMANCE_UPLOADER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "STORAGE_LAYOUT_VERSION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "agentSystemAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "productIds", "type": "uint256[]"}], "name": "batchGetProducts", "outputs": [{"components": [{"internalType": "uint256", "name": "productId", "type": "uint256"}, {"internalType": "address", "name": "merchant", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "formattedPrice", "type": "uint256"}, {"internalType": "uint256", "name": "stock", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "sales", "type": "uint256"}], "internalType": "struct SimpleQueryContract.ProductInfo[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "calculateStorageChecksum", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roomId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "checkClaimEligibility", "outputs": [{"internalType": "bool", "name": "canClaim", "type": "bool"}, {"internalType": "string", "name": "reason", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyStorageFix", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxRoomsToCheck", "type": "uint256"}], "name": "getGroupBuyStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalRooms", "type": "uint256"}, {"internalType": "uint256", "name": "activeRooms", "type": "uint256"}, {"internalType": "uint256", "name": "completedRooms", "type": "uint256"}, {"internalType": "uint256", "name": "totalParticipants", "type": "uint256"}], "internalType": "struct SimpleQueryContract.GroupBuyStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getHealthReport", "outputs": [{"internalType": "bool", "name": "is<PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "string[]", "name": "issues", "type": "string[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxRoomsToCheck", "type": "uint256"}], "name": "getLockingStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalNodeStaked", "type": "uint256"}, {"internalType": "uint256", "name": "totalGroupBuyLocked", "type": "uint256"}, {"internalType": "uint256", "name": "totalBuybackLocked", "type": "uint256"}, {"internalType": "uint256", "name": "totalLocked", "type": "uint256"}, {"internalType": "uint256", "name": "activeGroupBuyRooms", "type": "uint256"}, {"internalType": "uint256", "name": "activeBuybackRooms", "type": "uint256"}], "internalType": "struct SimpleQueryContract.LockingStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getNodeStakingStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalStakedQPT", "type": "uint256"}, {"internalType": "uint256", "name": "totalActiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "currentRequiredStake", "type": "uint256"}, {"internalType": "uint256", "name": "maxNodes", "type": "uint256"}, {"internalType": "uint256", "name": "averageStakeAmount", "type": "uint256"}], "internalType": "struct SimpleQueryContract.NodeStakingStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPointsSystemStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalGroupBuyPoints", "type": "uint256"}, {"internalType": "uint256", "name": "totalSalesPoints", "type": "uint256"}, {"internalType": "uint256", "name": "totalBurnedPoints", "type": "uint256"}, {"internalType": "uint256", "name": "totalUSDTExchanged", "type": "uint256"}, {"internalType": "uint256", "name": "totalUSDTBalance", "type": "uint256"}, {"internalType": "uint256", "name": "pointsDecimals", "type": "uint256"}, {"internalType": "uint256", "name": "minExchangePoints", "type": "uint256"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}], "internalType": "struct SimpleQueryContract.PointsSystemStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "maxProductsToCheck", "type": "uint256"}], "name": "getProductStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalProducts", "type": "uint256"}, {"internalType": "uint256", "name": "activeProducts", "type": "uint256"}, {"internalType": "uint256", "name": "totalSales", "type": "uint256"}, {"internalType": "uint256", "name": "averagePrice", "type": "uint256"}], "internalType": "struct SimpleQueryContract.ProductStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "key", "type": "string"}], "name": "getStorageChecksum", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserCompleteStatus", "outputs": [{"components": [{"internalType": "address", "name": "user", "type": "address"}, {"components": [{"internalType": "address", "name": "inviter", "type": "address"}, {"internalType": "uint8", "name": "level", "type": "uint8"}, {"internalType": "uint256", "name": "totalPerformance", "type": "uint256"}, {"internalType": "uint256", "name": "referralsCount", "type": "uint256"}, {"internalType": "bool", "name": "isRegistered", "type": "bool"}, {"internalType": "uint256", "name": "personalPerformance", "type": "uint256"}, {"internalType": "bool", "name": "hasAdminRole", "type": "bool"}, {"internalType": "bool", "name": "hasUploaderRole", "type": "bool"}, {"internalType": "bool", "name": "isSystemAdmin", "type": "bool"}], "internalType": "struct SimpleQueryContract.AgentInfo", "name": "agentInfo", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "groupBuyPoints", "type": "uint256"}, {"internalType": "uint256", "name": "salesPoints", "type": "uint256"}, {"internalType": "uint256", "name": "lastExchangeTime", "type": "uint256"}, {"internalType": "bool", "name": "canExchangeGroupBuy", "type": "bool"}, {"internalType": "bool", "name": "canExchangeSales", "type": "bool"}, {"internalType": "bool", "name": "canExchangeNow", "type": "bool"}, {"internalType": "uint256", "name": "exchangeCooldownRemaining", "type": "uint256"}], "internalType": "struct SimpleQueryContract.PointsInfo", "name": "pointsInfo", "type": "tuple"}, {"components": [{"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "lastClaimDate", "type": "uint256"}, {"internalType": "uint256", "name": "requiredStakeAmount", "type": "uint256"}, {"internalType": "bool", "name": "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "canClaimToday", "type": "bool"}, {"internalType": "uint256", "name": "totalActiveNodes", "type": "uint256"}, {"internalType": "uint256", "name": "maxNodes", "type": "uint256"}, {"internalType": "bool", "name": "canStakeNode", "type": "bool"}, {"internalType": "uint256", "name": "dailyReward", "type": "uint256"}, {"internalType": "uint256", "name": "totalDividends", "type": "uint256"}, {"internalType": "uint256", "name": "snapshotDividends", "type": "uint256"}, {"internalType": "uint256", "name": "lastSnapshotTime", "type": "uint256"}, {"internalType": "uint256", "name": "dailyClaimedCount", "type": "uint256"}, {"internalType": "uint256", "name": "stakedAmount", "type": "uint256"}, {"internalType": "uint256", "name": "activationTime", "type": "uint256"}, {"internalType": "bool", "name": "canCreateSnapshot", "type": "bool"}, {"internalType": "string", "name": "snapshotRestrictionReason", "type": "string"}], "internalType": "struct SimpleQueryContract.NodeInfo", "name": "nodeInfo", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "totalLocked", "type": "uint256"}, {"internalType": "uint256", "name": "totalUnlocked", "type": "uint256"}, {"internalType": "uint256", "name": "pendingUnlock", "type": "uint256"}, {"internalType": "uint256", "name": "nextUnlockTime", "type": "uint256"}, {"internalType": "bool", "name": "canUnlock", "type": "bool"}], "internalType": "struct SimpleQueryContract.LockerInfo", "name": "lockerInfo", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "currentRound", "type": "uint256"}, {"internalType": "bool", "name": "hasParticipatedCurrentRound", "type": "bool"}], "internalType": "struct SimpleQueryContract.BuybackInfo", "name": "buybackInfo", "type": "tuple"}], "internalType": "struct SimpleQueryContract.UserCompleteStatus", "name": "status", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roomId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRoleInRoom", "outputs": [{"components": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "roomId", "type": "uint256"}, {"internalType": "bool", "name": "hasJoined", "type": "bool"}, {"internalType": "bool", "name": "hasClaimed", "type": "bool"}, {"internalType": "bool", "name": "isCreator", "type": "bool"}, {"internalType": "bool", "name": "isParticipant", "type": "bool"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "bool", "name": "canClaim", "type": "bool"}, {"internalType": "uint256", "name": "participantIndex", "type": "uint256"}, {"internalType": "bool", "name": "roomClosed", "type": "bool"}, {"internalType": "bool", "name": "roomSuccessful", "type": "bool"}, {"internalType": "address", "name": "winner<PERSON><PERSON><PERSON>", "type": "address"}], "internalType": "struct SimpleQueryContract.RoomRoleInfo", "name": "roleInfo", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "groupBuyRoomAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agentSystem", "type": "address"}, {"internalType": "address", "name": "_groupBuyRoom", "type": "address"}, {"internalType": "address", "name": "_pointsManagement", "type": "address"}, {"internalType": "address", "name": "_nodeStaking", "type": "address"}, {"internalType": "address", "name": "_qptLocker", "type": "address"}, {"internalType": "address", "name": "_qpt<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "_productManagement", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_timelock", "type": "address"}], "name": "initializeTimelock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "isEmergencyFixMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nodeStakingAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pointsManagementAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "postUpgradeValidation", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "productManagementAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxiableUUID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "qpt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "qpt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "savePreUpgradeState", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "enabled", "type": "bool"}], "name": "setEmergencyFixMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_productManagement", "type": "address"}], "name": "setProductManagementAddress", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "timelock", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_agentSystem", "type": "address"}, {"internalType": "address", "name": "_groupBuyRoom", "type": "address"}, {"internalType": "address", "name": "_pointsManagement", "type": "address"}, {"internalType": "address", "name": "_nodeStaking", "type": "address"}, {"internalType": "address", "name": "_qptLocker", "type": "address"}, {"internalType": "address", "name": "_qpt<PERSON><PERSON><PERSON>", "type": "address"}], "name": "updateContractAddresses", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "validateBasicState", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validateStorageLayout", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "validateUpgradeConsistency", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x60a0806040523461003157306080526146f7908161003782396080518181816123720152818161246d015261286e0152f35b600080fdfe60e0604052600436101561001257600080fd5b60003560e01c80620e81771461302457806306007c1514612efe5780630cdc002d14612e3657806311c6702814612d6f5780631bf57d6614612d365780633587647614612b325780633659cfe6146128495780633b4d2cfb146128305780633da846251461280757806344287e25146127de5780634a5d5f9c14611c155780634c167fb0146127b45780634f1ef2861461241e57806352d1902d1461235f57806358536bdd146123355780635c975abb146123125780636c6ae0e7146122a95780636f7d7b8914612262578063715018a61461221757806375b238fc146121ee578063765fdaf1146121cb57806379f43065146121605780637ab8bb45146121155780637ac5234d146120eb5780638438eae8146120d05780638a13f9e9146120415780638da5cb5b146120185780638e61c85514611e7f57806394186ffd14611c31578063a19f05fc14611c15578063a886990614611bec578063aaea935914611bc2578063acdc674e14611b9f578063aededd1114611b32578063bb20764414611b09578063c123cf8f14611769578063d33219b41461173f578063ef49eb66146104f2578063f2fde38b14610463578063f6ebc190146103b9578063f99a38c4146103945763fa363cf0146101e957600080fd5b3461032057600036600319011261032057610202614188565b506101005460405163cce500eb60e01b81526001600160a01b03909116906020908181600481865afa90811561032c57600091610367575b506040516311de0b0160e21b8152928284600481845afa93841561032c57600094610338575b50928260049460405195868092638f16e1cd60e01b82525afa90811561032c576000916102f7575b60a0945061029461430c565b9383156102ef576102a58486613f9c565b935b60806040516102b581613197565b8781528381019283526040810194855260608101958652019485526040519586525190850152516040840152516060830152516080820152f35b6000936102a7565b90508284813d8311610325575b61030e818361321f565b810103126103205760a0935190610288565b600080fd5b503d610304565b6040513d6000823e3d90fd5b93508284813d8311610360575b61034f818361321f565b810103126103205792519282610260565b503d610345565b90508181813d831161038d575b61037e818361321f565b8101031261032057513861023a565b503d610374565b346103205760003660031901126103205760206103af6141b3565b6040519015158152f35b346103205760c0366003190112610320576103d261308a565b6103da6130a0565b906103e36130b6565b6103eb6130cc565b6103f36130e2565b906103fc6130f8565b9461040561421d565b60018060a01b03938480928160018060a01b031998168860fd54161760fd55168660fe54161760fe55168460ff54161760ff558261010091168482541617905581610101911683825416179055610102921690825416179055600080f35b346103205760203660031901126103205761047c61308a565b61048461421d565b6001600160a01b0381161561049e5761049c906142d5565b005b60405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201526564647265737360d01b6064820152608490fd5b346103205760203660031901126103205761050b61308a565b604051906105188261310e565b6000825260405161052881613129565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c0820152600060e08201526000610100820152602083015260405161057681613145565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c082015260408301526040516105b581613160565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c0820152600060e08201526000610100820152600061012082015260006101408201526000610160820152600061018082015260006101a082015260006101c082015260006101e08201526060610200820152606083015261063f614188565b60808301526040516106508161317c565b6000808252602082015260a08301526001600160a01b03818116835260fd541680611467575b5060ff546001600160a01b0316806111c1575b50610100546001600160a01b031680610abc575b50610101546001600160a01b0316806109de575b50610102546001600160a01b0316806108de575b8260405180916020825260018060a01b038151166020830152610100602082015160018060a01b03815116604085015260ff602082015116606085015260408101516080850152606081015160a08501526080810151151560c085015260a081015160e085015260c081015115158285015260e081015115156101208501520151151561014083015260c060408201518051610160850152602081015161018085015260408101516101a0850152606081015115156101c0850152608081015115156101e085015260a081015115156102008501520151610220830152602060a061088d610200606085015161032061024088015280511515610340880152848101516103608801526040810151610380880152606081015115156103a0880152608081015115156103c0880152838101516103e088015260c081015161040088015260e081015115156104208801526101008101516104408801526101208101516104608801526101408101516104808801526101608101516104a08801526101808101516104c08801526101a08101516104e08801526101c08101516105008801526101e081015115156105208801520151610220610540870152610560860190613065565b9260808082015180516102608801528481015161028088015260408101516102a088015260608101516102c0880152015115156102e086015201518051610300850152015115156103208301520390f35b60405163a32bf59760e01b815291602083600481855afa600093816109aa575b5061090a575b506106c5565b602061092f9160009360405180809581946351a1cc5160e11b83528960048401613979565b03915afa6000918161096e575b50610966575b50604051916109508361317c565b82521515602082015260a0820152818080610904565b905083610942565b9091506020813d6020116109a2575b8161098a6020938361321f565b810103126103205761099b9061334d565b908561093c565b3d915061097d565b9093506020813d6020116109d6575b816109c66020938361321f565b81010312610320575192856108fe565b3d91506109b9565b6040516318e5938160e11b81526001600160a01b038316600482015290608090829060249082905afa9060009160009181600091600093610a70575b50610a28575b5050506106b1565b814210159283610a66575b60405194610a4086613197565b855260208501526040840152606083015215156080820152608083015282808080610a20565b8115159350610a33565b9350935050506080813d608011610ab4575b81610a8f6080938361321f565b8101031261032057805160208201516040830151606090930151919390929087610a1a565b3d9150610a82565b604051637dbd1d2760e01b81526001600160a01b0383166004820152600081602481855afa60009181611018575b50610e0f5750604051639407af1f60e01b81526001600160a01b0383166004820152602081602481855afa60009181610dd3575b50610b2d575b50505b8261069d565b6040516306052ccb60e41b81526001600160a01b038416600482015260009283928392839290602081602481855afa60009181610d9f575b50610d97575b506040516311de0b0160e21b8152602081600481855afa60009181610d63575b50610d5b575b5060405163cce500eb60e01b8152602081600481855afa60009181610d27575b50610d1e575b50602060049160405192838092638f16e1cd60e01b82525afa60009181610cea575b50610ce2575b508080610cd2575b1594859485610cc8575b60405196610bfe88613160565b15875281602088015260408701526201518042041460608601521515608085015260a084015260c0830152151560e08201526000610100820152600061012082015260006101408201526000610160820152600061018082015260006101a082015260006101c082015260006101e0820152604051610c7c81613204565b602781527f46616c6c6261636b206d6f6465202d206e6f2061637469766174696f6e2074696020820152666d65206461746160c81b604082015261020082015260608301528280610b24565b8484109550610bf1565b6201518042048614159150610be7565b925087610bdf565b9091506020813d602011610d16575b81610d066020938361321f565b8101031261032057519089610bd9565b3d9150610cf9565b92506020610bb7565b9091506020813d602011610d53575b81610d436020938361321f565b810103126103205751908a610bb1565b3d9150610d36565b945088610b91565b9091506020813d602011610d8f575b81610d7f6020938361321f565b810103126103205751908a610b8b565b3d9150610d72565b955088610b6b565b9091506020813d602011610dcb575b81610dbb6020938361321f565b810103126103205751908a610b65565b3d9150610dae565b9091506020813d602011610e07575b81610def6020938361321f565b8101031261032057610e009061334d565b9085610b1e565b3d9150610de2565b809160006004602060009360405192838092635d1749f160e11b82525afa60009181610fe1575b50610fd9575b506101015483906001600160a01b031680610f5f575b50808080808080808080806102606102406102206102006101e06101c060c061014061010060e060a06080606060208e015115159d6040810151885201519d015115159d015115159d01519d01519d015115159d01519d01519d01519d01519d01519f015115159f015160805260405160a052610ed060a051613160565b60a0515260c051602060a0510152604060a0510152606060a0510152608060a051015260a08051015260c060a051015260e060a051015261010060a051015261012060a051015261014060a051015261016060a051015261018060a05101526101a060a05101526101c060a05101526101e060a051015260805161020060a051015260a0516060830152610b27565b60405163d4cc7c4360e01b81526001600160a01b03881660048201529150602090829060249082905afa60009181610fa3575b5090849115610e5257925080610e52565b915093506020813d602011610fd1575b81610fc06020938361321f565b810103126103205751849384610f92565b3d9150610fb3565b905086610e3c565b945090506020843d602011611010575b81610ffe6020938361321f565b81010312610320578493519088610e36565b3d9150610ff1565b9091503d806000833e61102b818361321f565b6020828281010312610320578151916001600160401b03831161032057610280838201838301031261032057604051926001600160401b036102808501908111908511176111ab5761028084016040526110868183016132b9565b845261109660208284010161334d565b602085015281810160408181015190860152606080820151908601526110be9060800161334d565b60808501526110d160a08284010161334d565b60a085015281810160c0818101519086015260e0808201519086015261010080820151908601526101208082015190860152611110906101400161334d565b610140850152818101610160818101519086015261018080820151908601526101a080820151908601526101c080820151908601526101e08082015190860152610200808201519086015261022080820151908601526102409061117590820161334d565b908501528181016102600151906001600160401b0382116103205761119e9383019201016135da565b6102608201529085610aea565b634e487b7160e01b600052604160045260246000fd5b60405163198a695d60e21b81526001600160a01b038316600482015290602082602481845afa60009281611433575b506111fc575b50610689565b60405163978f7ae760e01b81526001600160a01b03841660048201526000928392839283929190602081602481855afa600091816113ff575b506113f7575b50604051630d06f67f60e01b81526001600160a01b0388166004820152602081602481855afa600091816113c3575b506113bb575b50604051632ce2f3b160e01b8152602081600481855afa60009181611387575b5061137e575b5060206004916040519283809263fd8b5f5b60e01b82525afa6000918161134a575b50611342575b5060009385151580611330575b611312575b50604051946112de86613145565b81865282602087015260408601521515606085015210156080830152801560a083015260c0820152604083015282806111f6565b61132991945061132290866133c2565b4290613d56565b92876112d0565b5061133b81876133c2565b42106112cb565b9350876112be565b9091506020813d602011611376575b816113666020938361321f565b81010312610320575190896112b8565b3d9150611359565b93506020611296565b9091506020813d6020116113b3575b816113a36020938361321f565b810103126103205751908a611290565b3d9150611396565b955088611270565b9091506020813d6020116113ef575b816113df6020938361321f565b810103126103205751908a61126a565b3d91506113d2565b92508861123b565b9091506020813d60201161142b575b8161141b6020938361321f565b810103126103205751908a611235565b3d915061140e565b9092506020813d60201161145f575b8161144f6020938361321f565b81010312610320575191856111f0565b3d9150611442565b604051636386c1c760e01b81526001600160a01b038316600482015260c081602481855afa600091600080600091846000946000966116cc575b506114b2575b505050505050610676565b60ff90604051966114c288613129565b60018060a01b03168752166020860152604085015260608401521515608083015260a0820152600060c0820152600060e082015260006101008201526020840152604051632474521560e21b8152600080516020614642833981519152600482015260018060a01b0383166024820152602081604481855afa60009181611690575b5061167d575b50604051632474521560e21b815260008051602061460283398151915260048201526001600160a01b0383166024820152602081604481855afa6000918161163a575b5091602091600493611628575b5060405163a239e33d60e01b815292839182905afa600091816115ec575b506115c9575b8080808080806114a7565b60208301516001600160a01b0383811692169190911461010090910152826115be565b9091506020813d602011611620575b816116086020938361321f565b8101031261032057611619906132b9565b90846115b8565b3d91506115fb565b60e0838701510190151590528561159a565b9150916020823d602011611675575b816116566020938361321f565b810103126103205760049261166c60209361334d565b9250909261158d565b3d9150611649565b60c060208501510190151590528361154a565b9091506020813d6020116116c4575b816116ac6020938361321f565b81010312610320576116bd9061334d565b9085611544565b3d915061169f565b965094505050505060c0823d60c011611737575b816116ed60c0938361321f565b81010312610320576116fe826132b9565b60208301519260ff8416840361032057604081015160608201519060a06117276080850161334d565b930151939590919293948a6114a1565b3d91506116e0565b3461032057600036600319011261032057610103546040516001600160a01b039091168152602090f35b34610320576040366003190112610320576117826130a0565b60405161018081016001600160401b038111828210176111ab57604052600060408201526000606082015260006080820152600060a0820152600060c0820152600060e0820152600061010082015260006101208201526000610140820152600061016082015260018060a01b0382168152600435602082015260018060a01b0360fe5416806118b1575b610180826040519060018060a01b0381511682526020810151602083015260408101511515604083015260608101511515606083015260808101511515608083015260a0810151151560a083015260c0810151151560c083015260e0810151151560e08301526101008101516101008301526101208101511515610120830152610140810151151561014083015261016060018060a01b0391015116610160820152f35b604051636819471960e01b815290602082806118d38760048035908401613979565b0381845afa60009281611acd575b506118ed575b5061180d565b811515604084015260405163120aa87760e01b8152602081806119168860043560048401613979565b0381855afa60009181611a8a575b5091600091602493611a7d575b5060405192838092636d8a74cb60e01b825260043560048301525afa600091829182918291829181611a49575b5061196d575b505050506118e7565b6001600160a01b03908116888216908114608089015286151560a0890152911690811460c087015282151561012087015283151561014087015261016086015260005b8151811015611a3d576001600160a01b03878116906119cf838561369e565b5116146119de576001016119b0565b6101008601525061018094505b80611a31575b6119ff575b80808080611964565b15611a1357151560e08201525b82806119f6565b80611a25575b151560e0820152611a0c565b50608081015115611a19565b506060830151156119f1565b505061018094506119eb565b935094505050611a6c91503d806000833e611a64818361321f565b81019061335a565b50919590949293509091908961195e565b1515606086015286611931565b9150916020823d602011611ac5575b81611aa66020938361321f565b8101031261032057602492611abc60009361334d565b92509092611924565b3d9150611a99565b9092506020813d602011611b01575b81611ae96020938361321f565b8101031261032057611afa9061334d565b91856118e1565b3d9150611adc565b346103205760003660031901126103205760ff546040516001600160a01b039091168152602090f35b34610320576020366003190112610320576004356001600160401b038111610320573660238201121561032057611b8c6020611b798193369060248160040135910161325d565b8160405193828580945193849201613042565b8101600181520301902054604051908152f35b3461032057600036600319011261032057602060ff600254166040519015158152f35b3461032057600036600319011261032057610100546040516001600160a01b039091168152602090f35b346103205760003660031901126103205760fd546040516001600160a01b039091168152602090f35b3461032057600036600319011261032057602060405160018152f35b34610320576020806003193601126103205760043590604051611c538161310e565b60008152600082820152600060408201526060926000606083015260809360006080840152600060a080940152611c8861430c565b60fe546040516357aa9bfd60e11b81529195600094859491936001600160a01b039385908290600490829088165afa90811561032c57600091611e52575b5081811115611e4a5750915b61010154811660005b848110611d4e57505050505060c09550611cf583866133c2565b9060405190611d038261310e565b86825280820194855285604083019260008452606081019485526080810195865201946000865260405197885251908701525160408601525160608501525160808401525190820152f35b604051636790d2b560e01b8152600481018290528981602481865afa9060008560009184600094600096611ded575b50611d90575b5050505050600101611cdb565b16151580611de4575b611da5575b8581611d83565b611db091929a6133c2565b98159081611ddb575b50611dc7575b8b8080611d9e565b95611dd36001916133e5565b969050611dbf565b9050158c611db9565b50801515611d99565b945094505050508a81813d8311611e43575b611e09818361321f565b81010312610320578085611e1d8f936132b9565b92611e388b84015191611e318b860161334d565b940161334d565b939092939438611d7d565b503d611dff565b905091611cd2565b90508481813d8311611e78575b611e69818361321f565b8101031261032057518a611cc6565b503d611e5f565b346103205760003660031901126103205760405161016081016001600160401b038111828210176111ab57604052600a815260005b61014081106120075750600090611ec96141b3565b15611fb3575b611ed8826132cd565b90611ee6604051928361321f565b828252601f19611ef5846132cd565b0160005b818110611fa257505060005b838110611f7857505060405190604082019215825260206040602084015281518094526060830193602060608260051b8601019301916000955b828710611f4c5785850386f35b909192938280611f68600193605f198a82030186528851613065565b9601920196019592919092611f3f565b80611f856001928461369e565b51611f90828661369e565b52611f9b818561369e565b5001611f05565b806060602080938701015201611ef9565b9050604051611fc18161317c565b602081527f53746f72616765206c61796f75742076616c69646174696f6e206661696c65646020820152611ff482613691565b52611ffe81613691565b50600190611ecf565b806060602080938501015201611eb4565b34610320576000366003190112610320576035546040516001600160a01b039091168152602090f35b34610320576020366003190112610320577f943e9d45a11aaae5d87503e3bc248665d9807856e5cf2bdb4a988bee44422781608061207d61308a565b61208561421d565b60018060a01b03166101048160018060a01b031982541617905560405190604082526011604083015270141c9bd91d58dd13585b9859d95b595b9d607a1b60608301526020820152a1005b346103205760003660031901126103205760206103af614152565b3461032057600036600319011261032057610101546040516001600160a01b039091168152602090f35b34610320576020366003190112610320576080612133600435613fbc565b61215e6040518092606080918051845260208101516020850152604081015160408501520151910152565bf35b346103205760003660031901126103205761010061217c613d63565b60e060405191805183526020810151602084015260408101516040840152606081015160608401526080810151608084015260a081015160a084015260c081015160c0840152015160e0820152f35b346103205760003660031901126103205760206121e6613d04565b604051908152f35b346103205760003660031901126103205760206040516000805160206146428339815191528152f35b346103205760003660031901126103205761223061421d565b603580546001600160a01b031981169091556000906001600160a01b03166000805160206145e28339815191528280a3005b346103205760403660031901126103205761228661227e6130a0565b600435613992565b906122a560405192839215158352604060208401526040830190613065565b0390f35b34610320576000366003190112610320576122c261421d565b6122ca6141b3565b156122d157005b60405162461bcd60e51b815260206004820152601960248201527814dd1bdc9859d9481d985b1a59185d1a5bdb8819985a5b1959603a1b6044820152606490fd5b3461032057600036600319011261032057602060ff606754166040519015158152f35b3461032057600036600319011261032057610102546040516001600160a01b039091168152602090f35b34610320576000366003190112610320577f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031630036123b85760206040516000805160206145c28339815191528152f35b60405162461bcd60e51b815260206004820152603860248201527f555550535570677261646561626c653a206d757374206e6f742062652063616c6044820152771b1959081d1a1c9bdd59da0819195b1959d85d1958d85b1b60421b6064820152608490fd5b60403660031901126103205761243261308a565b60249081356001600160401b038111610320573660238201121561032057612463903690848160040135910161325d565b6001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116919061249c308414156138db565b6124b96000805160206145c283398151915293828554161461392a565b8316801561277757833b156127355760ff60025416156126ea575b6124dc613d04565b602a604051697072655570677261646560b01b81526001600a8201522055604051924284528160008051602061468283398151915260208096a26000805160206145828339815191525460ff161561253c575050505061049c9150614454565b6040516352d1902d60e01b81529394938581600481865afa600091816126bb575b506125ab5760405162461bcd60e51b815260048101879052602e818901526000805160206146a283398151915260448201526d6f6e206973206e6f74205555505360901b6064820152608490fd5b9591929493950361267857506125c084614454565b600080516020614622833981519152600080a2815115801590612670575b6125e457005b61049c92600080604051946125f886613204565b602786527f416464726573733a206c6f772d6c6576656c2064656c65676174652063616c6c85870152660819985a5b195960ca1b60408701528481519101845af4903d15612667573d61264a81613242565b90612658604051928361321f565b8152600081943d92013e6144e4565b606092506144e4565b5060016125de565b8260296084926040519262461bcd60e51b845260048401528201526000805160206146628339815191526044820152681a58589b195555525160ba1b6064820152fd5b9091508681813d83116126e3575b6126d3818361321f565b810103126103205751908861255d565b503d6126c9565b6126f26141b3565b6124d45760405162461bcd60e51b8152602060048201526018818701527714dd1bdc9859d9481b185e5bdd5d0818dbdc9c9d5c1d195960421b6044820152606490fd5b60405162461bcd60e51b815260206004820152601b818701527a125b5c1b195b595b9d185d1a5bdb881b9bdd0819195c1b1bde5959602a1b6044820152606490fd5b60405162461bcd60e51b8152602060048201526016818701527524b73b30b634b21034b6b83632b6b2b73a30ba34b7b760511b6044820152606490fd5b3461032057600036600319011261032057610104546040516001600160a01b039091168152602090f35b346103205760003660031901126103205760206040516000805160206146028339815191528152f35b346103205760003660031901126103205760fe546040516001600160a01b039091168152602090f35b346103205760003660031901126103205761049c61421d565b34610320576020806003193601126103205761286361308a565b906001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081169061289c308314156138db565b6128b96000805160206145c283398151915292828454161461392a565b83168015612af457833b15612ab15760ff6002541615612a65575b6128dc613d04565b602a604051697072655570677261646560b01b81526001600a82015220558060008051602061468283398151915284604051428152a26040519161291f836131e9565b600083526000805160206145828339815191525460ff1615612948575050505061049c90614454565b6040516352d1902d60e01b81528481600481865afa60009181612a36575b506129b55760405162461bcd60e51b815260048101869052602e60248201526000805160206146a283398151915260448201526d6f6e206973206e6f74205555505360901b6064820152608490fd5b939293036129f1576129c684614454565b600080516020614622833981519152600080a28151158015906129e9576125e457005b5060006125de565b60405162461bcd60e51b815260048101839052602960248201526000805160206146628339815191526044820152681a58589b195555525160ba1b6064820152608490fd5b9091508581813d8311612a5e575b612a4e818361321f565b8101031261032057519087612966565b503d612a44565b612a6d6141b3565b6128d45760405162461bcd60e51b815260048101849052601860248201527714dd1bdc9859d9481b185e5bdd5d0818dbdc9c9d5c1d195960421b6044820152606490fd5b60405162461bcd60e51b815260048101849052601b60248201527a125b5c1b195b595b9d185d1a5bdb881b9bdd0819195c1b1bde5959602a1b6044820152606490fd5b60405162461bcd60e51b815260048101849052601660248201527524b73b30b634b21034b6b83632b6b2b73a30ba34b7b760511b6044820152606490fd5b346103205760e036600319011261032057612b4b61308a565b612b536130a0565b90612b5c6130b6565b90612b656130cc565b612b6d6130e2565b90612b766130f8565b9060c4359260018060a01b03918285168095036103205760005460ff98898260081c16159889809a612d2a575b8015612d14575b15612cb857858092612c158c8e9660ff1991600183831617600055612ca6575b50612be48760005460081c16612bdf81614275565b614275565b612bed336142d5565b6000549e8f60081c1690612c0082614275565b612c0982614275565b60675416606755614275565b8160018060a01b03199b168b60fd54161760fd55168960fe54161760fe551687825416179055826101009116868254161790558161010191168582541617905561010291168382541617905561010491825416179055612c7157005b61ff0019166000557f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498602060405160018152a1005b61ffff1916610101176000558f612bca565b60405162461bcd60e51b815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201526d191e481a5b9a5d1a585b1a5e995960921b6064820152608490fd5b50303b158015612baa575060018b841614612baa565b5060018b841610612ba3565b346103205760203660031901126103205760043580151580910361032057612d5c61421d565b60ff801960025416911617600255600080f35b3461032057602036600319011261032057612d8861308a565b612d9061421d565b610103805490916001600160a01b0390818316612df25716908115612dbe576001600160a01b031916179055005b60405162461bcd60e51b815260206004820152600c60248201526b5a65726f206164647265737360a01b6044820152606490fd5b60405162461bcd60e51b815260206004820152601c60248201527b151a5b595b1bd8dac8185b1c9958591e481a5b9a5d1a585b1a5e995960221b6044820152606490fd5b3461032057600036600319011261032057612e4f61421d565b60ff6067541615612ec25760405160208152602360208201527f53696d706c655175657279436f6e74726163742073746f7261676520636865636040820152621ad95960ea1b60608201527f4b7382446c9963a3df8a63f09f9dfb8d0fbca2d60f96266274d3e82eec99ca2b60803092a2005b60405162461bcd60e51b815260206004820152601460248201527314185d5cd8589b194e881b9bdd081c185d5cd95960621b6044820152606490fd5b3461032057602080600319360112610320576004356001600160401b038082116103205736602383011215610320578160040135908111610320573660248260051b84010111610320576024612f5492016136b2565b6040519082820192808352815180945260408301938160408260051b8601019301916000955b828710612f875785850386f35b909192938280600192603f19898203018552875190612fdf612fcc61012084518452878060a01b03868601511686850152604085015190806040860152840190613065565b6060808501519084830390850152613065565b916080808201519083015260a0808201519083015260c0808201519083015260e0810151151560e0830152610100809101519101529601920196019592919092612f7a565b346103205760203660031901126103205760806121336004356133f4565b60005b8381106130555750506000910152565b8181015183820152602001613045565b9060209161307e81518092818552858086019101613042565b601f01601f1916010190565b600435906001600160a01b038216820361032057565b602435906001600160a01b038216820361032057565b604435906001600160a01b038216820361032057565b606435906001600160a01b038216820361032057565b608435906001600160a01b038216820361032057565b60a435906001600160a01b038216820361032057565b60c081019081106001600160401b038211176111ab57604052565b61012081019081106001600160401b038211176111ab57604052565b60e081019081106001600160401b038211176111ab57604052565b61022081019081106001600160401b038211176111ab57604052565b604081019081106001600160401b038211176111ab57604052565b60a081019081106001600160401b038211176111ab57604052565b608081019081106001600160401b038211176111ab57604052565b61010081019081106001600160401b038211176111ab57604052565b602081019081106001600160401b038211176111ab57604052565b606081019081106001600160401b038211176111ab57604052565b601f909101601f19168101906001600160401b038211908210176111ab57604052565b6001600160401b0381116111ab57601f01601f191660200190565b92919261326982613242565b91613277604051938461321f565b829481845281830111610320578281602093846000960137010152565b604051906132a1826131b2565b60006060838281528260208201528260408201520152565b51906001600160a01b038216820361032057565b6001600160401b0381116111ab5760051b60200190565b9080601f83011215610320578151906020916132ff816132cd565b9361330d604051958661321f565b81855260208086019260051b82010192831161032057602001905b828210613336575050505090565b838091613342846132b9565b815201910190613328565b5190811515820361032057565b9160e0838303126103205761336e836132b9565b9260208101519260408201519060018060401b038211610320576133939183016132e4565b916133a06060830161334d565b916133ad6080820161334d565b9160c06133bc60a084016132b9565b92015190565b919082018092116133cf57565b634e487b7160e01b600052601160045260246000fd5b60001981146133cf5760010190565b6133fc613294565b5060fe546001600160a01b039081169190821561358957604092604051936357aa9bfd60e11b8552600492602086600481865afa95861561032c57600096613552575b506000928394849680891160001461354a57935b855b858110613483575050505050506040519361346f856131b2565b845260208401526040830152606082015290565b8151636d8a74cb60e01b8152838101829052600081602481885afa9060008781848194829661351f575b506134c0575b5050505050600101613455565b1615159081613515575b506134d8575b8087816134b3565b996134e8919a99929951906133c2565b9815613502576134f96001916133e5565b975b90386134d0565b969561350f6001916133e5565b966134fb565b90501515386134ca565b929450945061353892503d8091833e611a64818361321f565b505094929193905090929394386134ad565b508793613453565b90956020823d602011613581575b8161356d6020938361321f565b8101031261357e575051943861343f565b80fd5b3d9150613560565b505050604051613598816131b2565b6000815260006020820152600060408201526000606082015290565b91908110156135c45760051b0190565b634e487b7160e01b600052603260045260246000fd5b81601f820112156103205780516135f081613242565b926135fe604051948561321f565b818452602082840101116103205761361c9160208085019101613042565b90565b90610100828203126103205781519261363a602084016132b9565b60408401519093906001600160401b0390818111610320578461365e9184016135da565b936060830151918211610320576136769183016135da565b9160808201519160a08101519160e06133bc60c0840161334d565b8051156135c45760200190565b80518210156135c45760209160051b010190565b9190916136be836132cd565b9060406136cd8151938461321f565b848352601f196136dc866132cd565b0160005b818110613888575050610104546001600160a01b0390811695909190861561387f5760005b818110613716575093955050505050565b8061372460019284886135b4565b84516303d6605960e51b8152903560048201526000816024818d5afa6000918283908485908693868896899861384a575b506137eb57505050505050505061376d8184886135b4565b3584519061377a82613129565b815260006020820152845161378e816131e9565b600081528582015284516137a1816131e9565b60008152606082015260006080820152600060a0820152600060c0820152600060e082015260006101008201526137d8828961369e565b526137e3818861369e565b505b01613705565b8c6137f5846141ea565b948d51996138028b613129565b8a521660208901528b8801526060870152608086015260a085015260c0840152151560e0830152610100820152613839828961369e565b52613844818861369e565b506137e5565b96509794505050505061386f92503d8091833e613867818361321f565b81019061361f565b9697909591949193919038613755565b50929450505050565b602090835161389681613129565b6000815282600081830152606080878401528083015260006080830152600060a0830152600060c0830152600060e083015260006101008301528288010152016136e0565b156138e257565b60405162461bcd60e51b815260206004820152602c60248201526000805160206145a283398151915260448201526b19195b1959d85d1958d85b1b60a21b6064820152608490fd5b1561393157565b60405162461bcd60e51b815260206004820152602c60248201526000805160206145a283398151915260448201526b6163746976652070726f787960a01b6064820152608490fd5b9081526001600160a01b03909116602082015260400190565b60fe549192916001600160a01b03908116908115613cc357604094855193636819471960e01b85526020948581806139ce868660048401613979565b0381885afa60009181613c8c575b50613a1f5750505050507f4661696c656420746f20636865636b2070617274696369706174696f6e0000006000935191613a158361317c565b601d835282015290565b15613c5b57865163120aa87760e01b8152858180613a41868660048401613979565b0381885afa60009181613c24575b50613a915750505050507b4661696c656420746f20636865636b20636c61696d2073746174757360201b6000935191613a878361317c565b601c835282015290565b613bf5576000906024885180968193636d8a74cb60e01b835260048301525afa6000938491829181613bcc575b50613afb575050505050764661696c656420746f2067657420726f6f6d20696e666f60481b6000935191613af18361317c565b6017835282015290565b15613b995715613b34575050506f10d85b8818db185a5b481c995dd85c9960821b6001935191613b2a8361317c565b6010835282015290565b8116911614600014613b77577a10dc99585d1bdc8818d85b9b9bdd0818db185a5b481c99599d5b99602a1b6000935191613b6d8361317c565b601b835282015290565b6f10d85b8818db185a5b481c99599d5b9960821b6001935191613b2a8361317c565b5050505072149bdbdb481b9bdd0818db1bdcd959081e595d606a1b6000935191613bc28361317c565b6013835282015290565b91509450613be591503d806000833e611a64818361321f565b5094979194509250905038613abe565b505050506e105b1c9958591e4818db185a5b5959608a1b6000935191613c1a8361317c565b600f835282015290565b9091508681813d8311613c54575b613c3c818361321f565b8101031261032057613c4d9061334d565b9038613a4f565b503d613c32565b5050505070139bdd0818481c185c9d1a58da5c185b9d607a1b6000935191613c828361317c565b6011835282015290565b9091508681813d8311613cbc575b613ca4818361321f565b8101031261032057613cb59061334d565b90386139dc565b503d613c9a565b5050509050600090604051613cd78161317c565b601d81527f47726f7570427579526f6f6d20636f6e7472616374206e6f7420736574000000602082015290565b60fd5460fe54610103549060405191602083019360018060601b03199283809260601b16865260601b16603484015260601b1660488201526001605c820152605c8152613d50816131b2565b51902090565b919082039182116133cf57565b60408051613d70816131cd565b600090818152602092828483015282818301528260608301528260808301528260e060a09382858201528260c0820152015260018060a01b0360ff5416838115613f8957508151632c4c58b960e11b8152908382600481845afa85928687908389938a95613f39575b50613e1f57505050505050908282816006945196613df6886131cd565b818852870152850152826060850152826080850152830152620f424060c083015260e082015290565b6006999896919293949795999689620f42409984516391d885e760e01b81528281600481855afa849181613f03575b50613efa575b508451632ce2f3b160e01b8152929190839060049082905afa90918282613ec8575b5050613ec0575b50613e9183613e8c838d6133c2565b613d56565b9882519a613e9e8c6131cd565b8b528a01528801526060870152608086015284015260c083015260e082015290565b975038613e7d565b909192508b82813d8311613ef3575b613ee1818361321f565b8101031261357e575051903880613e76565b503d613ed7565b99506004613e54565b809250849193943d8311613f32575b613f1c818361321f565b81010312613f2e57518c929138613e4e565b8380fd5b503d613f12565b965093505050508483813d8311613f82575b613f55818361321f565b81010312613f7e5782518784015193858101516080606083015192015192959091929338613dd9565b8580fd5b503d613f4b565b90508181600694935196613df6886131cd565b8115613fa6570490565b634e487b7160e01b600052601260045260246000fd5b613fc4613294565b50610104546001600160a01b03168015614144576000918291839184916040906040519263e0f6ef8760e01b8452600490602085600481865afa89958161410c575b5061403e575b5050505050828015156000146140345761402591613f9c565b915b6040519361346f856131b2565b5050600091614027565b90919293809850978181116000146141045750925b60015b84811161400c5781516303d6605960e51b8152838101829052600081602481885afa60009181839184936140db575b5061409c575b505050614097906133e5565b614056565b6140a7575b8061408b565b966140cd6140c761409794996140c16140d3959c9d6133e5565b9c6133c2565b996141ea565b906133c2565b9590386140a1565b9150926140f292503d8091833e613867818361321f565b95509695505091505092909138614085565b905092614053565b9095506020813d60201161413c575b816141286020938361321f565b8101031261413857519438614006565b8980fd5b3d915061411b565b5050604051613598816131b2565b602a604051697072655570677261646560b01b81526001600a820152205480156141825761417e613d04565b1490565b50600190565b6040519061419582613197565b60006080838281528260208201528260408201528260608201520152565b60fd546001600160a01b03908116156141e4578060fe5416156141e4576101035416156141df57600190565b600090565b50600090565b80156141e45766038d7ea4c68000811161421257620f424080821161420d575090565b900490565b64e8d4a51000900490565b6035546001600160a01b0316330361423157565b606460405162461bcd60e51b815260206004820152602060248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e65726044820152fd5b1561427c57565b60405162461bcd60e51b815260206004820152602b60248201527f496e697469616c697a61626c653a20636f6e7472616374206973206e6f74206960448201526a6e697469616c697a696e6760a81b6064820152608490fd5b603580546001600160a01b039283166001600160a01b0319821681179092559091166000805160206145e2833981519152600080a3565b610101546000919082906001600160a01b0390811680158015614447575b6144425781610100541690604092604051630ba5260960e21b8152878160048181975afa889181614403575b5061436357505050505050565b875b81518110156143f85782614379828461369e565b51875163d4cc7c4360e01b815291168682015260208082602481895afa9182918c936143c4575b50506143b0575b50600101614365565b6143bd90600192986133c2565b96906143a7565b9080929350813d83116143f1575b6143dc818361321f565b810103126143ed57519038806143a0565b8a80fd5b503d6143d2565b509496505050505050565b9091503d808a833e614415818361321f565b81016020828203126141385781516001600160401b0381116143ed5761443b92016132e4565b9038614356565b505050565b508161010054161561432a565b803b15614489576000805160206145c283398151915280546001600160a01b0319166001600160a01b03909216919091179055565b60405162461bcd60e51b815260206004820152602d60248201527f455243313936373a206e657720696d706c656d656e746174696f6e206973206e60448201526c1bdd08184818dbdb9d1c9858dd609a1b6064820152608490fd5b9192901561454657508151156144f8575090565b3b156145015790565b60405162461bcd60e51b815260206004820152601d60248201527f416464726573733a2063616c6c20746f206e6f6e2d636f6e74726163740000006044820152606490fd5b8251909150156145595750805190602001fd5b60405162461bcd60e51b81526020600482015290819061457d906024830190613065565b0390fdfe4910fdfa16fed3260ed0e7147f7cc6da11a60208b5b9406d12a635614ffd914346756e6374696f6e206d7573742062652063616c6c6564207468726f75676820360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05940018b445cdbcdb9244b1e1aa23d872a1318cebbb9c0c20eacad866bdd97b5bc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3ba49807205ce4d355092ef5a8a18f56e8913cf4a201fbe287825b095693c2177545524331393637557067726164653a20756e737570706f727465642070726f78fd1f977f200838b1578002d86ef8a4669471790b72095ae2fff58baa6c01f81945524331393637557067726164653a206e657720696d706c656d656e74617469a264697066735822122085b804a950319548ba6429a87d0d91cc755194d0abdfb018e083ec8bdad6e2c664736f6c63430008160033", "deployedBytecode": "0x60e0604052600436101561001257600080fd5b60003560e01c80620e81771461302457806306007c1514612efe5780630cdc002d14612e3657806311c6702814612d6f5780631bf57d6614612d365780633587647614612b325780633659cfe6146128495780633b4d2cfb146128305780633da846251461280757806344287e25146127de5780634a5d5f9c14611c155780634c167fb0146127b45780634f1ef2861461241e57806352d1902d1461235f57806358536bdd146123355780635c975abb146123125780636c6ae0e7146122a95780636f7d7b8914612262578063715018a61461221757806375b238fc146121ee578063765fdaf1146121cb57806379f43065146121605780637ab8bb45146121155780637ac5234d146120eb5780638438eae8146120d05780638a13f9e9146120415780638da5cb5b146120185780638e61c85514611e7f57806394186ffd14611c31578063a19f05fc14611c15578063a886990614611bec578063aaea935914611bc2578063acdc674e14611b9f578063aededd1114611b32578063bb20764414611b09578063c123cf8f14611769578063d33219b41461173f578063ef49eb66146104f2578063f2fde38b14610463578063f6ebc190146103b9578063f99a38c4146103945763fa363cf0146101e957600080fd5b3461032057600036600319011261032057610202614188565b506101005460405163cce500eb60e01b81526001600160a01b03909116906020908181600481865afa90811561032c57600091610367575b506040516311de0b0160e21b8152928284600481845afa93841561032c57600094610338575b50928260049460405195868092638f16e1cd60e01b82525afa90811561032c576000916102f7575b60a0945061029461430c565b9383156102ef576102a58486613f9c565b935b60806040516102b581613197565b8781528381019283526040810194855260608101958652019485526040519586525190850152516040840152516060830152516080820152f35b6000936102a7565b90508284813d8311610325575b61030e818361321f565b810103126103205760a0935190610288565b600080fd5b503d610304565b6040513d6000823e3d90fd5b93508284813d8311610360575b61034f818361321f565b810103126103205792519282610260565b503d610345565b90508181813d831161038d575b61037e818361321f565b8101031261032057513861023a565b503d610374565b346103205760003660031901126103205760206103af6141b3565b6040519015158152f35b346103205760c0366003190112610320576103d261308a565b6103da6130a0565b906103e36130b6565b6103eb6130cc565b6103f36130e2565b906103fc6130f8565b9461040561421d565b60018060a01b03938480928160018060a01b031998168860fd54161760fd55168660fe54161760fe55168460ff54161760ff558261010091168482541617905581610101911683825416179055610102921690825416179055600080f35b346103205760203660031901126103205761047c61308a565b61048461421d565b6001600160a01b0381161561049e5761049c906142d5565b005b60405162461bcd60e51b815260206004820152602660248201527f4f776e61626c653a206e6577206f776e657220697320746865207a65726f206160448201526564647265737360d01b6064820152608490fd5b346103205760203660031901126103205761050b61308a565b604051906105188261310e565b6000825260405161052881613129565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c0820152600060e08201526000610100820152602083015260405161057681613145565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c082015260408301526040516105b581613160565b6000815260006020820152600060408201526000606082015260006080820152600060a0820152600060c0820152600060e08201526000610100820152600061012082015260006101408201526000610160820152600061018082015260006101a082015260006101c082015260006101e08201526060610200820152606083015261063f614188565b60808301526040516106508161317c565b6000808252602082015260a08301526001600160a01b03818116835260fd541680611467575b5060ff546001600160a01b0316806111c1575b50610100546001600160a01b031680610abc575b50610101546001600160a01b0316806109de575b50610102546001600160a01b0316806108de575b8260405180916020825260018060a01b038151166020830152610100602082015160018060a01b03815116604085015260ff602082015116606085015260408101516080850152606081015160a08501526080810151151560c085015260a081015160e085015260c081015115158285015260e081015115156101208501520151151561014083015260c060408201518051610160850152602081015161018085015260408101516101a0850152606081015115156101c0850152608081015115156101e085015260a081015115156102008501520151610220830152602060a061088d610200606085015161032061024088015280511515610340880152848101516103608801526040810151610380880152606081015115156103a0880152608081015115156103c0880152838101516103e088015260c081015161040088015260e081015115156104208801526101008101516104408801526101208101516104608801526101408101516104808801526101608101516104a08801526101808101516104c08801526101a08101516104e08801526101c08101516105008801526101e081015115156105208801520151610220610540870152610560860190613065565b9260808082015180516102608801528481015161028088015260408101516102a088015260608101516102c0880152015115156102e086015201518051610300850152015115156103208301520390f35b60405163a32bf59760e01b815291602083600481855afa600093816109aa575b5061090a575b506106c5565b602061092f9160009360405180809581946351a1cc5160e11b83528960048401613979565b03915afa6000918161096e575b50610966575b50604051916109508361317c565b82521515602082015260a0820152818080610904565b905083610942565b9091506020813d6020116109a2575b8161098a6020938361321f565b810103126103205761099b9061334d565b908561093c565b3d915061097d565b9093506020813d6020116109d6575b816109c66020938361321f565b81010312610320575192856108fe565b3d91506109b9565b6040516318e5938160e11b81526001600160a01b038316600482015290608090829060249082905afa9060009160009181600091600093610a70575b50610a28575b5050506106b1565b814210159283610a66575b60405194610a4086613197565b855260208501526040840152606083015215156080820152608083015282808080610a20565b8115159350610a33565b9350935050506080813d608011610ab4575b81610a8f6080938361321f565b8101031261032057805160208201516040830151606090930151919390929087610a1a565b3d9150610a82565b604051637dbd1d2760e01b81526001600160a01b0383166004820152600081602481855afa60009181611018575b50610e0f5750604051639407af1f60e01b81526001600160a01b0383166004820152602081602481855afa60009181610dd3575b50610b2d575b50505b8261069d565b6040516306052ccb60e41b81526001600160a01b038416600482015260009283928392839290602081602481855afa60009181610d9f575b50610d97575b506040516311de0b0160e21b8152602081600481855afa60009181610d63575b50610d5b575b5060405163cce500eb60e01b8152602081600481855afa60009181610d27575b50610d1e575b50602060049160405192838092638f16e1cd60e01b82525afa60009181610cea575b50610ce2575b508080610cd2575b1594859485610cc8575b60405196610bfe88613160565b15875281602088015260408701526201518042041460608601521515608085015260a084015260c0830152151560e08201526000610100820152600061012082015260006101408201526000610160820152600061018082015260006101a082015260006101c082015260006101e0820152604051610c7c81613204565b602781527f46616c6c6261636b206d6f6465202d206e6f2061637469766174696f6e2074696020820152666d65206461746160c81b604082015261020082015260608301528280610b24565b8484109550610bf1565b6201518042048614159150610be7565b925087610bdf565b9091506020813d602011610d16575b81610d066020938361321f565b8101031261032057519089610bd9565b3d9150610cf9565b92506020610bb7565b9091506020813d602011610d53575b81610d436020938361321f565b810103126103205751908a610bb1565b3d9150610d36565b945088610b91565b9091506020813d602011610d8f575b81610d7f6020938361321f565b810103126103205751908a610b8b565b3d9150610d72565b955088610b6b565b9091506020813d602011610dcb575b81610dbb6020938361321f565b810103126103205751908a610b65565b3d9150610dae565b9091506020813d602011610e07575b81610def6020938361321f565b8101031261032057610e009061334d565b9085610b1e565b3d9150610de2565b809160006004602060009360405192838092635d1749f160e11b82525afa60009181610fe1575b50610fd9575b506101015483906001600160a01b031680610f5f575b50808080808080808080806102606102406102206102006101e06101c060c061014061010060e060a06080606060208e015115159d6040810151885201519d015115159d015115159d01519d01519d015115159d01519d01519d01519d01519d01519f015115159f015160805260405160a052610ed060a051613160565b60a0515260c051602060a0510152604060a0510152606060a0510152608060a051015260a08051015260c060a051015260e060a051015261010060a051015261012060a051015261014060a051015261016060a051015261018060a05101526101a060a05101526101c060a05101526101e060a051015260805161020060a051015260a0516060830152610b27565b60405163d4cc7c4360e01b81526001600160a01b03881660048201529150602090829060249082905afa60009181610fa3575b5090849115610e5257925080610e52565b915093506020813d602011610fd1575b81610fc06020938361321f565b810103126103205751849384610f92565b3d9150610fb3565b905086610e3c565b945090506020843d602011611010575b81610ffe6020938361321f565b81010312610320578493519088610e36565b3d9150610ff1565b9091503d806000833e61102b818361321f565b6020828281010312610320578151916001600160401b03831161032057610280838201838301031261032057604051926001600160401b036102808501908111908511176111ab5761028084016040526110868183016132b9565b845261109660208284010161334d565b602085015281810160408181015190860152606080820151908601526110be9060800161334d565b60808501526110d160a08284010161334d565b60a085015281810160c0818101519086015260e0808201519086015261010080820151908601526101208082015190860152611110906101400161334d565b610140850152818101610160818101519086015261018080820151908601526101a080820151908601526101c080820151908601526101e08082015190860152610200808201519086015261022080820151908601526102409061117590820161334d565b908501528181016102600151906001600160401b0382116103205761119e9383019201016135da565b6102608201529085610aea565b634e487b7160e01b600052604160045260246000fd5b60405163198a695d60e21b81526001600160a01b038316600482015290602082602481845afa60009281611433575b506111fc575b50610689565b60405163978f7ae760e01b81526001600160a01b03841660048201526000928392839283929190602081602481855afa600091816113ff575b506113f7575b50604051630d06f67f60e01b81526001600160a01b0388166004820152602081602481855afa600091816113c3575b506113bb575b50604051632ce2f3b160e01b8152602081600481855afa60009181611387575b5061137e575b5060206004916040519283809263fd8b5f5b60e01b82525afa6000918161134a575b50611342575b5060009385151580611330575b611312575b50604051946112de86613145565b81865282602087015260408601521515606085015210156080830152801560a083015260c0820152604083015282806111f6565b61132991945061132290866133c2565b4290613d56565b92876112d0565b5061133b81876133c2565b42106112cb565b9350876112be565b9091506020813d602011611376575b816113666020938361321f565b81010312610320575190896112b8565b3d9150611359565b93506020611296565b9091506020813d6020116113b3575b816113a36020938361321f565b810103126103205751908a611290565b3d9150611396565b955088611270565b9091506020813d6020116113ef575b816113df6020938361321f565b810103126103205751908a61126a565b3d91506113d2565b92508861123b565b9091506020813d60201161142b575b8161141b6020938361321f565b810103126103205751908a611235565b3d915061140e565b9092506020813d60201161145f575b8161144f6020938361321f565b81010312610320575191856111f0565b3d9150611442565b604051636386c1c760e01b81526001600160a01b038316600482015260c081602481855afa600091600080600091846000946000966116cc575b506114b2575b505050505050610676565b60ff90604051966114c288613129565b60018060a01b03168752166020860152604085015260608401521515608083015260a0820152600060c0820152600060e082015260006101008201526020840152604051632474521560e21b8152600080516020614642833981519152600482015260018060a01b0383166024820152602081604481855afa60009181611690575b5061167d575b50604051632474521560e21b815260008051602061460283398151915260048201526001600160a01b0383166024820152602081604481855afa6000918161163a575b5091602091600493611628575b5060405163a239e33d60e01b815292839182905afa600091816115ec575b506115c9575b8080808080806114a7565b60208301516001600160a01b0383811692169190911461010090910152826115be565b9091506020813d602011611620575b816116086020938361321f565b8101031261032057611619906132b9565b90846115b8565b3d91506115fb565b60e0838701510190151590528561159a565b9150916020823d602011611675575b816116566020938361321f565b810103126103205760049261166c60209361334d565b9250909261158d565b3d9150611649565b60c060208501510190151590528361154a565b9091506020813d6020116116c4575b816116ac6020938361321f565b81010312610320576116bd9061334d565b9085611544565b3d915061169f565b965094505050505060c0823d60c011611737575b816116ed60c0938361321f565b81010312610320576116fe826132b9565b60208301519260ff8416840361032057604081015160608201519060a06117276080850161334d565b930151939590919293948a6114a1565b3d91506116e0565b3461032057600036600319011261032057610103546040516001600160a01b039091168152602090f35b34610320576040366003190112610320576117826130a0565b60405161018081016001600160401b038111828210176111ab57604052600060408201526000606082015260006080820152600060a0820152600060c0820152600060e0820152600061010082015260006101208201526000610140820152600061016082015260018060a01b0382168152600435602082015260018060a01b0360fe5416806118b1575b610180826040519060018060a01b0381511682526020810151602083015260408101511515604083015260608101511515606083015260808101511515608083015260a0810151151560a083015260c0810151151560c083015260e0810151151560e08301526101008101516101008301526101208101511515610120830152610140810151151561014083015261016060018060a01b0391015116610160820152f35b604051636819471960e01b815290602082806118d38760048035908401613979565b0381845afa60009281611acd575b506118ed575b5061180d565b811515604084015260405163120aa87760e01b8152602081806119168860043560048401613979565b0381855afa60009181611a8a575b5091600091602493611a7d575b5060405192838092636d8a74cb60e01b825260043560048301525afa600091829182918291829181611a49575b5061196d575b505050506118e7565b6001600160a01b03908116888216908114608089015286151560a0890152911690811460c087015282151561012087015283151561014087015261016086015260005b8151811015611a3d576001600160a01b03878116906119cf838561369e565b5116146119de576001016119b0565b6101008601525061018094505b80611a31575b6119ff575b80808080611964565b15611a1357151560e08201525b82806119f6565b80611a25575b151560e0820152611a0c565b50608081015115611a19565b506060830151156119f1565b505061018094506119eb565b935094505050611a6c91503d806000833e611a64818361321f565b81019061335a565b50919590949293509091908961195e565b1515606086015286611931565b9150916020823d602011611ac5575b81611aa66020938361321f565b8101031261032057602492611abc60009361334d565b92509092611924565b3d9150611a99565b9092506020813d602011611b01575b81611ae96020938361321f565b8101031261032057611afa9061334d565b91856118e1565b3d9150611adc565b346103205760003660031901126103205760ff546040516001600160a01b039091168152602090f35b34610320576020366003190112610320576004356001600160401b038111610320573660238201121561032057611b8c6020611b798193369060248160040135910161325d565b8160405193828580945193849201613042565b8101600181520301902054604051908152f35b3461032057600036600319011261032057602060ff600254166040519015158152f35b3461032057600036600319011261032057610100546040516001600160a01b039091168152602090f35b346103205760003660031901126103205760fd546040516001600160a01b039091168152602090f35b3461032057600036600319011261032057602060405160018152f35b34610320576020806003193601126103205760043590604051611c538161310e565b60008152600082820152600060408201526060926000606083015260809360006080840152600060a080940152611c8861430c565b60fe546040516357aa9bfd60e11b81529195600094859491936001600160a01b039385908290600490829088165afa90811561032c57600091611e52575b5081811115611e4a5750915b61010154811660005b848110611d4e57505050505060c09550611cf583866133c2565b9060405190611d038261310e565b86825280820194855285604083019260008452606081019485526080810195865201946000865260405197885251908701525160408601525160608501525160808401525190820152f35b604051636790d2b560e01b8152600481018290528981602481865afa9060008560009184600094600096611ded575b50611d90575b5050505050600101611cdb565b16151580611de4575b611da5575b8581611d83565b611db091929a6133c2565b98159081611ddb575b50611dc7575b8b8080611d9e565b95611dd36001916133e5565b969050611dbf565b9050158c611db9565b50801515611d99565b945094505050508a81813d8311611e43575b611e09818361321f565b81010312610320578085611e1d8f936132b9565b92611e388b84015191611e318b860161334d565b940161334d565b939092939438611d7d565b503d611dff565b905091611cd2565b90508481813d8311611e78575b611e69818361321f565b8101031261032057518a611cc6565b503d611e5f565b346103205760003660031901126103205760405161016081016001600160401b038111828210176111ab57604052600a815260005b61014081106120075750600090611ec96141b3565b15611fb3575b611ed8826132cd565b90611ee6604051928361321f565b828252601f19611ef5846132cd565b0160005b818110611fa257505060005b838110611f7857505060405190604082019215825260206040602084015281518094526060830193602060608260051b8601019301916000955b828710611f4c5785850386f35b909192938280611f68600193605f198a82030186528851613065565b9601920196019592919092611f3f565b80611f856001928461369e565b51611f90828661369e565b52611f9b818561369e565b5001611f05565b806060602080938701015201611ef9565b9050604051611fc18161317c565b602081527f53746f72616765206c61796f75742076616c69646174696f6e206661696c65646020820152611ff482613691565b52611ffe81613691565b50600190611ecf565b806060602080938501015201611eb4565b34610320576000366003190112610320576035546040516001600160a01b039091168152602090f35b34610320576020366003190112610320577f943e9d45a11aaae5d87503e3bc248665d9807856e5cf2bdb4a988bee44422781608061207d61308a565b61208561421d565b60018060a01b03166101048160018060a01b031982541617905560405190604082526011604083015270141c9bd91d58dd13585b9859d95b595b9d607a1b60608301526020820152a1005b346103205760003660031901126103205760206103af614152565b3461032057600036600319011261032057610101546040516001600160a01b039091168152602090f35b34610320576020366003190112610320576080612133600435613fbc565b61215e6040518092606080918051845260208101516020850152604081015160408501520151910152565bf35b346103205760003660031901126103205761010061217c613d63565b60e060405191805183526020810151602084015260408101516040840152606081015160608401526080810151608084015260a081015160a084015260c081015160c0840152015160e0820152f35b346103205760003660031901126103205760206121e6613d04565b604051908152f35b346103205760003660031901126103205760206040516000805160206146428339815191528152f35b346103205760003660031901126103205761223061421d565b603580546001600160a01b031981169091556000906001600160a01b03166000805160206145e28339815191528280a3005b346103205760403660031901126103205761228661227e6130a0565b600435613992565b906122a560405192839215158352604060208401526040830190613065565b0390f35b34610320576000366003190112610320576122c261421d565b6122ca6141b3565b156122d157005b60405162461bcd60e51b815260206004820152601960248201527814dd1bdc9859d9481d985b1a59185d1a5bdb8819985a5b1959603a1b6044820152606490fd5b3461032057600036600319011261032057602060ff606754166040519015158152f35b3461032057600036600319011261032057610102546040516001600160a01b039091168152602090f35b34610320576000366003190112610320577f00000000000000000000000000000000000000000000000000000000000000006001600160a01b031630036123b85760206040516000805160206145c28339815191528152f35b60405162461bcd60e51b815260206004820152603860248201527f555550535570677261646561626c653a206d757374206e6f742062652063616c6044820152771b1959081d1a1c9bdd59da0819195b1959d85d1958d85b1b60421b6064820152608490fd5b60403660031901126103205761243261308a565b60249081356001600160401b038111610320573660238201121561032057612463903690848160040135910161325d565b6001600160a01b037f00000000000000000000000000000000000000000000000000000000000000008116919061249c308414156138db565b6124b96000805160206145c283398151915293828554161461392a565b8316801561277757833b156127355760ff60025416156126ea575b6124dc613d04565b602a604051697072655570677261646560b01b81526001600a8201522055604051924284528160008051602061468283398151915260208096a26000805160206145828339815191525460ff161561253c575050505061049c9150614454565b6040516352d1902d60e01b81529394938581600481865afa600091816126bb575b506125ab5760405162461bcd60e51b815260048101879052602e818901526000805160206146a283398151915260448201526d6f6e206973206e6f74205555505360901b6064820152608490fd5b9591929493950361267857506125c084614454565b600080516020614622833981519152600080a2815115801590612670575b6125e457005b61049c92600080604051946125f886613204565b602786527f416464726573733a206c6f772d6c6576656c2064656c65676174652063616c6c85870152660819985a5b195960ca1b60408701528481519101845af4903d15612667573d61264a81613242565b90612658604051928361321f565b8152600081943d92013e6144e4565b606092506144e4565b5060016125de565b8260296084926040519262461bcd60e51b845260048401528201526000805160206146628339815191526044820152681a58589b195555525160ba1b6064820152fd5b9091508681813d83116126e3575b6126d3818361321f565b810103126103205751908861255d565b503d6126c9565b6126f26141b3565b6124d45760405162461bcd60e51b8152602060048201526018818701527714dd1bdc9859d9481b185e5bdd5d0818dbdc9c9d5c1d195960421b6044820152606490fd5b60405162461bcd60e51b815260206004820152601b818701527a125b5c1b195b595b9d185d1a5bdb881b9bdd0819195c1b1bde5959602a1b6044820152606490fd5b60405162461bcd60e51b8152602060048201526016818701527524b73b30b634b21034b6b83632b6b2b73a30ba34b7b760511b6044820152606490fd5b3461032057600036600319011261032057610104546040516001600160a01b039091168152602090f35b346103205760003660031901126103205760206040516000805160206146028339815191528152f35b346103205760003660031901126103205760fe546040516001600160a01b039091168152602090f35b346103205760003660031901126103205761049c61421d565b34610320576020806003193601126103205761286361308a565b906001600160a01b037f000000000000000000000000000000000000000000000000000000000000000081169061289c308314156138db565b6128b96000805160206145c283398151915292828454161461392a565b83168015612af457833b15612ab15760ff6002541615612a65575b6128dc613d04565b602a604051697072655570677261646560b01b81526001600a82015220558060008051602061468283398151915284604051428152a26040519161291f836131e9565b600083526000805160206145828339815191525460ff1615612948575050505061049c90614454565b6040516352d1902d60e01b81528481600481865afa60009181612a36575b506129b55760405162461bcd60e51b815260048101869052602e60248201526000805160206146a283398151915260448201526d6f6e206973206e6f74205555505360901b6064820152608490fd5b939293036129f1576129c684614454565b600080516020614622833981519152600080a28151158015906129e9576125e457005b5060006125de565b60405162461bcd60e51b815260048101839052602960248201526000805160206146628339815191526044820152681a58589b195555525160ba1b6064820152608490fd5b9091508581813d8311612a5e575b612a4e818361321f565b8101031261032057519087612966565b503d612a44565b612a6d6141b3565b6128d45760405162461bcd60e51b815260048101849052601860248201527714dd1bdc9859d9481b185e5bdd5d0818dbdc9c9d5c1d195960421b6044820152606490fd5b60405162461bcd60e51b815260048101849052601b60248201527a125b5c1b195b595b9d185d1a5bdb881b9bdd0819195c1b1bde5959602a1b6044820152606490fd5b60405162461bcd60e51b815260048101849052601660248201527524b73b30b634b21034b6b83632b6b2b73a30ba34b7b760511b6044820152606490fd5b346103205760e036600319011261032057612b4b61308a565b612b536130a0565b90612b5c6130b6565b90612b656130cc565b612b6d6130e2565b90612b766130f8565b9060c4359260018060a01b03918285168095036103205760005460ff98898260081c16159889809a612d2a575b8015612d14575b15612cb857858092612c158c8e9660ff1991600183831617600055612ca6575b50612be48760005460081c16612bdf81614275565b614275565b612bed336142d5565b6000549e8f60081c1690612c0082614275565b612c0982614275565b60675416606755614275565b8160018060a01b03199b168b60fd54161760fd55168960fe54161760fe551687825416179055826101009116868254161790558161010191168582541617905561010291168382541617905561010491825416179055612c7157005b61ff0019166000557f7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498602060405160018152a1005b61ffff1916610101176000558f612bca565b60405162461bcd60e51b815260206004820152602e60248201527f496e697469616c697a61626c653a20636f6e747261637420697320616c72656160448201526d191e481a5b9a5d1a585b1a5e995960921b6064820152608490fd5b50303b158015612baa575060018b841614612baa565b5060018b841610612ba3565b346103205760203660031901126103205760043580151580910361032057612d5c61421d565b60ff801960025416911617600255600080f35b3461032057602036600319011261032057612d8861308a565b612d9061421d565b610103805490916001600160a01b0390818316612df25716908115612dbe576001600160a01b031916179055005b60405162461bcd60e51b815260206004820152600c60248201526b5a65726f206164647265737360a01b6044820152606490fd5b60405162461bcd60e51b815260206004820152601c60248201527b151a5b595b1bd8dac8185b1c9958591e481a5b9a5d1a585b1a5e995960221b6044820152606490fd5b3461032057600036600319011261032057612e4f61421d565b60ff6067541615612ec25760405160208152602360208201527f53696d706c655175657279436f6e74726163742073746f7261676520636865636040820152621ad95960ea1b60608201527f4b7382446c9963a3df8a63f09f9dfb8d0fbca2d60f96266274d3e82eec99ca2b60803092a2005b60405162461bcd60e51b815260206004820152601460248201527314185d5cd8589b194e881b9bdd081c185d5cd95960621b6044820152606490fd5b3461032057602080600319360112610320576004356001600160401b038082116103205736602383011215610320578160040135908111610320573660248260051b84010111610320576024612f5492016136b2565b6040519082820192808352815180945260408301938160408260051b8601019301916000955b828710612f875785850386f35b909192938280600192603f19898203018552875190612fdf612fcc61012084518452878060a01b03868601511686850152604085015190806040860152840190613065565b6060808501519084830390850152613065565b916080808201519083015260a0808201519083015260c0808201519083015260e0810151151560e0830152610100809101519101529601920196019592919092612f7a565b346103205760203660031901126103205760806121336004356133f4565b60005b8381106130555750506000910152565b8181015183820152602001613045565b9060209161307e81518092818552858086019101613042565b601f01601f1916010190565b600435906001600160a01b038216820361032057565b602435906001600160a01b038216820361032057565b604435906001600160a01b038216820361032057565b606435906001600160a01b038216820361032057565b608435906001600160a01b038216820361032057565b60a435906001600160a01b038216820361032057565b60c081019081106001600160401b038211176111ab57604052565b61012081019081106001600160401b038211176111ab57604052565b60e081019081106001600160401b038211176111ab57604052565b61022081019081106001600160401b038211176111ab57604052565b604081019081106001600160401b038211176111ab57604052565b60a081019081106001600160401b038211176111ab57604052565b608081019081106001600160401b038211176111ab57604052565b61010081019081106001600160401b038211176111ab57604052565b602081019081106001600160401b038211176111ab57604052565b606081019081106001600160401b038211176111ab57604052565b601f909101601f19168101906001600160401b038211908210176111ab57604052565b6001600160401b0381116111ab57601f01601f191660200190565b92919261326982613242565b91613277604051938461321f565b829481845281830111610320578281602093846000960137010152565b604051906132a1826131b2565b60006060838281528260208201528260408201520152565b51906001600160a01b038216820361032057565b6001600160401b0381116111ab5760051b60200190565b9080601f83011215610320578151906020916132ff816132cd565b9361330d604051958661321f565b81855260208086019260051b82010192831161032057602001905b828210613336575050505090565b838091613342846132b9565b815201910190613328565b5190811515820361032057565b9160e0838303126103205761336e836132b9565b9260208101519260408201519060018060401b038211610320576133939183016132e4565b916133a06060830161334d565b916133ad6080820161334d565b9160c06133bc60a084016132b9565b92015190565b919082018092116133cf57565b634e487b7160e01b600052601160045260246000fd5b60001981146133cf5760010190565b6133fc613294565b5060fe546001600160a01b039081169190821561358957604092604051936357aa9bfd60e11b8552600492602086600481865afa95861561032c57600096613552575b506000928394849680891160001461354a57935b855b858110613483575050505050506040519361346f856131b2565b845260208401526040830152606082015290565b8151636d8a74cb60e01b8152838101829052600081602481885afa9060008781848194829661351f575b506134c0575b5050505050600101613455565b1615159081613515575b506134d8575b8087816134b3565b996134e8919a99929951906133c2565b9815613502576134f96001916133e5565b975b90386134d0565b969561350f6001916133e5565b966134fb565b90501515386134ca565b929450945061353892503d8091833e611a64818361321f565b505094929193905090929394386134ad565b508793613453565b90956020823d602011613581575b8161356d6020938361321f565b8101031261357e575051943861343f565b80fd5b3d9150613560565b505050604051613598816131b2565b6000815260006020820152600060408201526000606082015290565b91908110156135c45760051b0190565b634e487b7160e01b600052603260045260246000fd5b81601f820112156103205780516135f081613242565b926135fe604051948561321f565b818452602082840101116103205761361c9160208085019101613042565b90565b90610100828203126103205781519261363a602084016132b9565b60408401519093906001600160401b0390818111610320578461365e9184016135da565b936060830151918211610320576136769183016135da565b9160808201519160a08101519160e06133bc60c0840161334d565b8051156135c45760200190565b80518210156135c45760209160051b010190565b9190916136be836132cd565b9060406136cd8151938461321f565b848352601f196136dc866132cd565b0160005b818110613888575050610104546001600160a01b0390811695909190861561387f5760005b818110613716575093955050505050565b8061372460019284886135b4565b84516303d6605960e51b8152903560048201526000816024818d5afa6000918283908485908693868896899861384a575b506137eb57505050505050505061376d8184886135b4565b3584519061377a82613129565b815260006020820152845161378e816131e9565b600081528582015284516137a1816131e9565b60008152606082015260006080820152600060a0820152600060c0820152600060e082015260006101008201526137d8828961369e565b526137e3818861369e565b505b01613705565b8c6137f5846141ea565b948d51996138028b613129565b8a521660208901528b8801526060870152608086015260a085015260c0840152151560e0830152610100820152613839828961369e565b52613844818861369e565b506137e5565b96509794505050505061386f92503d8091833e613867818361321f565b81019061361f565b9697909591949193919038613755565b50929450505050565b602090835161389681613129565b6000815282600081830152606080878401528083015260006080830152600060a0830152600060c0830152600060e083015260006101008301528288010152016136e0565b156138e257565b60405162461bcd60e51b815260206004820152602c60248201526000805160206145a283398151915260448201526b19195b1959d85d1958d85b1b60a21b6064820152608490fd5b1561393157565b60405162461bcd60e51b815260206004820152602c60248201526000805160206145a283398151915260448201526b6163746976652070726f787960a01b6064820152608490fd5b9081526001600160a01b03909116602082015260400190565b60fe549192916001600160a01b03908116908115613cc357604094855193636819471960e01b85526020948581806139ce868660048401613979565b0381885afa60009181613c8c575b50613a1f5750505050507f4661696c656420746f20636865636b2070617274696369706174696f6e0000006000935191613a158361317c565b601d835282015290565b15613c5b57865163120aa87760e01b8152858180613a41868660048401613979565b0381885afa60009181613c24575b50613a915750505050507b4661696c656420746f20636865636b20636c61696d2073746174757360201b6000935191613a878361317c565b601c835282015290565b613bf5576000906024885180968193636d8a74cb60e01b835260048301525afa6000938491829181613bcc575b50613afb575050505050764661696c656420746f2067657420726f6f6d20696e666f60481b6000935191613af18361317c565b6017835282015290565b15613b995715613b34575050506f10d85b8818db185a5b481c995dd85c9960821b6001935191613b2a8361317c565b6010835282015290565b8116911614600014613b77577a10dc99585d1bdc8818d85b9b9bdd0818db185a5b481c99599d5b99602a1b6000935191613b6d8361317c565b601b835282015290565b6f10d85b8818db185a5b481c99599d5b9960821b6001935191613b2a8361317c565b5050505072149bdbdb481b9bdd0818db1bdcd959081e595d606a1b6000935191613bc28361317c565b6013835282015290565b91509450613be591503d806000833e611a64818361321f565b5094979194509250905038613abe565b505050506e105b1c9958591e4818db185a5b5959608a1b6000935191613c1a8361317c565b600f835282015290565b9091508681813d8311613c54575b613c3c818361321f565b8101031261032057613c4d9061334d565b9038613a4f565b503d613c32565b5050505070139bdd0818481c185c9d1a58da5c185b9d607a1b6000935191613c828361317c565b6011835282015290565b9091508681813d8311613cbc575b613ca4818361321f565b8101031261032057613cb59061334d565b90386139dc565b503d613c9a565b5050509050600090604051613cd78161317c565b601d81527f47726f7570427579526f6f6d20636f6e7472616374206e6f7420736574000000602082015290565b60fd5460fe54610103549060405191602083019360018060601b03199283809260601b16865260601b16603484015260601b1660488201526001605c820152605c8152613d50816131b2565b51902090565b919082039182116133cf57565b60408051613d70816131cd565b600090818152602092828483015282818301528260608301528260808301528260e060a09382858201528260c0820152015260018060a01b0360ff5416838115613f8957508151632c4c58b960e11b8152908382600481845afa85928687908389938a95613f39575b50613e1f57505050505050908282816006945196613df6886131cd565b818852870152850152826060850152826080850152830152620f424060c083015260e082015290565b6006999896919293949795999689620f42409984516391d885e760e01b81528281600481855afa849181613f03575b50613efa575b508451632ce2f3b160e01b8152929190839060049082905afa90918282613ec8575b5050613ec0575b50613e9183613e8c838d6133c2565b613d56565b9882519a613e9e8c6131cd565b8b528a01528801526060870152608086015284015260c083015260e082015290565b975038613e7d565b909192508b82813d8311613ef3575b613ee1818361321f565b8101031261357e575051903880613e76565b503d613ed7565b99506004613e54565b809250849193943d8311613f32575b613f1c818361321f565b81010312613f2e57518c929138613e4e565b8380fd5b503d613f12565b965093505050508483813d8311613f82575b613f55818361321f565b81010312613f7e5782518784015193858101516080606083015192015192959091929338613dd9565b8580fd5b503d613f4b565b90508181600694935196613df6886131cd565b8115613fa6570490565b634e487b7160e01b600052601260045260246000fd5b613fc4613294565b50610104546001600160a01b03168015614144576000918291839184916040906040519263e0f6ef8760e01b8452600490602085600481865afa89958161410c575b5061403e575b5050505050828015156000146140345761402591613f9c565b915b6040519361346f856131b2565b5050600091614027565b90919293809850978181116000146141045750925b60015b84811161400c5781516303d6605960e51b8152838101829052600081602481885afa60009181839184936140db575b5061409c575b505050614097906133e5565b614056565b6140a7575b8061408b565b966140cd6140c761409794996140c16140d3959c9d6133e5565b9c6133c2565b996141ea565b906133c2565b9590386140a1565b9150926140f292503d8091833e613867818361321f565b95509695505091505092909138614085565b905092614053565b9095506020813d60201161413c575b816141286020938361321f565b8101031261413857519438614006565b8980fd5b3d915061411b565b5050604051613598816131b2565b602a604051697072655570677261646560b01b81526001600a820152205480156141825761417e613d04565b1490565b50600190565b6040519061419582613197565b60006080838281528260208201528260408201528260608201520152565b60fd546001600160a01b03908116156141e4578060fe5416156141e4576101035416156141df57600190565b600090565b50600090565b80156141e45766038d7ea4c68000811161421257620f424080821161420d575090565b900490565b64e8d4a51000900490565b6035546001600160a01b0316330361423157565b606460405162461bcd60e51b815260206004820152602060248201527f4f776e61626c653a2063616c6c6572206973206e6f7420746865206f776e65726044820152fd5b1561427c57565b60405162461bcd60e51b815260206004820152602b60248201527f496e697469616c697a61626c653a20636f6e7472616374206973206e6f74206960448201526a6e697469616c697a696e6760a81b6064820152608490fd5b603580546001600160a01b039283166001600160a01b0319821681179092559091166000805160206145e2833981519152600080a3565b610101546000919082906001600160a01b0390811680158015614447575b6144425781610100541690604092604051630ba5260960e21b8152878160048181975afa889181614403575b5061436357505050505050565b875b81518110156143f85782614379828461369e565b51875163d4cc7c4360e01b815291168682015260208082602481895afa9182918c936143c4575b50506143b0575b50600101614365565b6143bd90600192986133c2565b96906143a7565b9080929350813d83116143f1575b6143dc818361321f565b810103126143ed57519038806143a0565b8a80fd5b503d6143d2565b509496505050505050565b9091503d808a833e614415818361321f565b81016020828203126141385781516001600160401b0381116143ed5761443b92016132e4565b9038614356565b505050565b508161010054161561432a565b803b15614489576000805160206145c283398151915280546001600160a01b0319166001600160a01b03909216919091179055565b60405162461bcd60e51b815260206004820152602d60248201527f455243313936373a206e657720696d706c656d656e746174696f6e206973206e60448201526c1bdd08184818dbdb9d1c9858dd609a1b6064820152608490fd5b9192901561454657508151156144f8575090565b3b156145015790565b60405162461bcd60e51b815260206004820152601d60248201527f416464726573733a2063616c6c20746f206e6f6e2d636f6e74726163740000006044820152606490fd5b8251909150156145595750805190602001fd5b60405162461bcd60e51b81526020600482015290819061457d906024830190613065565b0390fdfe4910fdfa16fed3260ed0e7147f7cc6da11a60208b5b9406d12a635614ffd914346756e6374696f6e206d7573742062652063616c6c6564207468726f75676820360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e05940018b445cdbcdb9244b1e1aa23d872a1318cebbb9c0c20eacad866bdd97b5bc7cd75a20ee27fd9adebab32041f755214dbc6bffa90cc0225b39da2e5c2d3ba49807205ce4d355092ef5a8a18f56e8913cf4a201fbe287825b095693c2177545524331393637557067726164653a20756e737570706f727465642070726f78fd1f977f200838b1578002d86ef8a4669471790b72095ae2fff58baa6c01f81945524331393637557067726164653a206e657720696d706c656d656e74617469a264697066735822122085b804a950319548ba6429a87d0d91cc755194d0abdfb018e083ec8bdad6e2c664736f6c63430008160033", "linkReferences": {}, "deployedLinkReferences": {}}