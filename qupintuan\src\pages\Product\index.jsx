import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { getProductCount, getProductInfo } from '@/apis/mallApi';
import { checkUserRegistered } from '@/apis/agentSystemApi';
import { fixPriceDisplay, needsPriceFix } from '@/utils/priceDisplayFix';
import SearchAndFilter from '@/components/Mall/SearchAndFilter';
import './ProductList.css';

// 商品图片组件 - 简化版本
function ProductImage({ imageUrl, alt }) {
  const [hasError, setHasError] = useState(false)

  if (!imageUrl) {
    return (
      <div className="placeholder-image">
        <div className="placeholder-icon">📦</div>
        <div className="placeholder-text">暂无图片</div>
      </div>
    )
  }

  // 构建图片URL
  let imageSrc = imageUrl
  if (!imageUrl.startsWith('http')) {
    // 如果是IPFS哈希，使用Pinata网关
    imageSrc = `https://gateway.pinata.cloud/ipfs/${imageUrl}`
  }

  const handleError = (e) => {
    // 尝试备用网关
    if (e.target.src.includes('pinata')) {
      const backupUrl = `https://dweb.link/ipfs/${imageUrl}`
      e.target.src = backupUrl
    } else {
      setHasError(true)
    }
  }

  const handleLoad = () => {
    setHasError(false)
  }

  if (hasError) {
    return (
      <div className="placeholder-image">
        <div className="placeholder-icon">📦</div>
        <div className="placeholder-text">图片加载失败</div>
      </div>
    )
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      onLoad={handleLoad}
      onError={handleError}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover'
      }}
    />
  )
}

export default function ProductList() {
  const { address: account } = useAccount();

  // 状态管理
  const [products, setProducts] = useState([]);
  const [allProducts, setAllProducts] = useState([]); // 存储所有商品用于筛选
  const [isLoading, setIsLoading] = useState(true);
  const [isRegisteredUser, setIsRegisteredUser] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);

  // 搜索和筛选状态
  const [filters, setFilters] = useState({
    searchTerm: '',
    category: 'all',
    priceRange: 'all',
    sortBy: 'newest'
  });

  const productsPerPage = 12; // 每页显示12个商品

  // 检查用户注册状态
  const checkUserStatus = async () => {
    if (!account) {
      setIsRegisteredUser(false);
      return;
    }

    try {
      const registered = await checkUserRegistered({ userAddress: account });
      setIsRegisteredUser(registered);
    } catch (error) {
      console.error('❌ [ProductList] 检查用户状态失败:', error);
      setIsRegisteredUser(false);
    }
  };

  // 加载商品列表
  const loadProducts = async () => {
    setIsLoading(true);
    try {
      // 获取商品总数
      const count = await getProductCount();
      setTotalProducts(count);

      if (count === 0) {
        setProducts([]);
        return;
      }

      // 计算当前页的商品范围
      const startIndex = (currentPage - 1) * productsPerPage + 1;
      const endIndex = Math.min(currentPage * productsPerPage, count);

      // 批量获取商品信息
      const productPromises = [];
      for (let i = startIndex; i <= endIndex; i++) {
        productPromises.push(
          getProductInfo({ productId: i }).catch(error => {
            console.warn(`⚠️ 获取商品 ${i} 信息失败:`, error.message);
            return null;
          })
        );
      }

      const productResults = await Promise.all(productPromises);

      // 过滤掉黑名单商家的商品
      const { shouldDisplayProduct } = await import('@/apis/mallApi');
      const validProductPromises = productResults
        .filter(product => product && product.isActive)
        .map(async (product) => {
          const shouldDisplay = await shouldDisplayProduct(product);
          return shouldDisplay ? product : null;
        });

      const validProductResults = await Promise.all(validProductPromises);
      const validProducts = validProductResults.filter(product => product !== null);

      // 保存所有商品用于筛选
      setAllProducts(validProducts);

      // 应用当前筛选条件
      applyFilters(validProducts, filters);

    } catch (error) {
      console.error('❌ [ProductList] 加载商品失败:', error);
      toast.error(`加载商品失败: ${error.message}`);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 格式化地址显示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // 处理搜索和筛选
  const handleSearch = (newFilters) => {
    console.log('🔍 [ProductList] 搜索:', newFilters);
    setFilters(newFilters);
    setCurrentPage(1); // 重置到第一页
    applyFilters(allProducts, newFilters);
  };

  const handleFilter = (newFilters) => {
    console.log('🔧 [ProductList] 筛选:', newFilters);
    setFilters(newFilters);
    setCurrentPage(1); // 重置到第一页
    applyFilters(allProducts, newFilters);
  };

  // 应用筛选逻辑
  const applyFilters = (productList, filterOptions) => {
    let filteredProducts = [...productList];

    // 搜索筛选
    if (filterOptions.searchTerm) {
      const searchTerm = filterOptions.searchTerm.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm)
      );
    }

    // 分类筛选
    if (filterOptions.category !== 'all') {
      // 这里可以根据商品的分类字段进行筛选
      // 暂时跳过，因为合约中没有分类字段
    }

    // 价格筛选
    if (filterOptions.priceRange !== 'all') {
      const [min, max] = filterOptions.priceRange.split('-').map(Number);
      if (max) {
        filteredProducts = filteredProducts.filter(product =>
          product.price >= min && product.price <= max
        );
      } else if (filterOptions.priceRange === '500+') {
        filteredProducts = filteredProducts.filter(product =>
          product.price >= 500
        );
      }
    }

    // 排序
    switch (filterOptions.sortBy) {
      case 'price-low':
        filteredProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filteredProducts.sort((a, b) => b.price - a.price);
        break;
      case 'sales':
        filteredProducts.sort((a, b) => (b.sales || 0) - (a.sales || 0));
        break;
      case 'newest':
      default:
        filteredProducts.sort((a, b) => (b.productId || 0) - (a.productId || 0));
        break;
    }

    setProducts(filteredProducts);
    setTotalProducts(filteredProducts.length);
  };

  // 处理页面变化
  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 计算分页信息
  const totalPages = Math.ceil(totalProducts / productsPerPage);
  const startIndex = (currentPage - 1) * productsPerPage;
  const endIndex = Math.min(startIndex + productsPerPage, totalProducts);

  // 监听账户和页面变化
  useEffect(() => {
    checkUserStatus();
  }, [account]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    loadProducts();
  }, [currentPage]); // eslint-disable-line react-hooks/exhaustive-deps

  // 如果没有连接钱包
  if (!account) {
    return (
      <div className="product-list">
        <div className="page-header">
          <h1>🛒 商城</h1>
          <p>发现优质商品，享受购物乐趣</p>
        </div>

        <div className="access-notice">
          <div className="notice-icon">🔐</div>
          <div className="notice-content">
            <h3>请连接钱包</h3>
            <p>连接钱包后即可浏览和购买商品</p>
          </div>
        </div>
      </div>
    );
  }

  // 如果用户未注册
  if (!isRegisteredUser) {
    return (
      <div className="product-list">
        <div className="page-header">
          <h1>🛒 商城</h1>
          <p>发现优质商品，享受购物乐趣</p>
        </div>

        <div className="access-notice">
          <div className="notice-icon">⚠️</div>
          <div className="notice-content">
            <h3>需要注册代理系统</h3>
            <p>请先前往个人中心完成代理系统注册</p>
            <p>注册后即可使用积分购买商品</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="product-list">
      <div className="page-header">
        <h1>🛒 商城</h1>
        <p>发现优质商品，享受购物乐趣</p>
        {totalProducts > 0 && (
          <div className="product-stats">
            <span>共 {totalProducts} 个商品</span>
            {totalPages > 1 && (
              <span>第 {currentPage}/{totalPages} 页</span>
            )}
          </div>
        )}
      </div>

      {/* 搜索和筛选组件 */}
      <SearchAndFilter
        onSearch={handleSearch}
        onFilter={handleFilter}
        totalProducts={totalProducts}
      />

      {isLoading ? (
        <div className="loading-container">
          <div className="loading-spinner">🔄</div>
          <p>正在加载商品...</p>
        </div>
      ) : products.length === 0 ? (
        <div className="empty-container">
          <div className="empty-icon">📦</div>
          <h3>暂无商品</h3>
          <p>商家正在努力上架商品，敬请期待！</p>
        </div>
      ) : (
        <>
          <div className="product-grid">
            {products.map((product) => (
              <Link
                to={`/product/${product.productId}`}
                key={product.productId}
                className="product-card"
              >
                <div className="product-image">
                  {product.images && product.images.length > 0 ? (
                    <ProductImage
                      imageUrl={product.images[0]}
                      alt={product.name}
                    />
                  ) : (
                    <div className="placeholder-image">📦</div>
                  )}
                </div>

                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  <p className="product-description">{product.description}</p>
                  <div className="product-meta">
                    <span className="product-price">
                      {needsPriceFix(product.price) ? fixPriceDisplay(product.price) : product.price} 积分
                    </span>
                    <span className="product-stock">库存: {product.stock}</span>
                  </div>
                  <div className="product-merchant">
                    <span>商家: {formatAddress(product.merchant)}</span>
                    <span>销量: {product.sales}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* 分页组件 */}
          {totalPages > 1 && (
            <div className="pagination-container">
              <div className="pagination-info">
                <span>
                  显示第 {startIndex + 1}-{endIndex} 个商品，共 {totalProducts} 个
                </span>
              </div>

              <div className="pagination-controls">
                <button
                  className={`pagination-btn prev ${currentPage === 1 ? 'disabled' : ''}`}
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  ← 上一页
                </button>

                <div className="pagination-numbers">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        className={`pagination-btn number ${currentPage === pageNum ? 'active' : ''}`}
                        onClick={() => handlePageChange(pageNum)}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  className={`pagination-btn next ${currentPage === totalPages ? 'disabled' : ''}`}
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  下一页 →
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
