/* src/pages/Product/ProductList.css */

.product-list {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
}

.page-header p {
  margin: 0 0 16px 0;
  font-size: 18px;
  opacity: 0.9;
}

.product-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  font-size: 14px;
  opacity: 0.8;
}

.product-stats span {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 12px;
}

/* 访问提示样式 */
.access-notice {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 2px solid #f59e0b;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin: 32px 0;
}

.notice-icon {
  font-size: 48px;
}

.notice-content h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #92400e;
}

.notice-content p {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #92400e;
  line-height: 1.5;
}

.notice-content p:last-child {
  margin-bottom: 0;
}

/* 加载和空状态样式 */
.loading-container,
.empty-container {
  text-align: center;
  padding: 64px 32px;
  color: #6b7280;
}

.loading-spinner,
.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 2s linear infinite;
}

.empty-icon {
  animation: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p,
.empty-container h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #374151;
}

.empty-container p {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
}

/* 商品网格样式 */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.product-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.product-image {
  width: 100%;
  aspect-ratio: 1;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  color: #9ca3af;
  font-size: 48px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 14px;
  color: #6b7280;
}

.product-info {
  padding: 20px;
}

.product-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.product-price {
  font-size: 18px;
  font-weight: 700;
  color: #059669;
}

.product-stock {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 8px;
  border-radius: 8px;
}

.product-merchant {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

/* 分页样式 */
.pagination-container {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  text-align: center;
  margin-bottom: 16px;
  color: #6b7280;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.pagination-btn:hover:not(.disabled) {
  background: #f9fafb;
  border-color: #667eea;
  color: #667eea;
}

.pagination-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.pagination-btn.disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
  border-color: #e5e7eb;
}

.pagination-numbers {
  display: flex;
  gap: 4px;
}

.pagination-btn.number {
  min-width: 40px;
  padding: 8px 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-list {
    padding: 16px;
  }

  .page-header {
    padding: 20px 16px;
    margin-bottom: 24px;
  }

  .page-header h1 {
    font-size: 24px;
  }

  .page-header p {
    font-size: 16px;
  }

  .product-stats {
    flex-direction: column;
    gap: 8px;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }

  .product-info {
    padding: 16px;
  }

  .product-name {
    font-size: 16px;
  }

  .product-price {
    font-size: 16px;
  }

  .access-notice {
    padding: 24px 16px;
  }

  .notice-icon {
    font-size: 36px;
  }

  .notice-content h3 {
    font-size: 20px;
  }

  .notice-content p {
    font-size: 14px;
  }

  .pagination-controls {
    gap: 4px;
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .pagination-btn.number {
    min-width: 36px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .product-list {
    padding: 12px;
  }

  .page-header {
    padding: 16px 12px;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .product-info {
    padding: 12px;
  }

  .access-notice {
    padding: 20px 12px;
  }

  .loading-container,
  .empty-container {
    padding: 48px 16px;
  }
}
