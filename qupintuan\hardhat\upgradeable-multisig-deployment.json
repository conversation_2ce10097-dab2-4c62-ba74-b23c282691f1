{"network": "bscTestnet", "timestamp": "2025-07-30T22:30:47.354Z", "deployer": "******************************************", "contract": {"name": "MultiSigWallet", "type": "UUPS_UPGRADEABLE", "address": "******************************************", "implementation": "******************************************", "owners": ["******************************************", "******************************************", "******************************************"], "requiredConfirmations": 2, "tempTimelock": "******************************************"}, "features": ["UUPS_UPGRADEABLE", "ENHANCED_QUERY_FUNCTIONS", "TIMELOCK_PROTECTED", "PAUSABLE"]}