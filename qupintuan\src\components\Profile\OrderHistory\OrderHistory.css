/* src/components/Profile/OrderHistory/OrderHistory.css */

.order-history {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 状态过滤器 */
.status-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-btn {
  padding: 8px 16px;
  border: 2px solid #e9ecef;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

/* 订单内容区域 */
.orders-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.order-card {
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.order-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.order-id {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.order-status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
}

.order-time {
  color: #666;
  font-size: 14px;
}

.order-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
}

.product-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.product-info p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.price-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-info span {
  color: #666;
  font-size: 14px;
}

.total-price {
  font-weight: 600;
  color: #667eea !important;
  font-size: 16px !important;
}

.merchant-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.shipping-info {
  grid-column: 1 / -1;
  padding: 20px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 12px;
  margin-top: 16px;
}

.shipping-header {
  margin-bottom: 16px;
}

.shipping-header h5 {
  margin: 0;
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
}

.shipping-details p {
  margin: 8px 0;
  color: #1890ff;
  font-size: 14px;
}

.tracking-number {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 8px 0;
}

.copy-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.tracking-tip {
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 6px;
  color: #1890ff;
  font-size: 12px;
  line-height: 1.4;
}

/* 订单时间线 */
.order-timeline {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #e9ecef;
}

.order-timeline::before {
  content: '';
  position: absolute;
  top: 35px;
  left: 12.5%;
  right: 12.5%;
  height: 2px;
  background: #e9ecef;
  z-index: 1;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e9ecef;
  border: 2px solid white;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.timeline-item.completed .timeline-dot {
  background: #52c41a;
}

.timeline-content {
  text-align: center;
}

.timeline-title {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-item.completed .timeline-title {
  color: #52c41a;
  font-weight: 500;
}

.timeline-time {
  display: block;
  font-size: 11px;
  color: #999;
}

.not-connected {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.not-connected h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 20px;
}

.not-connected p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-history {
    padding: 16px;
  }

  .section-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .status-filters {
    flex-wrap: wrap;
    gap: 8px;
  }

  .order-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .order-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .tracking-number {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .order-timeline {
    flex-direction: column;
    gap: 16px;
  }

  .order-timeline::before {
    display: none;
  }

  .timeline-item {
    flex-direction: row;
    text-align: left;
  }

  .timeline-content {
    text-align: left;
    margin-left: 12px;
  }
}
