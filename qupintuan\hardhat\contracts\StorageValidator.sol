// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * @title StorageValidator
 * @dev 通用存储验证基类，用于防止合约升级时的存储槽损坏
 * @notice 所有可升级合约都应该继承此基类
 * @dev 不继承权限管理合约，让子合约自由选择权限管理方式
 */
abstract contract StorageValidator {
    
    // 存储布局版本号
    uint256 public constant STORAGE_LAYOUT_VERSION = 1;
    
    // 存储验证事件
    event StorageValidationPassed(address indexed contract_, uint256 version);
    event StorageValidationFailed(address indexed contract_, string reason);
    event StorageFixed(address indexed contract_, string fixType);
    event UpgradeAuthorized(address indexed newImplementation, uint256 timestamp);
    
    // 存储状态校验和
    mapping(string => bytes32) private _storageChecksums;
    
    // 紧急修复标志
    bool internal _emergencyFixMode;
    
    /**
     * @dev 验证存储布局完整性 - 子合约必须实现
     * @return bool 存储布局是否有效
     */
    function validateStorageLayout() public view virtual returns (bool);
    
    /**
     * @dev 计算存储状态校验和 - 子合约必须实现
     * @return bytes32 存储状态的校验和
     */
    function calculateStorageChecksum() public view virtual returns (bytes32);
    
    /**
     * @dev 紧急修复存储问题 - 子合约必须实现
     * @dev 子合约应该添加适当的权限控制修饰符
     */
    function emergencyStorageFix() external virtual {
        // 默认实现
        emit StorageFixed(address(this), "Basic storage validation");
    }
    
    /**
     * @dev 保存升级前状态
     * @dev 子合约应该添加适当的权限控制修饰符
     */
    function savePreUpgradeState() external virtual {
        bytes32 checksum = calculateStorageChecksum();
        _storageChecksums["preUpgrade"] = checksum;
        _storageChecksums[string(abi.encodePacked("backup_", block.timestamp))] = checksum;
    }
    
    /**
     * @dev 获取存储校验和
     * @param key 校验和键名
     * @return bytes32 校验和值
     */
    function getStorageChecksum(string memory key) external view returns (bytes32) {
        return _storageChecksums[key];
    }
    
    /**
     * @dev 验证升级前后状态一致性
     * @return bool 状态是否一致
     */
    function validateUpgradeConsistency() external view returns (bool) {
        bytes32 preUpgrade = _storageChecksums["preUpgrade"];
        if (preUpgrade == bytes32(0)) {
            return true; // 没有保存升级前状态，跳过验证
        }
        
        bytes32 current = calculateStorageChecksum();
        return preUpgrade == current;
    }
    
    /**
     * @dev 设置紧急修复模式
     * @param enabled 是否启用紧急修复模式
     * @dev 子合约应该添加适当的权限控制修饰符
     */
    function setEmergencyFixMode(bool enabled) external virtual {
        _emergencyFixMode = enabled;
    }
    
    /**
     * @dev 检查是否处于紧急修复模式
     * @return bool 是否处于紧急修复模式
     */
    function isEmergencyFixMode() external view returns (bool) {
        return _emergencyFixMode;
    }
    
    /**
     * @dev 验证合约基本状态
     * @return bool 基本状态是否正常
     * @dev 子合约可以重写此函数添加特定的状态检查
     */
    function validateBasicState() public view virtual returns (bool) {
        // 检查存储布局版本
        if (STORAGE_LAYOUT_VERSION == 0) {
            return false;
        }

        return true;
    }
    
    /**
     * @dev 获取合约健康状态报告
     * @return isHealthy 合约是否健康
     * @return issues 发现的问题列表
     */
    function getHealthReport() external view returns (bool isHealthy, string[] memory issues) {
        string[] memory tempIssues = new string[](10); // 临时数组
        uint256 issueCount = 0;
        
        // 检查基本状态
        if (!validateBasicState()) {
            tempIssues[issueCount] = "Basic state validation failed";
            issueCount++;
        }
        
        // 检查存储布局
        if (!validateStorageLayout()) {
            tempIssues[issueCount] = "Storage layout validation failed";
            issueCount++;
        }
        
        // 子合约可以重写此函数添加更多检查
        
        // 创建正确大小的数组
        issues = new string[](issueCount);
        for (uint256 i = 0; i < issueCount; i++) {
            issues[i] = tempIssues[i];
        }
        
        isHealthy = (issueCount == 0);
    }
    
    /**
     * @dev 增强的授权升级函数
     * @param newImplementation 新实现合约地址
     * @dev 子合约应该添加权限检查并调用此函数
     */
    function _authorizeUpgradeWithValidation(address newImplementation) internal {
        require(newImplementation != address(0), "Invalid implementation");
        require(newImplementation.code.length > 0, "Implementation not deployed");

        // 验证存储布局完整性
        if (!_emergencyFixMode) {
            require(validateStorageLayout(), "Storage layout corrupted");
            require(validateBasicState(), "Basic state validation failed");
        }

        // 保存升级前状态
        _storageChecksums["preUpgrade"] = calculateStorageChecksum();

        // 发出升级授权事件
        emit UpgradeAuthorized(newImplementation, block.timestamp);
    }
    
    /**
     * @dev 升级后验证函数
     * @dev 子合约应该添加适当的权限控制修饰符
     */
    function postUpgradeValidation() external virtual {
        require(validateStorageLayout(), "Post-upgrade storage validation failed");
        require(validateBasicState(), "Post-upgrade basic state validation failed");
        
        emit StorageValidationPassed(address(this), STORAGE_LAYOUT_VERSION);
    }
}
