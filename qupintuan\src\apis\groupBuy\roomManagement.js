// src/apis/groupBuy/roomManagement.js
// 房间管理模块：创建、加入、关闭房间等操作

/**
 * 发起人第2步：授权QPT给QPTLocker合约
 * @param {Object} params - 请求参数
 * @param {number} params.chainId - 链 ID
 * @param {number} params.tier - 档位（30, 50, 100, 200, 500, 1000）
 * @param {Object} params.signer - 钱包签名者
 * @returns {Promise<*>}
 */
export async function approveQPTForCreate({ chainId, tier, signer }) {
  try {
    if (!signer) {
      throw new Error('未检测到钱包签名者');
    }

    const userAddress = signer.account?.address;
    if (!userAddress) {
      throw new Error('无法获取用户地址');
    }

    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const qptLockerAddress = getContractAddress(chainId, 'QPTLocker');
    const qptAddress = getContractAddress(chainId, 'QPTToken');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const tierNum = Number(tier);

    // 将USDT档位转换为合约中的格式（6位小数）
    const usdtAmountInContract = BigInt(tierNum * 1000000); // 30 USDT -> ********

    // 从QPTLocker合约获取正确的锁仓数量
    const amountMapping = await publicClient.readContract({
      address: qptLockerAddress,
      abi: ABIS.QPTLocker,
      functionName: 'amountMappings',
      args: [usdtAmountInContract]
    });

    // amountMapping返回 [usdtAmount, lockAmount, rewardAmount]
    const qptAmount = amountMapping[1]; // lockAmount字段
    const qptAmountDisplay = Number(qptAmount) / (10 ** 18); // 转换为显示格式

    const qptBalance = await publicClient.readContract({
      address: qptAddress,
      abi: ABIS.QPTToken,
      functionName: 'balanceOf',
      args: [userAddress]
    });

    const balanceAmount = BigInt(qptBalance);
    const userBalance = Number(balanceAmount) / 1e18;

    if (balanceAmount < qptAmount) {
      throw new Error(`QPT余额不足。需要: ${qptAmountDisplay} QPT，当前: ${userBalance} QPT`);
    }

    const txHash = await signer.writeContract({
      address: qptAddress,
      abi: ABIS.QPTToken,
      functionName: 'approve',
      args: [qptLockerAddress, qptAmount]
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    return {
      success: true,
      receipt,
      txHash,
      approvedAmount: qptAmountDisplay,
      userBalance,
      message: `第2步完成：授权 ${qptAmountDisplay} QPT`
    };

  } catch (error) {
    throw error;
  }
}

/**
 * 发起人第3步：锁仓QPT到QPTLocker合约
 * @param {Object} params - 请求参数
 * @param {number} params.chainId - 链 ID
 * @param {number} params.tier - 档位
 * @param {number} params.roomId - 房间ID
 * @param {Object} params.signer - 钱包签名者
 * @returns {Promise<*>}
 */
export async function lockQPTForCreate({ chainId, tier, roomId, signer }) {
  try {
    if (!signer) {
      throw new Error('未检测到钱包签名者');
    }

    const userAddress = signer.account?.address;
    if (!userAddress) {
      throw new Error('无法获取用户地址');
    }

    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const qptLockerAddress = getContractAddress(chainId, 'QPTLocker');
    const qptAddress = getContractAddress(chainId, 'QPTToken');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const tierNum = Number(tier);

    // 将USDT档位转换为合约中的格式（6位小数）
    const usdtAmountInContract = BigInt(tierNum * 1000000); // 30 USDT -> ********

    // 从QPTLocker合约获取正确的锁仓数量
    const amountMapping = await publicClient.readContract({
      address: qptLockerAddress,
      abi: ABIS.QPTLocker,
      functionName: 'amountMappings',
      args: [usdtAmountInContract]
    });

    // amountMapping返回 [usdtAmount, lockAmount, rewardAmount]
    const qptAmount = amountMapping[1]; // lockAmount字段
    const qptAmountDisplay = Number(qptAmount) / (10 ** 18); // 转换为显示格式

    const qptAllowance = await publicClient.readContract({
      address: qptAddress,
      abi: ABIS.QPTToken,
      functionName: 'allowance',
      args: [userAddress, qptLockerAddress]
    });

    const allowanceAmount = BigInt(qptAllowance);
    const userAllowance = Number(allowanceAmount) / 1e18;

    if (allowanceAmount < qptAmount) {
      throw new Error(`QPT授权不足。需要: ${qptAmountDisplay} QPT，当前授权: ${userAllowance} QPT。请先完成第2步授权操作。`);
    }

    const txHash = await signer.writeContract({
      address: qptLockerAddress,
      abi: ABIS.QPTLocker,
      functionName: 'lockForRoom',
      args: [BigInt(roomId), usdtAmountInContract]
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    return {
      success: true,
      receipt,
      txHash,
      roomId,
      lockedAmount: qptAmountDisplay,
      message: `第3步完成：为房间 #${roomId} 锁仓 ${qptAmountDisplay} QPT`
    };

  } catch (error) {
    throw error;
  }
}

/**
 * 发起人第1步：创建拼团房间（不验证QPT锁仓）
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链的 ID
 * @param {string|BigNumber} params.tier - 房间等级或金额
 * @param {Object} params.signer - 钱包签名者
 * @returns {Promise<{roomId: number, receipt}>} - 返回房间 ID 和交易收据
 */
export async function createRoomWithQPTVerification({ chainId, tier, signer }) {
  try {
    console.log('🚀 [createRoomWithQPTVerification] 发起人第1步：创建拼团房间:', { chainId, tier, signerAddress: signer?.account?.address });

    if (!signer) {
      throw new Error('未检测到钱包签名者，请安装 MetaMask 或其他以太坊兼容钱包');
    }

    // 获取合约地址
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');

    // 获取合约 ABI
    const { ABIS } = await import('@/contracts/index.ts');
    const GroupBuyRoomABI = ABIS.GroupBuyRoom;

    console.log('📋 [apiCreateRoom] 合约信息:', {
      contractAddress,
      hasABI: !!GroupBuyRoomABI,
      abiLength: GroupBuyRoomABI?.length,
      chainId,
      tier: tier.toString()
    });

    // 验证合约地址
    if (!contractAddress || contractAddress === '0x0000000000000000000000000000000000000000') {
      throw new Error(`无效的合约地址: ${contractAddress}，链ID: ${chainId}`);
    }

    // 验证 ABI
    if (!GroupBuyRoomABI || GroupBuyRoomABI.length === 0) {
      throw new Error('GroupBuyRoom ABI 未找到或为空');
    }

    // 检查 createRoom 函数是否存在于 ABI 中
    const createRoomFunction = GroupBuyRoomABI.find(item =>
      item.type === 'function' && item.name === 'createRoom'
    );
    if (!createRoomFunction) {
      throw new Error('createRoom 函数在 ABI 中不存在');
    }

    // 检查 RoomCreated 事件是否存在于 ABI 中
    const roomCreatedEvent = GroupBuyRoomABI.find(item =>
      item.type === 'event' && item.name === 'RoomCreated'
    );
    if (!roomCreatedEvent) {
      throw new Error('RoomCreated 事件在 ABI 中不存在');
    }

    console.log('✅ [apiCreateRoom] ABI 验证通过:', {
      hasCreateRoomFunction: !!createRoomFunction,
      hasRoomCreatedEvent: !!roomCreatedEvent
    });

    // 获取签名者地址
    const signerAddress = signer.account.address;
    console.log('👤 [createRoomWithQPTVerification] 签名者地址:', signerAddress);

    // 获取正确的档位值（从合约常量）
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // tier 参数已经是原始数值（如 30），不需要转换
    const tierNum = Number(tier);

    console.log('🔢 [createRoomWithQPTVerification] 档位信息:', {
      originalTier: tier.toString(),
      tierNum,
      tierType: typeof tier
    });

    // 第1步：直接创建房间，不验证QPT锁仓（QPT锁仓将在后续步骤完成）
    console.log('🏠 [createRoomWithQPTVerification] 第1步：创建拼团房间（不验证QPT锁仓）');

    // 验证档位是否有效
    const supportedTiers = [30, 50, 100, 200, 500, 1000];
    if (!supportedTiers.includes(tierNum)) {
      throw new Error(`不支持的档位: ${tierNum}。支持的档位: ${supportedTiers.join(', ')}`);
    }

    // 根据档位数值获取合约中的档位常量
    let contractTierValue;
    try {
      const tierFunctionName = `TIER_${tierNum}`;
      console.log('📞 [apiCreateRoom] 调用合约函数:', tierFunctionName);

      contractTierValue = await publicClient.readContract({
        address: contractAddress,
        abi: GroupBuyRoomABI,
        functionName: tierFunctionName
      });

      console.log('✅ [apiCreateRoom] 获取合约档位值成功:', {
        tierFunctionName,
        contractTierValue: contractTierValue.toString()
      });
    } catch (tierError) {
      console.error('❌ [apiCreateRoom] 获取合约档位值失败:', tierError.message);
      throw new Error(`不支持的档位: ${tierNum}。支持的档位: 30, 50, 100, 200, 500, 1000。错误详情: ${tierError.message}`);
    }

    // 估算 Gas（用于提前发现问题）
    let gasEstimate;
    try {
      gasEstimate = await publicClient.estimateContractGas({
        address: contractAddress,
        abi: GroupBuyRoomABI,
        functionName: 'createRoom',
        args: [contractTierValue],
        account: signerAddress
      });
      console.log('⛽ [apiCreateRoom] Gas 估算成功:', gasEstimate.toString());
    } catch (gasError) {
      console.error('❌ [apiCreateRoom] Gas 估算失败:', gasError.message);
      console.error('可能的原因：');
      console.error('1. 用户 USDT 余额不足');
      console.error('2. 用户未授权合约使用 USDT');
      console.error('3. 合约参数错误');
      console.error('4. 合约逻辑错误');
      throw new Error(`Gas 估算失败，交易可能会失败。请检查：1) USDT余额 2) USDT授权 3) 网络连接。详细错误: ${gasError.message}`);
    }

    // 发送交易
    console.log('📤 [apiCreateRoom] 发送交易:', {
      contractAddress,
      functionName: 'createRoom',
      args: [contractTierValue.toString()],
      estimatedGas: gasEstimate.toString(),
      signerAddress,
      originalTier: tier.toString(),
      tierNum
    });

    const txHash = await signer.writeContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'createRoom',
      args: [contractTierValue],
      gas: gasEstimate * 120n / 100n // 增加 20% 的 Gas 缓冲
    });

    console.log('✅ [apiCreateRoom] 交易已发送:', txHash);

    // 等待交易确认（复用已有的 publicClient）
    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    console.log('📄 [apiCreateRoom] 交易收据:', {
      hash: receipt.hash,
      status: receipt.status,
      logsCount: receipt.logs?.length || 0,
      blockNumber: receipt.blockNumber,
      gasUsed: receipt.gasUsed?.toString()
    });

    // 检查交易是否成功
    if (receipt.status === 'reverted') {
      console.error('❌ [apiCreateRoom] 交易被回滚，可能的原因:');
      console.error('1. 用户余额不足（USDT 或 Gas）');
      console.error('2. 合约函数调用失败（参数错误、权限问题等）');
      console.error('3. 合约内部逻辑错误');
      console.error('4. 网络拥堵或Gas价格过低');

      throw new Error(`交易被回滚，请检查：1) USDT余额是否足够 2) Gas费用是否足够 3) 网络是否正常。交易哈希: ${receipt.transactionHash || receipt.hash}`);
    }

    // 从事件日志中提取房间ID
    let roomId = null;
    console.log('🔍 [apiCreateRoom] 开始解析事件日志，日志数量:', receipt.logs.length);
    console.log('🔍 [apiCreateRoom] 合约地址:', contractAddress);

    for (let i = 0; i < receipt.logs.length; i++) {
      const log = receipt.logs[i];
      console.log(`📝 [apiCreateRoom] 解析日志 ${i + 1}:`, {
        address: log.address,
        contractAddress: contractAddress,
        isFromContract: log.address.toLowerCase() === contractAddress.toLowerCase(),
        topics: log.topics,
        topicsLength: log.topics?.length || 0,
        dataLength: log.data?.length || 0,
        data: log.data
      });

      // 只处理来自目标合约的日志
      if (log.address.toLowerCase() !== contractAddress.toLowerCase()) {
        console.log(`⏭️ [apiCreateRoom] 跳过非目标合约的日志 ${i + 1}`);
        continue;
      }

      try {
        const { decodeEventLog } = await import('viem');
        const decoded = decodeEventLog({
          abi: GroupBuyRoomABI,
          data: log.data,
          topics: log.topics,
        });

        console.log(`✅ [apiCreateRoom] 成功解码事件 ${i + 1}:`, {
          eventName: decoded.eventName,
          args: decoded.args,
          argsKeys: Object.keys(decoded.args || {}),
          roomIdRaw: decoded.args?.roomId,
          roomIdType: typeof decoded.args?.roomId
        });

        if (decoded.eventName === 'RoomCreated') {
          const rawRoomId = decoded.args.roomId;
          roomId = Number(rawRoomId);
          console.log('🎯 [apiCreateRoom] 找到 RoomCreated 事件，房间ID:', {
            originalValue: rawRoomId,
            originalType: typeof rawRoomId,
            convertedValue: roomId,
            convertedType: typeof roomId,
            isValid: roomId >= 0,
            isNumber: !isNaN(roomId)
          });
          break;
        }
      } catch (decodeError) {
        console.warn(`⚠️ [apiCreateRoom] 无法解码日志 ${i + 1}:`, {
          error: decodeError.message,
          logAddress: log.address,
          topicsLength: log.topics?.length || 0,
          dataLength: log.data?.length || 0
        });
        // 跳过无法解码的日志
        continue;
      }
    }

    if (roomId === null || roomId === undefined) {
      console.error('❌ [apiCreateRoom] 无法提取房间ID，详细信息:', {
        receiptStatus: receipt.status,
        logsCount: receipt.logs.length,
        contractAddress: contractAddress,
        txHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed?.toString(),
        roomIdValue: roomId,
        roomIdType: typeof roomId,
        logs: receipt.logs.map((log, index) => ({
          index,
          address: log.address,
          topicsCount: log.topics?.length || 0,
          dataLength: log.data?.length || 0
        }))
      });
      throw new Error(`无法从交易收据中提取房间ID。交易哈希: ${receipt.transactionHash}，日志数量: ${receipt.logs.length}`);
    }

    console.log('✅ [createRoomWithQPTVerification] 第1步完成 - 拼团房间创建成功:', {
      roomId,
      receiptHash: receipt.hash,
      receiptStatus: receipt.status,
      logsCount: receipt.logs?.length || 0,
      blockNumber: receipt.blockNumber,
      timestamp: new Date().toISOString(),
      message: `第1步完成：创建拼团房间 #${roomId}`
    });

    // 额外验证：尝试立即查询新创建的房间
    try {
      console.log('🔍 [apiCreateRoom] 验证新创建的房间...');
      const verifyResult = await publicClient.readContract({
        address: contractAddress,
        abi: GroupBuyRoomABI,
        functionName: 'getRoom',
        args: [BigInt(roomId)]
      });
      console.log('✅ [apiCreateRoom] 房间验证成功:', {
        roomId,
        creator: verifyResult[0],
        tier: verifyResult[1]?.toString(),
        createTime: verifyResult[3]?.toString()
      });
    } catch (verifyError) {
      console.warn('⚠️ [apiCreateRoom] 房间验证失败:', verifyError.message);
    }

    return { roomId, receipt };
  } catch (error) {
    console.error('创建房间失败:', error);
    throw error;
  }
}

/**
 * 参与者第1步：强制重新授权USDT给GroupBuyRoom合约
 * @param {Object} params - 请求参数
 * @param {number} params.chainId - 链 ID
 * @param {string|number} params.roomId - 房间编号
 * @param {Object} params.signer - 钱包签名者
 * @returns {Promise<*>}
 */
export async function approveUSDTForJoin({ chainId, roomId, signer }) {
  try {
    if (!signer) {
      throw new Error('未检测到钱包签名者');
    }

    const userAddress = signer.account?.address;
    if (!userAddress) {
      throw new Error('无法获取用户地址');
    }

    // 获取合约地址和ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const groupBuyAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const usdtAddress = getContractAddress(chainId, 'USDT');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 获取房间信息以确定需要授权的金额
    const roomDetails = await publicClient.readContract({
      address: groupBuyAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator, tier] = roomDetails;
    const tierAmount = Number(tier) / 1000000;
    const requiredAmount = BigInt(tier);

    const usdtBalance = await publicClient.readContract({
      address: usdtAddress,
      abi: ABIS.USDT,
      functionName: 'balanceOf',
      args: [userAddress]
    });

    const balanceAmount = BigInt(usdtBalance);
    const userBalance = Number(balanceAmount) / 1000000;

    if (balanceAmount < requiredAmount) {
      throw new Error(`USDT余额不足。需要: ${tierAmount} USDT，当前: ${userBalance} USDT`);
    }
    const txHash = await signer.writeContract({
      address: usdtAddress,
      abi: ABIS.USDT,
      functionName: 'approve',
      args: [groupBuyAddress, requiredAmount]
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    return {
      success: true,
      receipt,
      txHash,
      approvedAmount: tierAmount,
      userBalance,
      message: `第1步完成：重新授权 ${tierAmount} USDT`
    };

  } catch (error) {
    throw error;
  }
}

/**
 * 参与者第2步：加入拼团房间并验证USDT支付
 * @param {Object} params - 请求参数
 * @param {number} params.chainId - 链 ID
 * @param {string|number} params.roomId - 房间编号
 * @param {Object} params.signer - 钱包签名者
 * @returns {Promise<*>}
 */
export async function joinRoomWithPaymentVerification({ chainId, roomId, signer }) {
  try {
    if (!signer) {
      throw new Error('未检测到钱包签名者');
    }

    const userAddress = signer.account?.address;
    if (!userAddress) {
      throw new Error('无法获取用户地址');
    }

    // 获取合约地址和ABI
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const usdtAddress = getContractAddress(chainId, 'USDT');
    const GroupBuyRoomABI = ABIS.GroupBuyRoom;

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 获取房间详细信息
    const roomDetails = await publicClient.readContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator, tier, participants, createTime, isClosed, isSuccessful] = roomDetails;
    const tierAmount = Number(tier) / 1000000;
    const requiredAmount = BigInt(tier);

    // 验证房间状态
    if (isClosed) {
      throw new Error('房间已关闭，无法参与');
    }

    if (participants.length >= 8) {
      throw new Error('房间已满员，无法参与');
    }

    // 检查用户是否已参与
    const hasJoined = participants.some(p => p.toLowerCase() === userAddress.toLowerCase());
    if (hasJoined) {
      throw new Error('您已参与此房间');
    }

    // 检查房间是否过期 - 使用合约返回的endTime字段
    const now = Math.floor(Date.now() / 1000);
    const endTime = Number(roomDetails[12]); // endTime字段在索引12

    if (now > endTime) {
      throw new Error('房间已过期，无法参与');
    }

    const [usdtBalance, usdtAllowance] = await Promise.all([
      publicClient.readContract({
        address: usdtAddress,
        abi: ABIS.USDT,
        functionName: 'balanceOf',
        args: [userAddress]
      }),
      publicClient.readContract({
        address: usdtAddress,
        abi: ABIS.USDT,
        functionName: 'allowance',
        args: [userAddress, contractAddress]
      })
    ]);

    const balanceAmount = BigInt(usdtBalance);
    const allowanceAmount = BigInt(usdtAllowance);
    const userBalance = Number(balanceAmount) / 1000000;
    const userAllowance = Number(allowanceAmount) / 1000000;

    if (balanceAmount < requiredAmount) {
      throw new Error(`USDT余额不足。需要: ${tierAmount} USDT，当前: ${userBalance} USDT`);
    }

    if (allowanceAmount < requiredAmount) {
      throw new Error(`USDT授权不足。需要: ${tierAmount} USDT，当前授权: ${userAllowance} USDT。请先完成第1步授权操作。`);
    }

    // 发送参与交易
    const txHash = await signer.writeContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'joinRoom',
      args: [BigInt(roomId)]
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    const finalBalance = await publicClient.readContract({
      address: usdtAddress,
      abi: ABIS.USDT,
      functionName: 'balanceOf',
      args: [userAddress]
    });

    const finalBalanceAmount = Number(finalBalance) / 1000000;
    const actualPaid = userBalance - finalBalanceAmount;

    return {
      receipt,
      txHash,
      roomId,
      paymentVerification: {
        expectedAmount: tierAmount,
        actualPaid,
        remainingBalance: finalBalanceAmount,
        paymentVerified: Math.abs(actualPaid - tierAmount) < 0.000001
      }
    };
  } catch (error) {
    throw error;
  }
}

/**
 * 验证发起人QPT锁仓状态
 * @param {Object} params - 验证参数
 * @param {number} params.chainId - 链ID
 * @param {string|number} params.roomId - 房间ID
 * @returns {Promise<Object>} 验证结果
 */
export async function validateCreatorQPTLock({ chainId, roomId }) {
  try {
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const groupBuyAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const qptLockerAddress = getContractAddress(chainId, 'QPTLocker');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 获取房间创建者
    const roomDetails = await publicClient.readContract({
      address: groupBuyAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator] = roomDetails;

    // 验证QPT锁仓状态
    const roomInfo = await publicClient.readContract({
      address: qptLockerAddress,
      abi: ABIS.QPTLocker,
      functionName: 'getRoomInfo',
      args: [BigInt(roomId)]
    });

    const [lockerCreator, amount, unlockTime, isSuccess, isClaimed] = roomInfo;

    const isValid = lockerCreator.toLowerCase() === creator.toLowerCase();

    return {
      isValid,
      creator,
      lockerCreator,
      amount: Number(amount) / 1e18,
      unlockTime: Number(unlockTime),
      isSuccess,
      isClaimed,
      message: isValid ? 'QPT锁仓验证通过' : '发起人未锁仓QPT'
    };

  } catch (error) {
    return {
      isValid: false,
      message: `QPT锁仓验证失败: ${error.message}`
    };
  }
}

/**
 * 验证参与者USDT状态
 * @param {Object} params - 验证参数
 * @param {number} params.chainId - 链ID
 * @param {string|number} params.roomId - 房间ID
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<Object>} 验证结果
 */
export async function validateParticipantUSDT({ chainId, roomId, userAddress }) {
  try {
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const groupBuyAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const usdtAddress = getContractAddress(chainId, 'USDT');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 获取房间档位信息
    const roomDetails = await publicClient.readContract({
      address: groupBuyAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator, tier] = roomDetails;
    const tierAmount = Number(tier) / 1000000;
    const requiredAmount = BigInt(tier);

    // 检查USDT余额和授权
    const [usdtBalance, usdtAllowance] = await Promise.all([
      publicClient.readContract({
        address: usdtAddress,
        abi: ABIS.USDT,
        functionName: 'balanceOf',
        args: [userAddress]
      }),
      publicClient.readContract({
        address: usdtAddress,
        abi: ABIS.USDT,
        functionName: 'allowance',
        args: [userAddress, groupBuyAddress]
      })
    ]);

    const balanceAmount = BigInt(usdtBalance);
    const allowanceAmount = BigInt(usdtAllowance);

    const result = {
      tierAmount,
      balance: Number(balanceAmount) / 1000000,
      allowance: Number(allowanceAmount) / 1000000,
      hasEnoughBalance: balanceAmount >= requiredAmount,
      hasEnoughAllowance: allowanceAmount >= requiredAmount,
      needsApproval: allowanceAmount < requiredAmount,
      errors: []
    };

    if (!result.hasEnoughBalance) {
      result.errors.push(`USDT余额不足 (需要: ${tierAmount}, 当前: ${result.balance})`);
    }

    if (!result.hasEnoughAllowance) {
      result.errors.push(`需要授权USDT (需要: ${tierAmount}, 当前授权: ${result.allowance})`);
    }

    result.canProceed = result.hasEnoughBalance && result.hasEnoughAllowance;
    return result;

  } catch (error) {
    return {
      canProceed: false,
      errors: [`USDT验证失败: ${error.message}`]
    };
  }
}

/**
 * 综合验证参与拼团的条件
 * @param {Object} params - 验证参数
 * @param {number} params.chainId - 链ID
 * @param {string|number} params.roomId - 房间ID
 * @param {string} params.userAddress - 用户地址
 * @returns {Promise<Object>} 验证结果
 */
export async function validateJoinRoomConditions({ chainId, roomId, userAddress }) {
  try {
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const { ABIS } = await import('@/contracts/index.ts');
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 获取房间基本信息
    const roomDetails = await publicClient.readContract({
      address: contractAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    });

    const [creator, tier, participants, createTime, isClosed, isSuccessful] = roomDetails;
    const tierAmount = Number(tier) / 1000000;

    const validationResult = {
      canJoin: true,
      errors: [],
      warnings: [],
      roomInfo: {
        creator,
        tierAmount,
        participantsCount: participants.length,
        isClosed,
        isSuccessful
      },
      steps: {
        needsApproval: false,
        canJoinRoom: false
      }
    };

    // 2. 验证房间基本状态
    if (isClosed) {
      validationResult.canJoin = false;
      validationResult.errors.push('房间已关闭');
    }

    if (participants.length >= 8) {
      validationResult.canJoin = false;
      validationResult.errors.push('房间已满员');
    }

    // 检查用户是否已参与
    const hasJoined = participants.some(p => p.toLowerCase() === userAddress.toLowerCase());
    if (hasJoined) {
      validationResult.canJoin = false;
      validationResult.errors.push('您已参与此房间');
    }

    // 检查房间是否过期 - 使用合约返回的endTime字段
    const now = Math.floor(Date.now() / 1000);
    const endTime = Number(roomDetails[12]); // endTime字段在索引12

    if (now > endTime) {
      validationResult.canJoin = false;
      validationResult.errors.push('房间已过期');
    }

    // 3. 验证发起人QPT锁仓（仅检查，不影响参与者操作）
    const qptValidation = await validateCreatorQPTLock({ chainId, roomId });
    if (!qptValidation.isValid) {
      validationResult.canJoin = false;
      validationResult.errors.push('发起人未锁仓QPT');
    }

    // 4. 验证参与者USDT状态
    const usdtValidation = await validateParticipantUSDT({ chainId, roomId, userAddress });
    validationResult.usdtInfo = usdtValidation;

    if (!usdtValidation.hasEnoughBalance) {
      validationResult.canJoin = false;
      validationResult.errors.push(...usdtValidation.errors.filter(e => e.includes('余额不足')));
    }

    // 设置操作步骤
    validationResult.steps.needsApproval = usdtValidation.needsApproval;
    validationResult.steps.canJoinRoom = validationResult.canJoin && !usdtValidation.needsApproval;

    return validationResult;

  } catch (error) {
    return {
      canJoin: false,
      errors: [`验证过程出错: ${error.message}`],
      warnings: [],
      steps: { needsApproval: false, canJoinRoom: false }
    };
  }
}

/**
 * 关闭房间（第1步开奖）
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链 ID
 * @param {string|number|bigint} params.roomId - 房间 ID
 * @param {Object} signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 * @throws {Error} 如果调用失败
 */
export async function closeRoom({ chainId, roomId, signer }) {
  try {
    if (!signer) {
      throw new Error('签名者未定义，请确保钱包已连接');
    }

    // 获取合约地址
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');

    // 获取合约 ABI
    const { ABIS } = await import('@/contracts/index.ts');
    const GroupBuyRoomABI = ABIS.GroupBuyRoom;

    // 发送交易
    const txHash = await signer.writeContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'closeRoom',
      args: [BigInt(roomId)]
    });

    // 等待交易确认
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    return { receipt, txHash };
  } catch (error) {
    console.error('关闭房间失败:', error);
    throw error;
  }
}

/**
 * 过期房间处理
 * @param {Object} params - 参数对象
 * @param {number} params.chainId - 链 ID
 * @param {string|number|bigint} params.roomId - 房间 ID
 * @param {Object} signer - wagmi 签名者对象
 * @returns {Promise<{receipt, txHash}>} - 返回交易收据和哈希
 * @throws {Error} 如果调用失败
 */
export async function expireRoom({ chainId, roomId, signer }) {
  try {
    if (!signer) {
      throw new Error('签名者未定义，请确保钱包已连接');
    }

    // 获取合约地址
    const { getContractAddress } = await import('@/contracts/addresses.ts');
    const contractAddress = getContractAddress(chainId, 'GroupBuyRoom');

    // 获取合约 ABI
    const { ABIS } = await import('@/contracts/index.ts');
    const GroupBuyRoomABI = ABIS.GroupBuyRoom;

    // 发送交易
    const txHash = await signer.writeContract({
      address: contractAddress,
      abi: GroupBuyRoomABI,
      functionName: 'expireRoom',
      args: [BigInt(roomId)]
    });

    // 等待交易确认
    const { createPublicClient, http } = await import('viem');
    const { bscTestnet } = await import('viem/chains');

    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    const receipt = await publicClient.waitForTransactionReceipt({
      hash: txHash,
      timeout: 60000
    });

    return { receipt, txHash };
  } catch (error) {
    console.error('过期房间处理失败:', error);
    throw error;
  }
}

// 兼容性导出 - 为了向后兼容，将createRoomWithQPTVerification也导出为createRoom
export { createRoomWithQPTVerification as createRoom };
export { joinRoomWithPaymentVerification as joinRoom };
