{"_format": "hh-sol-artifact-1", "contractName": "IOrderManagement", "sourceName": "contracts/ProductManagement.sol", "abi": [{"inputs": [{"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "address", "name": "merchant", "type": "address"}, {"internalType": "uint256", "name": "productId", "type": "uint256"}, {"internalType": "uint256", "name": "quantity", "type": "uint256"}, {"internalType": "uint256", "name": "totalPrice", "type": "uint256"}, {"internalType": "uint256", "name": "addressId", "type": "uint256"}], "name": "createOrder", "outputs": [{"internalType": "uint256", "name": "orderId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}