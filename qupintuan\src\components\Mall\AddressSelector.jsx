// src/components/Mall/AddressSelector.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import { createPublicClient, createWalletClient, http, custom } from 'viem';
import { bscTestnet } from 'viem/chains';
import './AddressSelector.css';

export default function AddressSelector({ onAddressChange, className = '' }) {
  const { address: account, isConnected } = useAccount();
  
  const [addresses, setAddresses] = useState([]);
  const [currentDefaultAddress, setCurrentDefaultAddress] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  // 创建公共客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http()
  });

  // 加载用户地址列表
  const loadAddresses = async () => {
    if (!account || !isConnected) return;

    setIsLoading(true);
    try {
      console.log('🔍 [AddressSelector] 加载用户地址列表...');
      
      const userAddresses = await publicClient.readContract({
        address: CONTRACT_ADDRESSES[97].AddressManagement,
        abi: ABIS.AddressManagement,
        functionName: 'getMyAddresses',
        args: [],
        account
      });

      console.log('✅ [AddressSelector] 地址加载成功:', userAddresses);
      setAddresses(userAddresses);

      // 找到当前默认地址
      const defaultAddr = userAddresses.find(addr => addr.isDefault);
      setCurrentDefaultAddress(defaultAddr);
      
      if (onAddressChange) {
        onAddressChange(defaultAddr);
      }

    } catch (error) {
      console.error('❌ [AddressSelector] 加载地址失败:', error);
      toast.error('加载地址列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 设置默认地址
  const setDefaultAddress = async (addressId) => {
    if (!account || !isConnected) {
      toast.error('请先连接钱包');
      return;
    }

    setIsChanging(true);
    try {
      console.log('🔄 [AddressSelector] 设置默认地址:', addressId);

      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      });

      // 发送设置默认地址的交易
      const hash = await walletClient.writeContract({
        address: CONTRACT_ADDRESSES[97].AddressManagement,
        abi: ABIS.AddressManagement,
        functionName: 'setDefaultAddress',
        args: [addressId],
        account
      });

      console.log('📋 [AddressSelector] 交易发送成功:', hash);
      toast.success('正在设置默认地址...');

      // 等待交易确认
      const receipt = await publicClient.waitForTransactionReceipt({ 
        hash,
        timeout: 60000
      });

      if (receipt.status === 'success') {
        console.log('✅ [AddressSelector] 默认地址设置成功');
        toast.success('默认地址设置成功！');
        
        // 重新加载地址列表
        await loadAddresses();
      } else {
        throw new Error('交易失败');
      }

    } catch (error) {
      console.error('❌ [AddressSelector] 设置默认地址失败:', error);
      toast.error(`设置默认地址失败: ${error.message}`);
    } finally {
      setIsChanging(false);
    }
  };

  // 组件挂载时加载地址
  useEffect(() => {
    loadAddresses();
  }, [account, isConnected]);

  if (!account || !isConnected) {
    return (
      <div className={`address-selector ${className}`}>
        <div className="address-selector-placeholder">
          请先连接钱包
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`address-selector ${className}`}>
        <div className="address-selector-loading">
          正在加载地址...
        </div>
      </div>
    );
  }

  if (addresses.length === 0) {
    return (
      <div className={`address-selector ${className}`}>
        <div className="address-selector-empty">
          <p>您还没有添加收货地址</p>
          <button 
            className="btn-add-address"
            onClick={() => {
              // 这里可以触发添加地址的弹窗
              toast.info('请前往个人中心添加收货地址');
            }}
          >
            添加地址
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`address-selector ${className}`}>
      <div className="address-selector-header">
        <h4>选择收货地址</h4>
        <span className="address-count">共 {addresses.length} 个地址</span>
      </div>

      <div className="address-list">
        {addresses.map((address, index) => {
          const addressId = typeof address.addressId === 'bigint' ? Number(address.addressId) : address.addressId;
          const isDefault = address.isDefault;
          
          return (
            <div 
              key={index}
              className={`address-item ${isDefault ? 'default' : ''}`}
            >
              <div className="address-info">
                <div className="address-header">
                  <span className="recipient-name">{address.name}</span>
                  <span className="recipient-phone">{address.phone}</span>
                  {isDefault && <span className="default-badge">默认</span>}
                </div>
                <div className="address-detail">
                  {address.province} {address.city} {address.district} {address.detail}
                </div>
              </div>
              
              {!isDefault && (
                <button
                  className="btn-set-default"
                  onClick={() => setDefaultAddress(addressId)}
                  disabled={isChanging}
                >
                  {isChanging ? '设置中...' : '设为默认'}
                </button>
              )}
            </div>
          );
        })}
      </div>

      {currentDefaultAddress && (
        <div className="current-default-info">
          <h5>当前默认地址：</h5>
          <p>
            {currentDefaultAddress.name} {currentDefaultAddress.phone}
            <br />
            {currentDefaultAddress.province} {currentDefaultAddress.city} {currentDefaultAddress.district} {currentDefaultAddress.detail}
          </p>
        </div>
      )}

      <div className="address-selector-note">
        <p>💡 购买商品时将自动使用默认地址</p>
        <p>💡 切换默认地址需要发送区块链交易</p>
      </div>
    </div>
  );
}
