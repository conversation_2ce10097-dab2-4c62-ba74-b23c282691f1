// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "./interfaces/IAddressManagement.sol";
import "./StorageValidator.sol";

interface IPointsManagement {
    function exchangeGoods(address user, address merchant, uint256 points) external;
}

interface IMerchantManagement {
    function isMerchant(address merchant) external view returns (bool);
    function updateMerchantSalesPoints(address merchant, uint256 points) external;
    function blacklist(address merchant) external view returns (bool);
}

interface IOrderManagement {
    function createOrder(
        address buyer,
        address merchant,
        uint256 productId,
        uint256 quantity,
        uint256 totalPrice,
        uint256 addressId
    ) external returns (uint256 orderId);
}

contract ProductManagement is Initializable, StorageValidator, OwnableUpgradeable, PausableUpgradeable, ReentrancyGuardUpgradeable, UUPSUpgradeable {
    // Timelock 控制
    address public timelock;

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    /// @notice 检查商家是否在黑名单中
    modifier notBlacklisted(address merchant) {
        require(!merchantManagement.blacklist(merchant), "Merchant is blacklisted");
        _;
    }

    // —— Constants —— //
    uint256 public constant MAX_IMAGES = 5; // 每个商品最多5张图片（优化Gas费）
    uint256 public constant MAX_IMAGE_HASH_LEN = 200; // 支持Cloudinary等长URL
    uint256 public constant MAX_TAGS_PER_PRODUCT = 5; // 每个产品最多5个标签
    uint256 public constant MAX_TAG_LENGTH = 50; // 标签最大长度

    // —— Events —— //
    event ProductCreated(uint256 indexed productId, address indexed merchant);
    event ProductUpdated(
        uint256 indexed productId,
        address indexed operator,
        bool isActive,
        uint256 stock
    );
    event ProductSalesUpdated(uint256 indexed productId, uint256 sales);
    event CategoryChanged(uint256 indexed categoryId, address indexed operator, bool isNew);

    // 新增：商品状态变更事件
    event ProductStatusChanged(uint256 indexed productId, address indexed merchant, bool isActive);
    event ProductStockUpdated(uint256 indexed productId, address indexed merchant, uint256 newStock);

    /// @notice 用户下单时，验证并记录其收货地址
    event DeliveryAddressVerified(
        address indexed user,
        uint256 indexed addressId,
        string region,
        string detail
    );

    /// @notice 商品购买成功事件
    event ProductPurchased(
        address indexed buyer,
        uint256 indexed productId,
        uint256 quantity,
        uint256 totalPrice,
        address indexed merchant,
        uint256 addressId
    );



    // —— 价格精度常量 —— //
    // 价格精度：与积分系统保持一致（6位小数）
    uint8 public constant PRICE_DECIMALS = 6;

    // —— Structs —— //
    struct Product {
        uint256 productId;
        address merchant;
        string name;
        string description;
        string[] images;
        uint256 price;        // 价格，使用6位小数精度 (例如: 1.50积分 = 1500000)
        uint256 stock;
        bool isActive;
        uint256 sales;
        string[] tags;        // 新增：产品标签
        uint256 rating;       // 新增：产品评分 (0-500, 表示0.0-5.0星)
        uint256 reviewCount;  // 新增：评价数量
    }

    struct Category {
        uint256 categoryId;
        string name;
        string description;
        bool isActive;
    }



    // —— State Variables —— //
    mapping(uint256 => Product) public products;
    mapping(uint256 => Category) public categories;
    mapping(address => uint256[]) public merchantProducts;
    uint256 public productCount;
    uint256 public categoryCount;

    // —— 订单管理合约地址 —— //
    address public orderManagement;

    IMerchantManagement public merchantManagement;
    IPointsManagement public pointsManagement;
    IAddressManagement public addressManagement;

    // 新增：产品标签和统计相关映射（必须放在最后以保持存储布局兼容性）
    mapping(string => uint256[]) public productsByTag;     // 标签 => 产品ID数组
    mapping(uint256 => bool) public featuredProducts;     // 特色产品标记
    mapping(uint256 => uint256) public productViews;      // 产品浏览次数

    // —— Initialization —— //

    /// @notice 初始化合约，设置相关合约地址
    /// @param _merchantManagement 商家管理合约地址
    /// @param _pointsManagement 积分管理合约地址
    /// @param _addressManagement 地址管理合约地址
    /// @param _orderManagement 订单管理合约地址
    /// @param _timelock Timelock合约地址
    function initialize(
        address _merchantManagement,
        address _pointsManagement,
        address _addressManagement,
        address _orderManagement,
        address _timelock
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        require(_merchantManagement != address(0), "Invalid merchantMgmt");
        require(_pointsManagement != address(0), "Invalid pointsMgmt");
        require(_addressManagement != address(0), "Invalid addrMgmt");
        require(_orderManagement != address(0), "Invalid orderMgmt");
        require(_timelock != address(0), "Invalid timelock");

        merchantManagement = IMerchantManagement(_merchantManagement);
        pointsManagement = IPointsManagement(_pointsManagement);
        addressManagement = IAddressManagement(_addressManagement);
        orderManagement = _orderManagement;
        timelock = _timelock;
    }

    /// @notice 设置Timelock地址（仅所有者）
    /// @param _timelock 新的Timelock地址
    function setTimelock(address _timelock) external onlyOwner {
        require(_timelock != address(0), "Invalid timelock");
        timelock = _timelock;
    }

    /// @notice 校验产品参数
    /// @param name 产品名称
    /// @param description 产品描述
    /// @param images 产品图片哈希数组
    /// @param price 产品价格
    /// @param stock 产品库存
    function _validateProductArgs(
        string memory name,
        string memory description,
        string[] memory images,
        uint256 price,
        uint256 stock
    ) internal pure {
        require(bytes(name).length > 0, "Name required");
        require(bytes(description).length > 0, "Description required");
        require(images.length > 0 && images.length <= MAX_IMAGES, "Invalid images count");
        for (uint256 i = 0; i < images.length; i++) {
            require(bytes(images[i]).length > 0, "Image hash empty");
            require(bytes(images[i]).length <= MAX_IMAGE_HASH_LEN, "Image hash too long");
        }
        require(price > 0, "Price > 0");
        require(stock > 0, "Stock > 0");

        // 验证价格精度：确保价格在合理范围内（6位精度）
        // 最小价格：0.000001 积分 = 1
        // 最大价格：1,000,000 积分 = 1,000,000 * 10^6
        require(price >= 1, "Price too small (min 0.000001)");
        require(price <= 1000000 * 10**PRICE_DECIMALS, "Price too large (max 1,000,000)");
    }

    /// @notice 创建新产品
    /// @param name 产品名称
    /// @param description 产品描述
    /// @param images 产品图片哈希数组
    /// @param price 产品价格
    /// @param stock 产品库存
    function createProduct(
        string memory name,
        string memory description,
        string[] memory images,
        uint256 price,
        uint256 stock
    ) external whenNotPaused notBlacklisted(msg.sender) {
        require(merchantManagement.isMerchant(msg.sender), "Not a merchant");
        _validateProductArgs(name, description, images, price, stock);

        productCount++;
        products[productCount] = Product({
            productId: productCount,
            merchant: msg.sender,
            name: name,
            description: description,
            images: images,
            price: price,
            stock: stock,
            isActive: true,
            sales: 0,
            tags: new string[](0),  // 初始化为空数组
            rating: 0,              // 初始评分为0
            reviewCount: 0          // 初始评价数为0
        });
        merchantProducts[msg.sender].push(productCount);

        emit ProductCreated(productCount, msg.sender);
    }

    /// @notice 更新产品信息
    /// @param productId 产品ID
    /// @param name 产品名称
    /// @param description 产品描述
    /// @param images 产品图片哈希数组
    /// @param price 产品价格
    /// @param stock 产品库存
    /// @param isActive 是否激活（可选参数，默认为当前状态）
    function updateProduct(
        uint256 productId,
        string memory name,
        string memory description,
        string[] memory images,
        uint256 price,
        uint256 stock,
        bool isActive
    ) external whenNotPaused {
        Product storage p = products[productId];
        require(p.merchant == msg.sender, "Not the owner");
        _validateProductArgs(name, description, images, price, stock);

        p.name = name;
        p.description = description;
        p.images = images;
        p.price = price;
        p.stock = stock;
        p.isActive = isActive;

        emit ProductUpdated(productId, msg.sender, isActive, stock);
    }

    /// @notice 购买产品（使用默认地址）
    /// @param productId 产品ID
    /// @param qty 购买数量
    function buyProductWithDefaultAddress(
        uint256 productId,
        uint256 qty
    ) external nonReentrant whenNotPaused {
        _buyProductWithDefaultAddress(productId, qty);
    }

    /// @notice 内部购买函数（统一使用默认地址逻辑）
    /// @param productId 产品ID
    /// @param qty 购买数量
    function _buyProductWithDefaultAddress(
        uint256 productId,
        uint256 qty
    ) internal {
        Product storage p = products[productId];
        require(p.isActive, "Not active");
        require(p.stock >= qty, "Insufficient stock");
        require(merchantManagement.isMerchant(p.merchant), "Merchant not exists");

        uint256 totalPrice = p.price * qty;

        // 1) 更新库存 & 销量
        p.stock -= qty;
        p.sales += qty;
        emit ProductSalesUpdated(productId, p.sales);

        // 2) 获取用户的默认地址（严格要求必须有默认地址）
        AddressInfo memory addr;

        // 必须有默认地址，不允许回退到第一个地址
        try addressManagement.getMyDefaultAddress() returns (AddressInfo memory defaultAddr) {
            addr = defaultAddr;
        } catch {
            revert("No default address found. Please set a default address first.");
        }

        require(addr.user == msg.sender, "Address user mismatch");

        string memory region = string(
            abi.encodePacked(addr.province, " ", addr.city, " ", addr.district)
        );
        emit DeliveryAddressVerified(
            msg.sender,
            addr.addressId,
            region,
            addr.detail
        );

        // 3) 扣用户积分并发货，后端可根据 addr 信息触发物流
        pointsManagement.exchangeGoods(
            msg.sender,
            p.merchant,
            totalPrice
        );

        // 4) 同步商家积分流水
        merchantManagement.updateMerchantSalesPoints(p.merchant, totalPrice);

        // 5) 创建订单记录（如果订单管理合约已设置）
        if (orderManagement != address(0)) {
            try IOrderManagement(orderManagement).createOrder(
                msg.sender,
                p.merchant,
                productId,
                qty,
                totalPrice,
                addr.addressId
            ) returns (uint256 orderId) {
                // 订单创建成功
            } catch {
                // 订单创建失败，但不影响购买流程
            }
        }

        // 6) 触发购买事件
        emit ProductPurchased(
            msg.sender,
            productId,
            qty,
            totalPrice,
            p.merchant,
            addr.addressId
        );
    }

    /// @notice 购买产品（统一使用默认地址）
    /// @param productId 产品ID
    /// @param qty 购买数量
    /// @dev 只使用用户的默认地址，确保地址验证逻辑一致性
    function buyProduct(
        uint256 productId,
        uint256 qty
    ) external nonReentrant whenNotPaused {
        // 直接调用默认地址购买函数
        _buyProductWithDefaultAddress(productId, qty);
    }

    /// @notice 创建新分类
    /// @param name 分类名称
    /// @param description 分类描述
    function createCategory(
        string memory name,
        string memory description
    ) external onlyOwner whenNotPaused {
        categoryCount++;
        categories[categoryCount] = Category({
            categoryId: categoryCount,
            name: name,
            description: description,
            isActive: true
        });
        emit CategoryChanged(categoryCount, msg.sender, true);
    }

    /// @notice 更新分类信息
    /// @param categoryId 分类ID
    /// @param name 分类名称
    /// @param description 分类描述
    /// @param isActive 是否激活
    function updateCategory(
        uint256 categoryId,
        string memory name,
        string memory description,
        bool isActive
    ) external onlyOwner whenNotPaused {
        require(categories[categoryId].categoryId != 0, "Not exists");
        Category storage c = categories[categoryId];
        c.name = name;
        c.description = description;
        c.isActive = isActive;
        emit CategoryChanged(categoryId, msg.sender, false);
    }

    // —— 新增：商品管理查询函数 —— //

    /// @notice 获取商家的所有商品ID列表
    /// @param merchant 商家地址
    /// @return 商品ID数组
    function getMerchantProducts(address merchant) external view returns (uint256[] memory) {
        return merchantProducts[merchant];
    }



    /// @notice 获取商家的商品统计信息
    /// @param merchant 商家地址
    /// @return totalProducts 总商品数
    /// @return activeProducts 激活商品数
    /// @return totalSales 总销量
    function getMerchantStats(address merchant) external view returns (
        uint256 totalProducts,
        uint256 activeProducts,
        uint256 totalSales
    ) {
        uint256[] memory productIds = merchantProducts[merchant];
        totalProducts = productIds.length;

        for (uint256 i = 0; i < productIds.length; i++) {
            Product storage p = products[productIds[i]];
            if (p.isActive) {
                activeProducts++;
            }
            totalSales += p.sales;
        }
    }

    /// @notice 分页获取所有激活的商品
    /// @param offset 偏移量
    /// @param limit 限制数量
    /// @return productList 商品信息数组
    /// @return total 总数量
    function getActiveProducts(uint256 offset, uint256 limit) external view returns (
        Product[] memory productList,
        uint256 total
    ) {
        // 先计算激活且非黑名单商家的商品总数
        uint256 activeCount = 0;
        for (uint256 i = 1; i <= productCount; i++) {
            if (products[i].isActive && !merchantManagement.blacklist(products[i].merchant)) {
                activeCount++;
            }
        }
        total = activeCount;

        // 计算实际返回数量
        uint256 returnCount = limit;
        if (offset >= activeCount) {
            returnCount = 0;
        } else if (offset + limit > activeCount) {
            returnCount = activeCount - offset;
        }

        productList = new Product[](returnCount);

        // 填充结果数组
        uint256 currentIndex = 0;
        uint256 resultIndex = 0;

        for (uint256 i = 1; i <= productCount && resultIndex < returnCount; i++) {
            if (products[i].isActive && !merchantManagement.blacklist(products[i].merchant)) {
                if (currentIndex >= offset) {
                    productList[resultIndex] = products[i];
                    resultIndex++;
                }
                currentIndex++;
            }
        }
    }

    // —— 新增：商品状态管理函数 —— //

    /// @notice 商家切换商品激活状态
    /// @param productId 商品ID
    /// @param isActive 是否激活
    function toggleProductStatus(uint256 productId, bool isActive) external whenNotPaused notBlacklisted(msg.sender) {
        Product storage p = products[productId];
        require(p.productId != 0, "Product not exists");
        require(p.merchant == msg.sender, "Not the owner");

        p.isActive = isActive;
        emit ProductStatusChanged(productId, msg.sender, isActive);
    }

    /// @notice 商家更新商品库存
    /// @param productId 商品ID
    /// @param newStock 新库存数量
    function updateProductStock(uint256 productId, uint256 newStock) external whenNotPaused notBlacklisted(msg.sender) {
        Product storage p = products[productId];
        require(p.productId != 0, "Product not exists");
        require(p.merchant == msg.sender, "Not the owner");

        p.stock = newStock;
        emit ProductStockUpdated(productId, msg.sender, newStock);
    }

    /// @notice 商家批量更新商品状态
    /// @param productIds 商品ID数组
    /// @param isActive 是否激活
    function batchToggleProductStatus(uint256[] memory productIds, bool isActive) external whenNotPaused notBlacklisted(msg.sender) {
        for (uint256 i = 0; i < productIds.length; i++) {
            Product storage p = products[productIds[i]];
            if (p.productId != 0 && p.merchant == msg.sender) {
                p.isActive = isActive;
                emit ProductStatusChanged(productIds[i], msg.sender, isActive);
            }
        }
    }

    /// @notice 删除商品（仅设置为非激活状态）
    /// @param productId 商品ID
    function deleteProduct(uint256 productId) external whenNotPaused notBlacklisted(msg.sender) {
        Product storage p = products[productId];
        require(p.productId != 0, "Product not exists");
        require(p.merchant == msg.sender, "Not the owner");

        p.isActive = false;
        emit ProductStatusChanged(productId, msg.sender, false);
    }

    /// @notice 暂停合约
    function pause() external onlyOwner {
        _pause();
    }

    /// @notice 恢复合约
    function unpause() external onlyOwner {
        _unpause();
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证产品管理基本状态
        if (productCount > 1000000) return false;
        if (categoryCount > 10000) return false;
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            productCount,
            categoryCount,
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // 产品管理的存储修复逻辑
        emit StorageFixed(address(this), "ProductManagement storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    // —— 新增功能函数 —— //

    /**
     * @dev 获取产品统计信息
     * @return totalProducts 总产品数
     * @return activeProducts 活跃产品数
     * @return totalSales 总销售量
     * @return totalCategories 总分类数
     */
    function getProductStats() external view returns (
        uint256 totalProducts,
        uint256 activeProducts,
        uint256 totalSales,
        uint256 totalCategories
    ) {
        totalProducts = productCount;
        totalCategories = categoryCount;

        uint256 activeProd = 0;
        uint256 sales = 0;

        for (uint256 i = 1; i <= productCount; i++) {
            if (products[i].isActive) {
                activeProd++;
            }
            sales += products[i].sales;
        }

        activeProducts = activeProd;
        totalSales = sales;
    }

    /**
     * @dev 批量获取产品信息
     * @param productIds 产品ID数组
     * @return productList 产品信息数组
     */
    function getProductsBatch(uint256[] calldata productIds) external view returns (Product[] memory productList) {
        productList = new Product[](productIds.length);

        for (uint256 i = 0; i < productIds.length; i++) {
            if (productIds[i] <= productCount && productIds[i] > 0) {
                productList[i] = products[productIds[i]];
            }
        }
    }

    /**
     * @dev 获取热销产品排行（按销量排序）
     * @param limit 返回数量限制
     * @return topProducts 热销产品ID数组
     * @return salesCounts 对应销量数组
     */
    function getTopSellingProducts(uint256 limit) external view returns (
        uint256[] memory topProducts,
        uint256[] memory salesCounts
    ) {
        require(limit > 0 && limit <= 100, "Invalid limit");

        uint256 actualLimit = limit > productCount ? productCount : limit;
        topProducts = new uint256[](actualLimit);
        salesCounts = new uint256[](actualLimit);

        // 简单的选择排序（适用于小数据集）
        for (uint256 i = 0; i < actualLimit; i++) {
            uint256 maxSales = 0;
            uint256 maxProductId = 0;

            for (uint256 j = 1; j <= productCount; j++) {
                if (products[j].isActive && products[j].sales > maxSales) {
                    // 检查是否已经在结果中
                    bool alreadyIncluded = false;
                    for (uint256 k = 0; k < i; k++) {
                        if (topProducts[k] == j) {
                            alreadyIncluded = true;
                            break;
                        }
                    }

                    if (!alreadyIncluded) {
                        maxSales = products[j].sales;
                        maxProductId = j;
                    }
                }
            }

            if (maxProductId > 0) {
                topProducts[i] = maxProductId;
                salesCounts[i] = maxSales;
            }
        }
    }

    /**
     * @dev 根据标签获取产品
     * @param tag 标签名称
     * @return productIds 产品ID数组
     */
    function getProductsByTag(string calldata tag) external view returns (uint256[] memory productIds) {
        return productsByTag[tag];
    }

    /**
     * @dev 为产品添加标签
     * @param productId 产品ID
     * @param tag 标签名称
     */
    function addProductTag(uint256 productId, string calldata tag) external {
        require(productId > 0 && productId <= productCount, "Invalid product ID");
        require(bytes(tag).length > 0 && bytes(tag).length <= MAX_TAG_LENGTH, "Invalid tag length");
        require(products[productId].merchant == msg.sender, "Only merchant can add tags");
        require(products[productId].tags.length < MAX_TAGS_PER_PRODUCT, "Too many tags");

        // 检查标签是否已存在
        for (uint256 i = 0; i < products[productId].tags.length; i++) {
            require(keccak256(bytes(products[productId].tags[i])) != keccak256(bytes(tag)), "Tag already exists");
        }

        products[productId].tags.push(tag);
        productsByTag[tag].push(productId);
    }

    /**
     * @dev 设置产品评分
     * @param productId 产品ID
     * @param rating 评分 (0-500, 表示0.0-5.0星)
     */
    function setProductRating(uint256 productId, uint256 rating) external {
        require(productId > 0 && productId <= productCount, "Invalid product ID");
        require(rating <= 500, "Rating must be 0-500");
        require(products[productId].merchant == msg.sender, "Only merchant can set rating");

        products[productId].rating = rating;
        products[productId].reviewCount++;
    }

    /**
     * @dev 增加产品浏览次数
     * @param productId 产品ID
     */
    function incrementProductViews(uint256 productId) external {
        require(productId > 0 && productId <= productCount, "Invalid product ID");
        productViews[productId]++;
    }

    /**
     * @dev 设置特色产品
     * @param productId 产品ID
     * @param featured 是否为特色产品
     */
    function setFeaturedProduct(uint256 productId, bool featured) external onlyOwner {
        require(productId > 0 && productId <= productCount, "Invalid product ID");
        featuredProducts[productId] = featured;
    }

    /**
     * @dev 获取特色产品列表
     * @return featuredList 特色产品ID数组
     */
    function getFeaturedProducts() external view returns (uint256[] memory featuredList) {
        // 先计算特色产品数量
        uint256 count = 0;
        for (uint256 i = 1; i <= productCount; i++) {
            if (featuredProducts[i] && products[i].isActive) {
                count++;
            }
        }

        // 构建结果数组
        featuredList = new uint256[](count);
        uint256 index = 0;
        for (uint256 i = 1; i <= productCount; i++) {
            if (featuredProducts[i] && products[i].isActive) {
                featuredList[index] = i;
                index++;
            }
        }
    }

    /// @notice 验证用户地址是否存在
    /// @param user 用户地址
    /// @param addressId 地址ID
    /// @return exists 地址是否存在
    /// @return addressInfo 地址信息
    function validateUserAddress(
        address user,
        uint256 addressId
    ) external view returns (bool exists, AddressInfo memory addressInfo) {
        try addressManagement.getMyAddresses() returns (AddressInfo[] memory userAddresses) {
            for (uint256 i = 0; i < userAddresses.length; i++) {
                if (userAddresses[i].addressId == addressId && userAddresses[i].user == user) {
                    return (true, userAddresses[i]);
                }
            }
            return (false, AddressInfo({
                addressId: 0,
                user: address(0),
                name: "",
                phone: "",
                province: "",
                city: "",
                district: "",
                detail: "",
                isDefault: false,
                createTime: 0
            }));
        } catch {
            return (false, AddressInfo({
                addressId: 0,
                user: address(0),
                name: "",
                phone: "",
                province: "",
                city: "",
                district: "",
                detail: "",
                isDefault: false,
                createTime: 0
            }));
        }
    }

    /// @notice 获取用户的默认地址
    /// @param user 用户地址
    /// @return hasDefault 是否有默认地址
    /// @return defaultAddress 默认地址信息
    function getUserDefaultAddress(
        address user
    ) external view returns (bool hasDefault, AddressInfo memory defaultAddress) {
        try addressManagement.getDefaultAddress(user) returns (AddressInfo memory addr) {
            return (true, addr);
        } catch {
            return (false, AddressInfo({
                addressId: 0,
                user: address(0),
                name: "",
                phone: "",
                province: "",
                city: "",
                district: "",
                detail: "",
                isDefault: false,
                createTime: 0
            }));
        }
    }

    // —— 订单管理设置 —— //

    /// @notice 设置订单管理合约地址
    /// @param _orderManagement 订单管理合约地址
    function setOrderManagement(address _orderManagement) external onlyOwner {
        orderManagement = _orderManagement;
    }

    function _authorizeUpgrade(address newImplementation) internal override {
        require(msg.sender == timelock, "Only Timelock can upgrade");
        require(newImplementation != address(0), "Invalid implementation");
        require(newImplementation.code.length > 0, "Implementation not deployed");

        // 验证存储布局完整性
        require(validateStorageLayout(), "Storage layout invalid");

        emit UpgradeAuthorized(newImplementation, block.timestamp);
    }
}
