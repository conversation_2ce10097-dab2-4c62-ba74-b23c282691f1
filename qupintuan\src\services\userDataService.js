/**
 * 统一的用户数据查询服务
 * 整合所有用户相关的查询功能，避免重复代码
 */

import { createPublicClient, http, formatUnits } from 'viem';
import { bscTestnet } from 'viem/chains';
import { checkUserRegistered, getUserInfo } from '@/apis/agentSystemApi';
import { isBlacklisted } from '@/apis/adminApi';

// 创建公共客户端
const publicClient = createPublicClient({
  chain: bscTestnet,
  transport: http()
});

/**
 * 获取合约配置
 */
async function getContractConfig() {
  const { CONTRACT_ADDRESSES } = await import('@/contracts/addresses');
  const { ABIS } = await import('@/contracts/index');
  return { CONTRACT_ADDRESSES, ABIS };
}

/**
 * 用户地址验证
 * @param {string} address - 用户地址
 * @returns {boolean} - 是否为有效地址
 */
export function validateUserAddress(address) {
  if (!address || typeof address !== 'string') {
    return false;
  }

  const trimmedAddress = address.trim();
  return /^0x[a-fA-F0-9]{40}$/.test(trimmedAddress);
}

/**
 * 格式化用户地址显示
 * @param {string} address - 用户地址
 * @returns {string} - 格式化后的地址
 */
export function formatUserAddress(address) {
  if (!validateUserAddress(address)) {
    return 'Invalid Address';
  }

  return `${address.slice(0, 6)}...${address.slice(-4)}`;
}

/**
 * 查询用户基础信息
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 用户基础信息
 */
export async function queryUserBasicInfo(userAddress) {
  try {
    console.log('👤 [queryUserBasicInfo] 查询用户基础信息:', userAddress);

    if (!validateUserAddress(userAddress)) {
      throw new Error('无效的用户地址');
    }

    const [isRegistered, isBlacklistedUser, userInfo] = await Promise.all([
      checkUserRegistered({ userAddress }).catch(() => false),
      isBlacklisted({ userAddress }).catch(() => false),
      getUserInfo({ userAddress }).catch(() => null)
    ]);

    return {
      address: userAddress,
      formattedAddress: formatUserAddress(userAddress),
      isRegistered,
      isBlacklisted: isBlacklistedUser,
      userInfo,
      timestamp: new Date()
    };
  } catch (error) {
    console.error('🚨 [queryUserBasicInfo] 查询失败:', error);
    throw new Error(`查询用户基础信息失败: ${error.message}`);
  }
}

/**
 * 查询用户质押节点数据
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 质押节点数据
 */
export async function queryUserNodeStaking(userAddress) {
  try {
    console.log('🏗️ [queryUserNodeStaking] 查询用户质押节点:', userAddress);

    const { CONTRACT_ADDRESSES, ABIS } = await getContractConfig();
    const nodeStakingAddress = CONTRACT_ADDRESSES[97].NodeStaking;

    // 使用正确的NodeStaking合约查询
    const [nodeInfo, isActive] = await Promise.all([
      // 查询节点信息
      publicClient.readContract({
        address: nodeStakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'nodes',
        args: [userAddress]
      }).catch(() => ({ stakedAmount: 0n, lockTime: 0n, isActive: false })),

      // 查询是否激活
      publicClient.readContract({
        address: nodeStakingAddress,
        abi: ABIS.NodeStaking,
        functionName: 'isNodeActive',
        args: [userAddress]
      }).catch(() => false)
    ]);

    const stakedAmount = nodeInfo.stakedAmount || 0n;
    const lockTime = nodeInfo.lockTime || 0n;

    return {
      stakedAmount: formatUnits(stakedAmount, 18),
      lockTime: Number(lockTime),
      isStaking: isActive,
      rawStakedAmount: stakedAmount,
      rawLockTime: lockTime
    };
  } catch (error) {
    console.error('🚨 [queryUserNodeStaking] 查询失败:', error);
    return {
      stakedAmount: '0',
      lockTime: 0,
      isStaking: false,
      rawStakedAmount: 0n,
      rawLockTime: 0n,
      error: error.message
    };
  }
}

/**
 * 查询用户积分详情
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 积分详情
 */
export async function queryUserPointsDetails(userAddress) {
  try {
    console.log('🎯 [queryUserPointsDetails] 查询用户积分详情:', userAddress);

    const { CONTRACT_ADDRESSES, ABIS } = await getContractConfig();
    const pointsManagementAddress = CONTRACT_ADDRESSES[97].PointsManagement;

    const [groupBuyPoints, salesPoints, totalEarned, totalSpent] = await Promise.all([
      // 查询拼团积分余额
      publicClient.readContract({
        address: pointsManagementAddress,
        abi: ABIS.PointsManagement,
        functionName: 'getGroupBuyPoints',
        args: [userAddress]
      }).catch(() => 0n),

      // 查询销售积分余额
      publicClient.readContract({
        address: pointsManagementAddress,
        abi: ABIS.PointsManagement,
        functionName: 'getSalesPoints',
        args: [userAddress]
      }).catch(() => 0n),

      // 查询总获得积分
      publicClient.readContract({
        address: pointsManagementAddress,
        abi: ABIS.PointsManagement,
        functionName: 'getTotalEarned',
        args: [userAddress]
      }).catch(() => 0n),

      // 查询总消费积分
      publicClient.readContract({
        address: pointsManagementAddress,
        abi: ABIS.PointsManagement,
        functionName: 'getTotalSpent',
        args: [userAddress]
      }).catch(() => 0n)
    ]);

    return {
      groupBuyPoints: formatUnits(groupBuyPoints, 6),
      salesPoints: formatUnits(salesPoints, 6),
      totalEarned: formatUnits(totalEarned, 6),
      totalSpent: formatUnits(totalSpent, 6),
      rawGroupBuyPoints: groupBuyPoints,
      rawSalesPoints: salesPoints,
      rawTotalEarned: totalEarned,
      rawTotalSpent: totalSpent
    };
  } catch (error) {
    console.error('🚨 [queryUserPointsDetails] 查询失败:', error);
    return {
      groupBuyPoints: '0',
      salesPoints: '0',
      totalEarned: '0',
      totalSpent: '0',
      error: error.message
    };
  }
}

/**
 * 查询用户商家状态
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 商家状态
 */
export async function queryUserMerchantStatus(userAddress) {
  try {
    console.log('🏪 [queryUserMerchantStatus] 查询用户商家状态:', userAddress);

    const { CONTRACT_ADDRESSES, ABIS } = await getContractConfig();
    const merchantAddress = CONTRACT_ADDRESSES[97].MerchantManagement;

    const [merchantData, verificationData] = await Promise.all([
      publicClient.readContract({
        address: merchantAddress,
        abi: ABIS.MerchantManagement,
        functionName: 'merchants',
        args: [userAddress]
      }).catch(() => ({ createTime: 0n, isActive: false })),

      publicClient.readContract({
        address: merchantAddress,
        abi: ABIS.MerchantManagement,
        functionName: 'verifications',
        args: [userAddress]
      }).catch(() => ({ isVerified: false }))
    ]);

    const isRegistered = merchantData.createTime > 0;
    const isVerified = verificationData.isVerified;
    const isActive = merchantData.isActive;

    return {
      isRegistered,
      isVerified,
      isActive,
      createTime: Number(merchantData.createTime),
      status: isRegistered ? (isVerified ? (isActive ? '已认证' : '已暂停') : '待审核') : '未注册'
    };
  } catch (error) {
    console.error('🚨 [queryUserMerchantStatus] 查询失败:', error);
    return {
      isRegistered: false,
      isVerified: false,
      isActive: false,
      createTime: 0,
      status: '未注册',
      error: error.message
    };
  }
}

/**
 * 查询用户钱包余额
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 钱包余额
 */
export async function queryUserWalletBalances(userAddress) {
  try {
    console.log('💰 [queryUserWalletBalances] 查询用户钱包余额:', userAddress);

    const { CONTRACT_ADDRESSES, ABIS } = await getContractConfig();
    const qptTokenAddress = CONTRACT_ADDRESSES[97].QPTToken;
    const usdtTokenAddress = import.meta.env.VITE_USDT_ADDRESS_TESTNET;

    const [qptBalance, usdtBalance, bnbBalance] = await Promise.all([
      // QPT余额
      publicClient.readContract({
        address: qptTokenAddress,
        abi: ABIS.QPTToken,
        functionName: 'balanceOf',
        args: [userAddress]
      }).catch(() => 0n),

      // USDT余额
      publicClient.readContract({
        address: usdtTokenAddress,
        abi: ABIS.QPTToken, // 使用相同的ERC20 ABI
        functionName: 'balanceOf',
        args: [userAddress]
      }).catch(() => 0n),

      // BNB余额
      publicClient.getBalance({
        address: userAddress
      }).catch(() => 0n)
    ]);

    return {
      qpt: formatUnits(qptBalance, 18),
      usdt: formatUnits(usdtBalance, 6),
      bnb: formatUnits(bnbBalance, 18),
      rawQPT: qptBalance,
      rawUSDT: usdtBalance,
      rawBNB: bnbBalance
    };
  } catch (error) {
    console.error('🚨 [queryUserWalletBalances] 查询失败:', error);
    return {
      qpt: '0',
      usdt: '0',
      bnb: '0',
      error: error.message
    };
  }
}

/**
 * 查询用户完整数据（整合所有信息）
 * @param {string} userAddress - 用户地址
 * @returns {Promise<Object>} - 用户完整数据
 */
export async function queryUserCompleteData(userAddress) {
  try {
    console.log('📊 [queryUserCompleteData] 查询用户完整数据:', userAddress);

    if (!validateUserAddress(userAddress)) {
      throw new Error('无效的用户地址');
    }

    // 并行查询所有用户数据
    const [
      basicInfo,
      nodeStaking,
      pointsDetails,
      merchantStatus,
      walletBalances
    ] = await Promise.all([
      queryUserBasicInfo(userAddress),
      queryUserNodeStaking(userAddress),
      queryUserPointsDetails(userAddress),
      queryUserMerchantStatus(userAddress),
      queryUserWalletBalances(userAddress)
    ]);

    // 计算用户活跃度和统计数据
    const activityScore = calculateUserActivityScore({
      isRegistered: basicInfo.isRegistered,
      isStaking: nodeStaking.isStaking,
      hasPoints: parseFloat(pointsDetails.groupBuyPoints) > 0,
      isMerchant: merchantStatus.isRegistered,
      hasBalance: parseFloat(walletBalances.qpt) > 0
    });

    return {
      address: userAddress,
      formattedAddress: formatUserAddress(userAddress),
      basicInfo,
      nodeStaking,
      pointsDetails,
      merchantStatus,
      walletBalances,
      activityScore,
      summary: {
        userType: getUserType(basicInfo, merchantStatus, nodeStaking),
        totalValue: calculateTotalValue(pointsDetails, walletBalances, nodeStaking),
        riskLevel: calculateRiskLevel(basicInfo, activityScore),
        lastUpdated: new Date()
      },
      timestamp: new Date()
    };
  } catch (error) {
    console.error('🚨 [queryUserCompleteData] 查询失败:', error);
    throw new Error(`查询用户完整数据失败: ${error.message}`);
  }
}

/**
 * 计算用户活跃度评分
 * @param {Object} factors - 活跃度因素
 * @returns {number} - 活跃度评分 (0-100)
 */
function calculateUserActivityScore(factors) {
  let score = 0;

  if (factors.isRegistered) score += 20;
  if (factors.isStaking) score += 30;
  if (factors.hasPoints) score += 20;
  if (factors.isMerchant) score += 20;
  if (factors.hasBalance) score += 10;

  return Math.min(score, 100);
}

/**
 * 获取用户类型
 * @param {Object} basicInfo - 基础信息
 * @param {Object} merchantStatus - 商家状态
 * @param {Object} nodeStaking - 质押状态
 * @returns {string} - 用户类型
 */
function getUserType(basicInfo, merchantStatus, nodeStaking) {
  if (!basicInfo.isRegistered) return '未注册用户';
  if (basicInfo.isBlacklisted) return '黑名单用户';
  if (merchantStatus.isVerified) return '认证商家';
  if (merchantStatus.isRegistered) return '待审核商家';
  if (nodeStaking.isStaking) return '质押用户';
  return '普通用户';
}

/**
 * 计算用户总价值
 * @param {Object} pointsDetails - 积分详情
 * @param {Object} walletBalances - 钱包余额
 * @param {Object} nodeStaking - 质押信息
 * @returns {string} - 总价值（USDT）
 */
function calculateTotalValue(pointsDetails, walletBalances, nodeStaking) {
  // 简化计算：假设1 QPT = 1 USDT，1 积分 = 0.1 USDT
  const qptValue = parseFloat(walletBalances.qpt) + parseFloat(nodeStaking.stakedAmount);
  const pointsValue = (parseFloat(pointsDetails.groupBuyPoints) + parseFloat(pointsDetails.salesPoints)) * 0.1;
  const usdtValue = parseFloat(walletBalances.usdt);

  const totalValue = qptValue + pointsValue + usdtValue;
  return totalValue.toFixed(2);
}

/**
 * 计算风险等级
 * @param {Object} basicInfo - 基础信息
 * @param {number} activityScore - 活跃度评分
 * @returns {string} - 风险等级
 */
function calculateRiskLevel(basicInfo, activityScore) {
  if (basicInfo.isBlacklisted) return '高风险';
  if (!basicInfo.isRegistered) return '中风险';
  if (activityScore >= 80) return '低风险';
  if (activityScore >= 50) return '中风险';
  return '高风险';
}

/**
 * 批量查询用户数据
 * @param {Array<string>} userAddresses - 用户地址列表
 * @returns {Promise<Array>} - 用户数据列表
 */
export async function queryMultipleUsersData(userAddresses) {
  try {
    console.log('📊 [queryMultipleUsersData] 批量查询用户数据:', userAddresses.length);

    const validAddresses = userAddresses.filter(validateUserAddress);

    if (validAddresses.length === 0) {
      return [];
    }

    // 并行查询所有用户的基础信息
    const usersData = await Promise.allSettled(
      validAddresses.map(address => queryUserCompleteData(address))
    );

    return usersData.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          address: validAddresses[index],
          error: result.reason.message,
          timestamp: new Date()
        };
      }
    });
  } catch (error) {
    console.error('🚨 [queryMultipleUsersData] 批量查询失败:', error);
    throw new Error(`批量查询用户数据失败: ${error.message}`);
  }
}
