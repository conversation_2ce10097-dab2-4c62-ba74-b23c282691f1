/* src/components/Mall/AddressSelector.css */

.address-selector {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.address-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.address-selector-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.address-count {
  color: #666;
  font-size: 14px;
}

.address-selector-placeholder,
.address-selector-loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-size: 14px;
}

.address-selector-empty {
  text-align: center;
  padding: 40px 20px;
}

.address-selector-empty p {
  color: #666;
  margin-bottom: 16px;
}

.btn-add-address {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-add-address:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.address-item:hover {
  border-color: #e0e0e0;
  background: white;
}

.address-item.default {
  border-color: #52c41a;
  background: #f6ffed;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.recipient-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.recipient-phone {
  color: #666;
  font-size: 14px;
}

.default-badge {
  background: #52c41a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.address-detail {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.btn-set-default {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-set-default:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.btn-set-default:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
  transform: none;
}

.current-default-info {
  margin-top: 20px;
  padding: 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
}

.current-default-info h5 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 14px;
  font-weight: 600;
}

.current-default-info p {
  margin: 0;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.address-selector-note {
  margin-top: 16px;
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
}

.address-selector-note p {
  margin: 4px 0;
  color: #d46b08;
  font-size: 12px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .address-selector {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .address-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .btn-set-default {
    align-self: flex-end;
  }
  
  .address-header {
    flex-wrap: wrap;
  }
  
  .recipient-name,
  .recipient-phone {
    font-size: 14px;
  }
  
  .address-detail {
    font-size: 13px;
  }
}
