// SPDX-License-Identifier: MIT
pragma solidity ^0.8.22;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "./StorageValidator.sol";

// 完整的接口定义
interface IAgentSystem {
    function getUserInfo(address user) external view returns (
        address inviter, uint8 level, uint256 totalPerformance,
        uint256 referralsCount, bool isRegistered, uint256 personalPerformance
    );
    function hasRole(bytes32 role, address account) external view returns (bool);
    function systemAdmin() external view returns (address);
}

interface IPointsManagement {
    function groupBuyPointsNonExchangeable(address user) external view returns (uint256);
    function salesPointsExchangeable(address user) external view returns (uint256);
    function lastExchangeTime(address user) external view returns (uint256);
    function MIN_EXCHANGE_POINTS() external view returns (uint256);
    function EXCHANGE_COOLDOWN() external view returns (uint256);
    function pointsStats() external view returns (
        uint256 totalGroupBuyPoints,
        uint256 totalSalesPoints,
        uint256 totalBurnedPoints,
        uint256 totalUSDTExchanged,
        uint256 totalUSDTBalance
    );
    function getPointsDecimals() external view returns (uint256);
}

interface IProductManagement {
    function productCount() external view returns (uint256);
    function products(uint256 productId) external view returns (
        uint256 id,
        address merchant,
        string memory name,
        string memory description,
        uint256 price,
        uint256 stock,
        bool isActive,
        uint256 sales
    );
}

interface IGroupBuyRoom {
    function hasJoined(uint256 roomId, address user) external view returns (bool);
    function claimed(uint256 roomId, address user) external view returns (bool);
    function getRoom(uint256 roomId) external view returns (
        address creator, uint256 tier, address[] memory participants,
        bool isSuccessful, bool isClosed, address winner, uint256 endTime
    );
    function totalRooms() external view returns (uint256);
}

interface INodeStaking {
    function isNodeActive(address user) external view returns (bool);
    function lastClaimDate(address user) external view returns (uint256);
    function getCurrentRequiredStake() external view returns (uint256);
    function totalEffectiveNodes() external view returns (uint256);
    function MAX_NODES() external view returns (uint256);
    function totalDividends() external view returns (uint256);
    function snapshotDividends() external view returns (uint256);
    function lastSnapshotTime() external view returns (uint256);
    function dailyClaimedCount() external view returns (uint256);
    function hasClaimedTodayReward(address user) external view returns (bool);
    function getNodeActivationTime(address user) external view returns (uint256);
    function canNodeCreateSnapshot(address user) external view returns (bool, string memory);
    function getNodeAddresses() external view returns (address[] memory);
    // 定义与 NodeStaking 合约匹配的结构体
    struct UserNodeStatus {
        address user;                    // 用户地址
        bool isActive;                   // 是否为活跃节点
        uint256 lastClaimDate;           // 最后领取日期
        uint256 requiredStakeAmount;     // 当前需要质押的数量
        bool hasClaimedToday;            // 今天是否已领取
        bool canClaimToday;              // 今天是否可以领取
        uint256 dailyReward;             // 每日奖励
        uint256 totalActiveNodes;        // 总活跃节点数
        uint256 maxNodes;                // 最大节点数
        uint256 availableSlots;          // 可用节点槽位
        bool canStakeNode;               // 是否可以质押节点
        uint256 currentTime;             // 当前时间
        uint256 startTime;               // 开始时间
        uint256 daysSinceLaunch;         // 启动以来的天数
        uint256 totalDividends;          // 总分红
        uint256 snapshotDividends;       // 快照分红
        uint256 lastSnapshotTime;        // 最后快照时间
        uint256 activationTime;          // 节点激活时间
        bool canCreateSnapshot;          // 是否可以创建快照
        string snapshotRestrictionReason; // 不能创建快照的原因
    }

    function getUserNodeStatus(address user) external view returns (UserNodeStatus memory);
}

interface IQPTLocker {
    function getUserLockInfo(address user) external view returns (
        uint256 totalLocked, uint256 totalUnlocked, uint256 pendingUnlock, uint256 nextUnlockTime
    );
    function lockedNodeQPT(address user) external view returns (uint256);
    function buybackLocked(address user) external view returns (uint256);
    function getRoomInfo(uint256 roomId) external view returns (
        address creator, uint256 amount, uint256 unlockTime, bool isSuccess, bool isClaimed
    );
}



interface IQPTBuyback {
    function hasParticipated(uint256 round, address user) external view returns (bool);
    function getCurrentRound() external view returns (uint256);
}

contract SimpleQueryContract is Initializable, StorageValidator, OwnableUpgradeable, PausableUpgradeable, UUPSUpgradeable {
    
    // 合约地址
    address public agentSystemAddress;
    address public groupBuyRoomAddress;
    address public pointsManagementAddress;
    address public nodeStakingAddress;
    address public qptLockerAddress;
    address public qptBuybackAddress;

    // 角色常量
    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant PERFORMANCE_UPLOADER = keccak256("PERFORMANCE_UPLOADER");

    function initialize(
        address _agentSystem,
        address _groupBuyRoom,
        address _pointsManagement,
        address _nodeStaking,
        address _qptLocker,
        address _qptBuyback,
        address _productManagement
    ) public initializer {
        __Ownable_init();
        __Pausable_init();
        __UUPSUpgradeable_init();

        agentSystemAddress = _agentSystem;
        groupBuyRoomAddress = _groupBuyRoom;
        pointsManagementAddress = _pointsManagement;
        nodeStakingAddress = _nodeStaking;
        qptLockerAddress = _qptLocker;
        qptBuybackAddress = _qptBuyback;
        productManagementAddress = _productManagement;
    }

    // Timelock 控制
    address public timelock;

    // 新增变量（放在所有现有变量之后）
    address public productManagementAddress;

    /**
     * @notice 设置商品管理合约地址（仅限管理员）
     * @param _productManagement 商品管理合约地址
     */
    function setProductManagementAddress(address _productManagement) external onlyOwner {
        productManagementAddress = _productManagement;
        emit AddressUpdated("ProductManagement", _productManagement);
    }

    event AddressUpdated(string contractName, address newAddress);

    modifier onlyTimelock() {
        require(msg.sender == timelock, "Only Timelock");
        _;
    }

    // —— 存储验证实现 —— //

    function validateStorageLayout() public view override returns (bool) {
        // 验证查询合约基本状态
        if (agentSystemAddress == address(0)) return false;
        if (groupBuyRoomAddress == address(0)) return false;
        if (address(timelock) == address(0)) return false;

        return true;
    }

    function calculateStorageChecksum() public view override returns (bytes32) {
        return keccak256(abi.encodePacked(
            agentSystemAddress,
            groupBuyRoomAddress,
            address(timelock),
            STORAGE_LAYOUT_VERSION
        ));
    }

    function emergencyStorageFix() external override onlyOwner whenPaused {
        // SimpleQueryContract的存储修复逻辑
        emit StorageFixed(address(this), "SimpleQueryContract storage checked");
    }

    function savePreUpgradeState() external override onlyOwner {
        // 保存升级前状态
        // 可以在这里添加特定的状态保存逻辑
    }

    function setEmergencyFixMode(bool enabled) external override onlyOwner {
        // 设置紧急修复模式
        _emergencyFixMode = enabled;
    }

    function postUpgradeValidation() external override onlyOwner {
        // 升级后验证
        require(validateStorageLayout(), "Storage validation failed");
        require(validateBasicState(), "Basic state validation failed");
    }

    function _authorizeUpgrade(address newImplementation) internal override {
        _authorizeUpgradeWithValidation(newImplementation);
    }

    /// @notice 初始化Timelock地址（只能调用一次）
    function initializeTimelock(address _timelock) external onlyOwner {
        require(timelock == address(0), "Timelock already initialized");
        require(_timelock != address(0), "Zero address");
        timelock = _timelock;
    }

    /// @notice 获取用户完整状态信息
    function getUserCompleteStatus(address user) external view returns (UserCompleteStatus memory status) {
        status.user = user;
        
        // 代理系统信息
        if (agentSystemAddress != address(0)) {
            try IAgentSystem(agentSystemAddress).getUserInfo(user) returns (
                address inviter, uint8 level, uint256 totalPerformance,
                uint256 referralsCount, bool isRegistered, uint256 personalPerformance
            ) {
                status.agentInfo = AgentInfo({
                    inviter: inviter, level: level, totalPerformance: totalPerformance,
                    referralsCount: referralsCount, isRegistered: isRegistered,
                    personalPerformance: personalPerformance, hasAdminRole: false,
                    hasUploaderRole: false, isSystemAdmin: false
                });
                
                try IAgentSystem(agentSystemAddress).hasRole(ADMIN_ROLE, user) returns (bool hasAdmin) {
                    status.agentInfo.hasAdminRole = hasAdmin;
                } catch {}
                
                try IAgentSystem(agentSystemAddress).hasRole(PERFORMANCE_UPLOADER, user) returns (bool hasUploader) {
                    status.agentInfo.hasUploaderRole = hasUploader;
                } catch {}
                
                try IAgentSystem(agentSystemAddress).systemAdmin() returns (address admin) {
                    status.agentInfo.isSystemAdmin = (admin == user);
                } catch {}
            } catch {}
        }

        // 积分信息
        if (pointsManagementAddress != address(0)) {
            try IPointsManagement(pointsManagementAddress).groupBuyPointsNonExchangeable(user) returns (uint256 groupBuyPoints) {
                uint256 salesPoints = 0;
                uint256 lastExchange = 0;
                uint256 minExchange = 0;
                uint256 cooldown = 0;
                
                try IPointsManagement(pointsManagementAddress).salesPointsExchangeable(user) returns (uint256 sales) {
                    salesPoints = sales;
                } catch {}
                
                try IPointsManagement(pointsManagementAddress).lastExchangeTime(user) returns (uint256 last) {
                    lastExchange = last;
                } catch {}
                
                try IPointsManagement(pointsManagementAddress).MIN_EXCHANGE_POINTS() returns (uint256 min) {
                    minExchange = min;
                } catch {}
                
                try IPointsManagement(pointsManagementAddress).EXCHANGE_COOLDOWN() returns (uint256 cd) {
                    cooldown = cd;
                } catch {}
                
                uint256 cooldownRemaining = 0;
                if (lastExchange > 0 && block.timestamp < lastExchange + cooldown) {
                    cooldownRemaining = lastExchange + cooldown - block.timestamp;
                }
                
                status.pointsInfo = PointsInfo({
                    groupBuyPoints: groupBuyPoints, salesPoints: salesPoints,
                    lastExchangeTime: lastExchange, canExchangeGroupBuy: groupBuyPoints > 0,
                    canExchangeSales: salesPoints >= minExchange, canExchangeNow: cooldownRemaining == 0,
                    exchangeCooldownRemaining: cooldownRemaining
                });
            } catch {}
        }

        // 节点信息 - 使用getUserNodeStatus获取完整信息
        if (nodeStakingAddress != address(0)) {
            try INodeStaking(nodeStakingAddress).getUserNodeStatus(user) returns (
                INodeStaking.UserNodeStatus memory nodeStatus
            ) {
                // 获取额外的分红信息
                uint256 dailyClaimedCount = 0;
                uint256 stakedAmount = 0;

                try INodeStaking(nodeStakingAddress).dailyClaimedCount() returns (uint256 claimed) {
                    dailyClaimedCount = claimed;
                } catch {}

                // 获取用户质押数量
                if (qptLockerAddress != address(0)) {
                    try IQPTLocker(qptLockerAddress).lockedNodeQPT(user) returns (uint256 locked) {
                        stakedAmount = locked;
                    } catch {}
                }

                status.nodeInfo = NodeInfo({
                    isActive: nodeStatus.isActive,
                    lastClaimDate: nodeStatus.lastClaimDate,
                    requiredStakeAmount: nodeStatus.requiredStakeAmount,
                    hasClaimedToday: nodeStatus.hasClaimedToday,
                    canClaimToday: nodeStatus.canClaimToday,
                    totalActiveNodes: nodeStatus.totalActiveNodes,
                    maxNodes: nodeStatus.maxNodes,
                    canStakeNode: nodeStatus.canStakeNode,
                    dailyReward: nodeStatus.dailyReward,
                    totalDividends: nodeStatus.totalDividends,
                    snapshotDividends: nodeStatus.snapshotDividends,
                    lastSnapshotTime: nodeStatus.lastSnapshotTime,
                    dailyClaimedCount: dailyClaimedCount,
                    stakedAmount: stakedAmount,
                    activationTime: nodeStatus.activationTime,
                    canCreateSnapshot: nodeStatus.canCreateSnapshot,
                    snapshotRestrictionReason: nodeStatus.snapshotRestrictionReason
                });
            } catch {
                // 如果getUserNodeStatus失败，使用原来的方式
                try INodeStaking(nodeStakingAddress).isNodeActive(user) returns (bool isActive) {
                    uint256 lastClaim = 0;
                    uint256 requiredStake = 0;
                    uint256 totalNodes = 0;
                    uint256 maxNodes = 0;

                    try INodeStaking(nodeStakingAddress).lastClaimDate(user) returns (uint256 last) {
                        lastClaim = last;
                    } catch {}

                    try INodeStaking(nodeStakingAddress).getCurrentRequiredStake() returns (uint256 required) {
                        requiredStake = required;
                    } catch {}

                    try INodeStaking(nodeStakingAddress).totalEffectiveNodes() returns (uint256 total) {
                        totalNodes = total;
                    } catch {}

                    try INodeStaking(nodeStakingAddress).MAX_NODES() returns (uint256 max) {
                        maxNodes = max;
                    } catch {}

                    uint256 today = block.timestamp / 1 days;
                    bool hasClaimedToday = (lastClaim == today);

                    status.nodeInfo = NodeInfo({
                        isActive: isActive,
                        lastClaimDate: lastClaim,
                        requiredStakeAmount: requiredStake,
                        hasClaimedToday: hasClaimedToday,
                        canClaimToday: isActive && !hasClaimedToday,
                        totalActiveNodes: totalNodes,
                        maxNodes: maxNodes,
                        canStakeNode: !isActive && totalNodes < maxNodes,
                        dailyReward: 0,
                        totalDividends: 0,
                        snapshotDividends: 0,
                        lastSnapshotTime: 0,
                        dailyClaimedCount: 0,
                        stakedAmount: 0,
                        activationTime: 0,
                        canCreateSnapshot: false,
                        snapshotRestrictionReason: "Fallback mode - no activation time data"
                    });
                } catch {}
            }
        }

        // 锁仓信息
        if (qptLockerAddress != address(0)) {
            try IQPTLocker(qptLockerAddress).getUserLockInfo(user) returns (
                uint256 totalLocked, uint256 totalUnlocked, uint256 pendingUnlock, uint256 nextUnlockTime
            ) {
                status.lockerInfo = LockerInfo({
                    totalLocked: totalLocked, totalUnlocked: totalUnlocked,
                    pendingUnlock: pendingUnlock, nextUnlockTime: nextUnlockTime,
                    canUnlock: block.timestamp >= nextUnlockTime && pendingUnlock > 0
                });
            } catch {}
        }

        // 回购信息
        if (qptBuybackAddress != address(0)) {
            try IQPTBuyback(qptBuybackAddress).getCurrentRound() returns (uint256 currentRound) {
                bool hasParticipated = false;
                try IQPTBuyback(qptBuybackAddress).hasParticipated(currentRound, user) returns (bool participated) {
                    hasParticipated = participated;
                } catch {}
                
                status.buybackInfo = BuybackInfo({
                    currentRound: currentRound,
                    hasParticipatedCurrentRound: hasParticipated
                });
            } catch {}
        }
    }

    /// @notice 获取用户在指定房间的角色信息
    function getUserRoleInRoom(uint256 roomId, address user) external view returns (RoomRoleInfo memory roleInfo) {
        roleInfo.user = user;
        roleInfo.roomId = roomId;
        
        if (groupBuyRoomAddress != address(0)) {
            try IGroupBuyRoom(groupBuyRoomAddress).hasJoined(roomId, user) returns (bool hasJoined) {
                roleInfo.hasJoined = hasJoined;
                
                try IGroupBuyRoom(groupBuyRoomAddress).claimed(roomId, user) returns (bool claimed) {
                    roleInfo.hasClaimed = claimed;
                } catch {}
                
                try IGroupBuyRoom(groupBuyRoomAddress).getRoom(roomId) returns (
                    address creator, uint256, address[] memory participants,
                    bool isSuccessful, bool isClosed, address winner, uint256
                ) {
                    roleInfo.isCreator = (creator == user);
                    roleInfo.isParticipant = hasJoined;
                    roleInfo.isWinner = (winner == user);
                    roleInfo.roomClosed = isClosed;
                    roleInfo.roomSuccessful = isSuccessful;
                    roleInfo.winnerAddress = winner;
                    
                    for (uint256 i = 0; i < participants.length; i++) {
                        if (participants[i] == user) {
                            roleInfo.participantIndex = i;
                            break;
                        }
                    }
                    
                    if (isClosed && !roleInfo.hasClaimed) {
                        if (isSuccessful) {
                            roleInfo.canClaim = hasJoined;
                        } else {
                            roleInfo.canClaim = hasJoined && !roleInfo.isCreator;
                        }
                    }
                } catch {}
            } catch {}
        }
    }

    /// @notice 检查用户领取权限
    function checkClaimEligibility(uint256 roomId, address user) external view returns (bool canClaim, string memory reason) {
        if (groupBuyRoomAddress == address(0)) {
            return (false, "GroupBuyRoom contract not set");
        }

        try IGroupBuyRoom(groupBuyRoomAddress).hasJoined(roomId, user) returns (bool hasJoined) {
            if (!hasJoined) return (false, "Not a participant");

            try IGroupBuyRoom(groupBuyRoomAddress).claimed(roomId, user) returns (bool claimed) {
                if (claimed) return (false, "Already claimed");

                try IGroupBuyRoom(groupBuyRoomAddress).getRoom(roomId) returns (
                    address creator, uint256, address[] memory, bool isSuccessful, bool isClosed, address, uint256
                ) {
                    if (!isClosed) return (false, "Room not closed yet");

                    if (isSuccessful) {
                        return (true, "Can claim reward");
                    } else {
                        if (creator == user) {
                            return (false, "Creator cannot claim refund");
                        } else {
                            return (true, "Can claim refund");
                        }
                    }
                } catch {
                    return (false, "Failed to get room info");
                }
            } catch {
                return (false, "Failed to check claim status");
            }
        } catch {
            return (false, "Failed to check participation");
        }
    }

    /// @notice 获取拼团房间统计数据
    function getGroupBuyStats(uint256 maxRoomsToCheck) external view returns (GroupBuyStats memory) {
        if (groupBuyRoomAddress == address(0)) {
            return GroupBuyStats({
                totalRooms: 0,
                activeRooms: 0,
                completedRooms: 0,
                totalParticipants: 0
            });
        }

        uint256 totalRooms = IGroupBuyRoom(groupBuyRoomAddress).totalRooms();
        uint256 activeRooms = 0;
        uint256 completedRooms = 0;
        uint256 totalParticipants = 0;

        uint256 roomsToCheck = totalRooms > maxRoomsToCheck ? maxRoomsToCheck : totalRooms;

        // 注意：房间ID从0开始，totalRooms返回的是nextRoomId（下一个要分配的ID）
        for (uint256 i = 0; i < roomsToCheck; i++) {
            try IGroupBuyRoom(groupBuyRoomAddress).getRoom(i) returns (
                address creator, uint256 tier, address[] memory participants,
                bool isSuccessful, bool isClosed, address winner, uint256 endTime
            ) {
                // 只统计有效房间（有创建者且tier > 0）
                if (creator != address(0) && tier > 0) {
                    totalParticipants += participants.length;

                    if (isClosed) {
                        completedRooms++;
                    } else {
                        activeRooms++;
                    }
                }
            } catch {
                // 忽略查询失败的房间
            }
        }

        return GroupBuyStats({
            totalRooms: totalRooms,
            activeRooms: activeRooms,
            completedRooms: completedRooms,
            totalParticipants: totalParticipants
        });
    }

    // 管理函数
    function updateContractAddresses(
        address _agentSystem,
        address _groupBuyRoom,
        address _pointsManagement,
        address _nodeStaking,
        address _qptLocker,
        address _qptBuyback
    ) external onlyOwner {
        agentSystemAddress = _agentSystem;
        groupBuyRoomAddress = _groupBuyRoom;
        pointsManagementAddress = _pointsManagement;
        nodeStakingAddress = _nodeStaking;
        qptLockerAddress = _qptLocker;
        qptBuybackAddress = _qptBuyback;
    }

    // 结构体定义
    struct UserCompleteStatus {
        address user;
        AgentInfo agentInfo;
        PointsInfo pointsInfo;
        NodeInfo nodeInfo;
        LockerInfo lockerInfo;
        BuybackInfo buybackInfo;
    }

    struct AgentInfo {
        address inviter;
        uint8 level;
        uint256 totalPerformance;
        uint256 referralsCount;
        bool isRegistered;
        uint256 personalPerformance;
        bool hasAdminRole;
        bool hasUploaderRole;
        bool isSystemAdmin;
    }

    struct PointsInfo {
        uint256 groupBuyPoints;
        uint256 salesPoints;
        uint256 lastExchangeTime;
        bool canExchangeGroupBuy;
        bool canExchangeSales;
        bool canExchangeNow;
        uint256 exchangeCooldownRemaining;
    }

    struct NodeInfo {
        bool isActive;
        uint256 lastClaimDate;
        uint256 requiredStakeAmount;
        bool hasClaimedToday;
        bool canClaimToday;
        uint256 totalActiveNodes;
        uint256 maxNodes;
        bool canStakeNode;
        uint256 dailyReward;
        uint256 totalDividends;
        uint256 snapshotDividends;
        uint256 lastSnapshotTime;
        uint256 dailyClaimedCount;
        uint256 stakedAmount;
        uint256 activationTime;
        bool canCreateSnapshot;
        string snapshotRestrictionReason;
    }

    struct NodeStakingStats {
        uint256 totalStakedQPT;
        uint256 totalActiveNodes;
        uint256 currentRequiredStake;
        uint256 maxNodes;
        uint256 averageStakeAmount;
    }

    struct LockingStats {
        uint256 totalNodeStaked;
        uint256 totalGroupBuyLocked;
        uint256 totalBuybackLocked;
        uint256 totalLocked;
        uint256 activeGroupBuyRooms;
        uint256 activeBuybackRooms;
    }

    struct LockerInfo {
        uint256 totalLocked;
        uint256 totalUnlocked;
        uint256 pendingUnlock;
        uint256 nextUnlockTime;
        bool canUnlock;
    }

    struct BuybackInfo {
        uint256 currentRound;
        bool hasParticipatedCurrentRound;
    }

    struct RoomRoleInfo {
        address user;
        uint256 roomId;
        bool hasJoined;
        bool hasClaimed;
        bool isCreator;
        bool isParticipant;
        bool isWinner;
        bool canClaim;
        uint256 participantIndex;
        bool roomClosed;
        bool roomSuccessful;
        address winnerAddress;
    }

    struct GroupBuyStats {
        uint256 totalRooms;
        uint256 activeRooms;
        uint256 completedRooms;
        uint256 totalParticipants;
    }

    struct ProductInfo {
        uint256 productId;
        address merchant;
        string name;
        string description;
        uint256 price;
        uint256 formattedPrice; // 修复精度后的价格
        uint256 stock;
        bool isActive;
        uint256 sales;
    }

    struct ProductStats {
        uint256 totalProducts;
        uint256 activeProducts;
        uint256 totalSales;
        uint256 averagePrice;
    }

    struct PointsSystemStats {
        uint256 totalGroupBuyPoints;
        uint256 totalSalesPoints;
        uint256 totalBurnedPoints;
        uint256 totalUSDTExchanged;
        uint256 totalUSDTBalance;
        uint256 pointsDecimals;
        uint256 minExchangePoints;
        uint256 totalSupply;
    }

    /**
     * @notice 计算实际的节点质押总量
     * @dev 查询每个节点的实际锁仓数量，而不是使用硬编码计算
     * @return totalStaked 实际的节点质押总量
     */
    function _calculateActualNodeStaked() internal view returns (uint256 totalStaked) {
        if (qptLockerAddress == address(0) || nodeStakingAddress == address(0)) {
            return 0;
        }

        try INodeStaking(nodeStakingAddress).getNodeAddresses() returns (address[] memory nodeAddresses) {
            // 查询每个节点的实际锁仓数量
            for (uint256 i = 0; i < nodeAddresses.length; i++) {
                try IQPTLocker(qptLockerAddress).lockedNodeQPT(nodeAddresses[i]) returns (uint256 lockedAmount) {
                    totalStaked += lockedAmount;
                } catch {
                    // 忽略单个节点查询失败，继续查询其他节点
                }
            }
        } catch {
            // 如果无法获取节点地址列表，返回0
            totalStaked = 0;
        }

        return totalStaked;
    }

    /**
     * @notice 获取节点质押统计数据
     * @return NodeStakingStats 节点质押统计信息
     */
    function getNodeStakingStats() external view returns (NodeStakingStats memory) {
        uint256 totalActiveNodes = INodeStaking(nodeStakingAddress).totalEffectiveNodes();
        uint256 currentRequiredStake = INodeStaking(nodeStakingAddress).getCurrentRequiredStake();
        uint256 maxNodes = INodeStaking(nodeStakingAddress).MAX_NODES();

        // 使用实际的锁仓数量，而不是理论计算值
        uint256 totalStakedQPT = _calculateActualNodeStaked();

        uint256 averageStakeAmount = totalActiveNodes > 0 ? totalStakedQPT / totalActiveNodes : 0;

        return NodeStakingStats({
            totalStakedQPT: totalStakedQPT,
            totalActiveNodes: totalActiveNodes,
            currentRequiredStake: currentRequiredStake,
            maxNodes: maxNodes,
            averageStakeAmount: averageStakeAmount
        });
    }

    /**
     * @notice 获取综合锁仓统计数据
     * @param maxRoomsToCheck 最大检查房间数（避免gas超限）
     * @return LockingStats 锁仓统计信息
     */
    function getLockingStats(uint256 maxRoomsToCheck) external view returns (LockingStats memory) {
        // 使用实际的节点质押总量，而不是理论计算值
        uint256 totalNodeStaked = _calculateActualNodeStaked();

        // 统计拼团锁仓（遍历房间）
        uint256 totalGroupBuyLocked = 0;
        uint256 activeGroupBuyRooms = 0;
        uint256 totalRooms = IGroupBuyRoom(groupBuyRoomAddress).totalRooms();
        uint256 roomsToCheck = totalRooms > maxRoomsToCheck ? maxRoomsToCheck : totalRooms;

        // 注意：房间ID从0开始，totalRooms返回的是nextRoomId（下一个要分配的ID）
        for (uint256 i = 0; i < roomsToCheck; i++) {
            try IQPTLocker(qptLockerAddress).getRoomInfo(i) returns (
                address creator, uint256 amount, uint256 unlockTime, bool isSuccess, bool isClaimed
            ) {
                if (creator != address(0) && amount > 0) {
                    totalGroupBuyLocked += amount;
                    if (!isClaimed && !isSuccess) {
                        activeGroupBuyRooms++;
                    }
                }
            } catch {
                // 忽略查询失败的房间
            }
        }

        // QPT回购锁仓统计（暂时设为0，需要具体实现）
        uint256 totalBuybackLocked = 0;
        uint256 activeBuybackRooms = 0;

        return LockingStats({
            totalNodeStaked: totalNodeStaked,
            totalGroupBuyLocked: totalGroupBuyLocked,
            totalBuybackLocked: totalBuybackLocked,
            totalLocked: totalNodeStaked + totalGroupBuyLocked + totalBuybackLocked,
            activeGroupBuyRooms: activeGroupBuyRooms,
            activeBuybackRooms: activeBuybackRooms
        });
    }

    /**
     * @notice 获取积分系统完整统计数据
     * @return PointsSystemStats 积分系统统计信息
     */
    function getPointsSystemStats() external view returns (PointsSystemStats memory) {
        if (pointsManagementAddress == address(0)) {
            return PointsSystemStats({
                totalGroupBuyPoints: 0,
                totalSalesPoints: 0,
                totalBurnedPoints: 0,
                totalUSDTExchanged: 0,
                totalUSDTBalance: 0,
                pointsDecimals: 6,
                minExchangePoints: 1000000,
                totalSupply: 0
            });
        }

        try IPointsManagement(pointsManagementAddress).pointsStats() returns (
            uint256 totalGroupBuyPoints,
            uint256 totalSalesPoints,
            uint256 totalBurnedPoints,
            uint256 totalUSDTExchanged,
            uint256 totalUSDTBalance
        ) {
            uint256 pointsDecimals = 6; // 默认值
            uint256 minExchangePoints = 1000000; // 默认值

            try IPointsManagement(pointsManagementAddress).getPointsDecimals() returns (uint256 decimals) {
                pointsDecimals = decimals;
            } catch {}

            try IPointsManagement(pointsManagementAddress).MIN_EXCHANGE_POINTS() returns (uint256 minPoints) {
                minExchangePoints = minPoints;
            } catch {}

            uint256 totalSupply = totalGroupBuyPoints + totalSalesPoints - totalBurnedPoints;

            return PointsSystemStats({
                totalGroupBuyPoints: totalGroupBuyPoints,
                totalSalesPoints: totalSalesPoints,
                totalBurnedPoints: totalBurnedPoints,
                totalUSDTExchanged: totalUSDTExchanged,
                totalUSDTBalance: totalUSDTBalance,
                pointsDecimals: pointsDecimals,
                minExchangePoints: minExchangePoints,
                totalSupply: totalSupply
            });
        } catch {
            return PointsSystemStats({
                totalGroupBuyPoints: 0,
                totalSalesPoints: 0,
                totalBurnedPoints: 0,
                totalUSDTExchanged: 0,
                totalUSDTBalance: 0,
                pointsDecimals: 6,
                minExchangePoints: 1000000,
                totalSupply: 0
            });
        }
    }

    /**
     * @notice 批量获取商品信息（带价格修复）
     * @param productIds 商品ID数组
     * @return ProductInfo[] 商品信息数组
     */
    function batchGetProducts(uint256[] calldata productIds) external view returns (ProductInfo[] memory) {
        ProductInfo[] memory products = new ProductInfo[](productIds.length);

        if (productManagementAddress == address(0)) {
            return products; // 返回空数组
        }

        for (uint256 i = 0; i < productIds.length; i++) {
            try IProductManagement(productManagementAddress).products(productIds[i]) returns (
                uint256 id,
                address merchant,
                string memory name,
                string memory description,
                uint256 price,
                uint256 stock,
                bool isActive,
                uint256 sales
            ) {
                // 价格精度修复：自动检测并修复精度问题
                uint256 formattedPrice = _fixPricePrecision(price);

                products[i] = ProductInfo({
                    productId: id,
                    merchant: merchant,
                    name: name,
                    description: description,
                    price: price,
                    formattedPrice: formattedPrice,
                    stock: stock,
                    isActive: isActive,
                    sales: sales
                });
            } catch {
                // 查询失败时返回默认值
                products[i] = ProductInfo({
                    productId: productIds[i],
                    merchant: address(0),
                    name: "",
                    description: "",
                    price: 0,
                    formattedPrice: 0,
                    stock: 0,
                    isActive: false,
                    sales: 0
                });
            }
        }

        return products;
    }

    /**
     * @notice 获取商品统计数据
     * @param maxProductsToCheck 最大检查商品数量
     * @return ProductStats 商品统计信息
     */
    function getProductStats(uint256 maxProductsToCheck) external view returns (ProductStats memory) {
        if (productManagementAddress == address(0)) {
            return ProductStats({
                totalProducts: 0,
                activeProducts: 0,
                totalSales: 0,
                averagePrice: 0
            });
        }

        uint256 totalProducts = 0;
        uint256 activeProducts = 0;
        uint256 totalSales = 0;
        uint256 totalPriceSum = 0;

        try IProductManagement(productManagementAddress).productCount() returns (uint256 count) {
            totalProducts = count;
            uint256 productsToCheck = count > maxProductsToCheck ? maxProductsToCheck : count;

            for (uint256 i = 1; i <= productsToCheck; i++) {
                try IProductManagement(productManagementAddress).products(i) returns (
                    uint256, address, string memory, string memory,
                    uint256 price, uint256, bool isActive, uint256 sales
                ) {
                    if (isActive) {
                        activeProducts++;
                        totalSales += sales;
                        totalPriceSum += _fixPricePrecision(price);
                    }
                } catch {
                    // 忽略查询失败的商品
                }
            }
        } catch {}

        uint256 averagePrice = activeProducts > 0 ? totalPriceSum / activeProducts : 0;

        return ProductStats({
            totalProducts: totalProducts,
            activeProducts: activeProducts,
            totalSales: totalSales,
            averagePrice: averagePrice
        });
    }

    /**
     * @dev 修复价格精度问题 - 统一使用6位小数精度
     * @param originalPrice 原始价格
     * @return 修复后的价格（6位精度显示值）
     */
    function _fixPricePrecision(uint256 originalPrice) internal pure returns (uint256) {
        if (originalPrice == 0) {
            return 0;
        }

        // 统一价格精度处理：目标是6位小数精度
        // 如果价格过大（可能是18位精度），转换为6位精度显示值
        if (originalPrice > 1e15) { // 大于 1000 * 10^12，可能是18位精度
            return originalPrice / 1e12; // 从18位转换为6位，再除以10^6得到显示值
        }

        // 如果价格在6位精度范围内（1,000,000 到 1e15），转换为显示值
        if (originalPrice > 1000000) {
            return originalPrice / 1000000; // 从6位精度转换为显示值
        }

        // 如果价格在显示值范围内（小于1,000,000），直接返回
        return originalPrice;
    }

    uint256[49] private __gap; // 减少1个slot，因为添加了productManagementAddress
}
