{"_format": "hh-sol-artifact-1", "contractName": "IAddressManagement", "sourceName": "contracts/interfaces/IAddressManagement.sol", "abi": [{"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "addressId", "type": "uint256"}], "name": "get<PERSON><PERSON><PERSON>", "outputs": [{"components": [{"internalType": "uint256", "name": "addressId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "phone", "type": "string"}, {"internalType": "string", "name": "province", "type": "string"}, {"internalType": "string", "name": "city", "type": "string"}, {"internalType": "string", "name": "district", "type": "string"}, {"internalType": "string", "name": "detail", "type": "string"}, {"internalType": "bool", "name": "isDefault", "type": "bool"}, {"internalType": "uint256", "name": "createTime", "type": "uint256"}], "internalType": "struct AddressInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getDefaultAddress", "outputs": [{"components": [{"internalType": "uint256", "name": "addressId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "phone", "type": "string"}, {"internalType": "string", "name": "province", "type": "string"}, {"internalType": "string", "name": "city", "type": "string"}, {"internalType": "string", "name": "district", "type": "string"}, {"internalType": "string", "name": "detail", "type": "string"}, {"internalType": "bool", "name": "isDefault", "type": "bool"}, {"internalType": "uint256", "name": "createTime", "type": "uint256"}], "internalType": "struct AddressInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMyAddresses", "outputs": [{"components": [{"internalType": "uint256", "name": "addressId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "phone", "type": "string"}, {"internalType": "string", "name": "province", "type": "string"}, {"internalType": "string", "name": "city", "type": "string"}, {"internalType": "string", "name": "district", "type": "string"}, {"internalType": "string", "name": "detail", "type": "string"}, {"internalType": "bool", "name": "isDefault", "type": "bool"}, {"internalType": "uint256", "name": "createTime", "type": "uint256"}], "internalType": "struct AddressInfo[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getMyDefaultAddress", "outputs": [{"components": [{"internalType": "uint256", "name": "addressId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "phone", "type": "string"}, {"internalType": "string", "name": "province", "type": "string"}, {"internalType": "string", "name": "city", "type": "string"}, {"internalType": "string", "name": "district", "type": "string"}, {"internalType": "string", "name": "detail", "type": "string"}, {"internalType": "bool", "name": "isDefault", "type": "bool"}, {"internalType": "uint256", "name": "createTime", "type": "uint256"}], "internalType": "struct AddressInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}