// scripts/transfer-AddressManagement-ownership.js
// 将 AddressManagement 合约的所有权转移给 Timelock
require("dotenv").config();
const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 转移 AddressManagement 所有权给 Timelock");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 1. 获取当前账户
    const [signer] = await ethers.getSigners();
    console.log("📝 操作者地址:", signer.address);

    // 2. 读取合约地址
    const addressManagementAddr = process.env.ADDRESS_MANAGEMENT_ADDRESS;
    const timelockAddr = process.env.SECURE_TIMELOCK_ADDRESS;

    console.log("\n📋 合约地址:");
    console.log("   • AddressManagement:", addressManagementAddr);
    console.log("   • Timelock:", timelockAddr);

    // 3. 验证地址
    if (!addressManagementAddr) {
      throw new Error("请在 .env 文件中设置 ADDRESS_MANAGEMENT_ADDRESS");
    }
    if (!timelockAddr) {
      throw new Error("请在 .env 文件中设置 SECURE_TIMELOCK_ADDRESS");
    }

    // 4. 连接到合约
    const AddressManagement = await ethers.getContractFactory("AddressManagement");
    const addressMgmt = AddressManagement.attach(addressManagementAddr);

    // 5. 检查当前状态
    console.log("\n🔍 检查当前状态:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const currentOwner = await addressMgmt.owner();
    console.log("   • 当前所有者:", currentOwner);
    console.log("   • 目标所有者:", timelockAddr);
    console.log("   • 操作者:", signer.address);

    // 6. 验证权限
    if (currentOwner.toLowerCase() !== signer.address.toLowerCase()) {
      throw new Error(`当前账户不是合约所有者。当前所有者: ${currentOwner}`);
    }

    if (currentOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("\n✅ 所有权已经是 Timelock，无需转移");
      return;
    }

    // 7. 检查授权状态（可选验证）
    console.log("\n🔍 验证授权状态:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const productManagementAddr = process.env.PRODUCT_MANAGEMENT_ADDRESS;
    if (productManagementAddr) {
      const isAuthorized = await addressMgmt.authorizedContracts(productManagementAddr);
      console.log("   • ProductManagement 授权状态:", isAuthorized ? "✅ 已授权" : "❌ 未授权");
      
      if (!isAuthorized) {
        console.log("\n⚠️ 警告: ProductManagement 尚未被授权");
        console.log("💡 建议先完成授权再转移所有权");
        console.log("💡 运行: npx hardhat run scripts/authorize-ProductManagement.js --network bscTestnet");
        
        // 询问是否继续
        console.log("\n❓ 是否仍要继续转移所有权？");
        console.log("   转移后将需要通过多签+Timelock流程进行授权");
      }
    }

    // 8. 执行所有权转移
    console.log("\n🔄 执行所有权转移:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    console.log("   ⏳ 正在转移所有权...");
    const transferTx = await addressMgmt.transferOwnership(timelockAddr);
    
    console.log("   📤 交易已提交:", transferTx.hash);
    const receipt = await transferTx.wait();
    console.log("   ✅ 交易已确认，区块:", receipt.blockNumber);

    // 9. 验证转移结果
    console.log("\n🔍 验证转移结果:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const newOwner = await addressMgmt.owner();
    console.log("   • 新所有者:", newOwner);
    
    if (newOwner.toLowerCase() === timelockAddr.toLowerCase()) {
      console.log("   ✅ 所有权转移成功!");
    } else {
      console.log("   ❌ 所有权转移失败");
      throw new Error("所有权转移验证失败");
    }

    // 10. 总结
    console.log("\n🎉 所有权转移完成!");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("✅ AddressManagement 现在由 Timelock 控制");
    console.log("✅ 所有后续操作需要通过多签+Timelock流程");
    console.log("🔒 合约安全性已提升到最高级别");

    console.log("\n📝 后续操作说明:");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    console.log("• 如需授权新合约，使用多签+Timelock流程");
    console.log("• 如需修改配置，使用多签+Timelock流程");
    console.log("• 如需升级合约，使用多签+Timelock流程");

  } catch (error) {
    console.error("❌ 所有权转移失败:", error);

    if (error.message.includes("Ownable: caller is not the owner")) {
      console.log("💡 提示: 当前账户不是合约所有者");
    } else if (error.message.includes("Ownable: new owner is the zero address")) {
      console.log("💡 提示: 目标地址不能是零地址");
    }

    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 脚本执行失败:", error);
    process.exit(1);
  });
