// 图片URL优化工具
// 解决合约中图片URL长度限制问题

/**
 * 合约限制常量
 */
export const CONTRACT_LIMITS = {
  MAX_IMAGE_HASH_LEN: 200, // 合约中的限制
  MAX_IMAGES: 5,           // 最大图片数量
  SAFE_LENGTH: 180         // 安全长度，留一些余量
};

/**
 * 检查图片URL是否超过合约限制
 * @param {string[]} imageUrls - 图片URL数组
 * @returns {Object} 检查结果
 */
export function checkImageUrlLengths(imageUrls) {
  const results = {
    valid: true,
    totalImages: imageUrls.length,
    oversizedImages: [],
    maxLength: 0,
    suggestions: []
  };

  imageUrls.forEach((url, index) => {
    const length = url.length;
    results.maxLength = Math.max(results.maxLength, length);

    if (length > CONTRACT_LIMITS.MAX_IMAGE_HASH_LEN) {
      results.valid = false;
      results.oversizedImages.push({
        index,
        url,
        length,
        excess: length - CONTRACT_LIMITS.MAX_IMAGE_HASH_LEN
      });
    }
  });

  // 生成建议
  if (!results.valid) {
    results.suggestions.push('图片URL超过合约限制，需要优化');
    results.suggestions.push('建议使用更短的IPFS网关或压缩图片');
  }

  if (results.maxLength > CONTRACT_LIMITS.SAFE_LENGTH) {
    results.suggestions.push('建议将URL长度控制在180字符以内');
  }

  return results;
}

/**
 * 优化IPFS URL长度
 * @param {string} ipfsUrl - 原始IPFS URL
 * @returns {string} 优化后的URL
 */
export function optimizeIpfsUrl(ipfsUrl) {
  // 如果不是IPFS URL，直接返回
  if (!ipfsUrl.includes('ipfs')) {
    return ipfsUrl;
  }

  // 提取IPFS哈希
  let hash = '';
  
  // 处理不同格式的IPFS URL
  if (ipfsUrl.includes('/ipfs/')) {
    hash = ipfsUrl.split('/ipfs/')[1];
  } else if (ipfsUrl.includes('ipfs://')) {
    hash = ipfsUrl.replace('ipfs://', '');
  } else if (ipfsUrl.includes('.ipfs.')) {
    // 处理子域名格式：hash.ipfs.gateway.com
    hash = ipfsUrl.split('.ipfs.')[0].split('//')[1];
  }

  // 清理哈希（移除查询参数等）
  hash = hash.split('?')[0].split('#')[0];

  // 使用最短的IPFS网关
  const shortGateways = [
    'ipfs.io/ipfs/',           // 46字符 + hash
    'gateway.pinata.cloud/ipfs/', // 26字符 + hash
    'cloudflare-ipfs.com/ipfs/',  // 25字符 + hash
    'dweb.link/ipfs/'             // 15字符 + hash (最短)
  ];

  // 选择最短的网关
  const optimizedUrl = `https://dweb.link/ipfs/${hash}`;
  
  console.log('🔧 [优化IPFS URL]');
  console.log('  原始URL:', ipfsUrl, `(${ipfsUrl.length}字符)`);
  console.log('  优化URL:', optimizedUrl, `(${optimizedUrl.length}字符)`);
  console.log('  节省:', ipfsUrl.length - optimizedUrl.length, '字符');

  return optimizedUrl;
}

/**
 * 优化图片URL数组
 * @param {string[]} imageUrls - 图片URL数组
 * @returns {Object} 优化结果
 */
export function optimizeImageUrls(imageUrls) {
  const result = {
    original: imageUrls,
    optimized: [],
    savings: [],
    totalSavings: 0,
    allValid: true
  };

  imageUrls.forEach((url, index) => {
    const optimizedUrl = optimizeIpfsUrl(url);
    const savings = url.length - optimizedUrl.length;
    
    result.optimized.push(optimizedUrl);
    result.savings.push(savings);
    result.totalSavings += savings;

    // 检查优化后是否仍然超长
    if (optimizedUrl.length > CONTRACT_LIMITS.MAX_IMAGE_HASH_LEN) {
      result.allValid = false;
      console.warn(`⚠️ 图片${index + 1}优化后仍超长: ${optimizedUrl.length}字符`);
    }
  });

  return result;
}

/**
 * 生成短哈希（用于极端情况）
 * @param {string} originalUrl - 原始URL
 * @returns {string} 短哈希
 */
export function generateShortHash(originalUrl) {
  // 这是一个简单的哈希函数，实际使用中可能需要更复杂的方案
  let hash = 0;
  for (let i = 0; i < originalUrl.length; i++) {
    const char = originalUrl.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  
  // 转换为16进制并添加前缀
  const shortHash = `short_${Math.abs(hash).toString(16)}`;
  
  console.log('🔧 [生成短哈希]');
  console.log('  原始URL:', originalUrl, `(${originalUrl.length}字符)`);
  console.log('  短哈希:', shortHash, `(${shortHash.length}字符)`);
  
  return shortHash;
}

/**
 * 智能优化图片URL
 * @param {string[]} imageUrls - 图片URL数组
 * @returns {Object} 优化结果
 */
export function smartOptimizeImageUrls(imageUrls) {
  // 第一步：检查当前状态
  const initialCheck = checkImageUrlLengths(imageUrls);

  if (initialCheck.valid) {
    return {
      success: true,
      optimized: imageUrls,
      method: 'no_optimization_needed',
      savings: 0
    };
  }

  // 第二步：尝试IPFS优化
  const ipfsOptimized = optimizeImageUrls(imageUrls);
  console.log('🔧 [IPFS优化]', ipfsOptimized);

  if (ipfsOptimized.allValid) {
    console.log('✅ IPFS优化成功，所有URL符合限制');
    return {
      success: true,
      optimized: ipfsOptimized.optimized,
      method: 'ipfs_optimization',
      savings: ipfsOptimized.totalSavings
    };
  }

  // 第三步：极端情况处理（生成短哈希）
  console.log('⚠️ IPFS优化后仍有URL超长，使用短哈希方案');
  const shortHashUrls = imageUrls.map(url => {
    if (url.length > CONTRACT_LIMITS.MAX_IMAGE_HASH_LEN) {
      return generateShortHash(url);
    }
    return optimizeIpfsUrl(url);
  });

  const finalCheck = checkImageUrlLengths(shortHashUrls);
  
  return {
    success: finalCheck.valid,
    optimized: shortHashUrls,
    method: 'short_hash_fallback',
    savings: imageUrls.reduce((total, url, index) => 
      total + (url.length - shortHashUrls[index].length), 0),
    warning: '使用了短哈希方案，可能影响图片显示'
  };
}

/**
 * 在创建商品前验证和优化图片URL
 * @param {string[]} imageUrls - 图片URL数组
 * @returns {Promise<Object>} 验证和优化结果
 */
export async function validateAndOptimizeForContract(imageUrls) {
  try {
    // 检查数量限制
    if (imageUrls.length === 0) {
      throw new Error('至少需要一张图片');
    }

    if (imageUrls.length > CONTRACT_LIMITS.MAX_IMAGES) {
      throw new Error(`最多只能上传${CONTRACT_LIMITS.MAX_IMAGES}张图片`);
    }

    // 智能优化
    const optimizationResult = smartOptimizeImageUrls(imageUrls);

    if (!optimizationResult.success) {
      throw new Error('无法将图片URL优化到合约限制范围内');
    }

    return {
      success: true,
      optimizedUrls: optimizationResult.optimized,
      method: optimizationResult.method,
      savings: optimizationResult.savings,
      warning: optimizationResult.warning
    };

  } catch (error) {
    console.error('❌ [合约验证] 验证失败:', error.message);
    return {
      success: false,
      error: error.message,
      originalUrls: imageUrls
    };
  }
}
