{"version": "3.4", "log": [{"@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:51", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable", "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol:IAccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol:StringsUpgradeable"], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "__gap", "offset": 0, "slot": "51", "type": "t_array(t_uint256)50_storage", "contract": "ERC165Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:41"}, {"label": "_roles", "offset": 0, "slot": "101", "type": "t_mapping(t_bytes32,t_struct(RoleData)34_storage)", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:260"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)34_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)34_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol:IAccessControlUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\IAccessControlUpgradeable.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:21", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["owner()", "renounceOwnership()", "transferOwnership(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "51", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/governance/TimelockControllerUpgradeable.sol:TimelockControllerUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:26", "version": {"withMetadata": "fafc60f06b75898e8292a7676e788cd1546a0c3c2b3a09b455dc9d52f0b31cb1", "withoutMetadata": "fb4a6df1e182e035ce4ddbd6f83a6b365e4eb2c728db51591d6571cf09d09722", "linkedWithoutMetadata": "fb4a6df1e182e035ce4ddbd6f83a6b365e4eb2c728db51591d6571cf09d09722"}, "inherit": ["@openzeppelin/contracts-upgradeable/token/ERC1155/IERC1155ReceiverUpgradeable.sol:IERC1155ReceiverUpgradeable", "@openzeppelin/contracts-upgradeable/token/ERC721/IERC721ReceiverUpgradeable.sol:IERC721ReceiverUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable", "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol:IAccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "supportsInterface(bytes4)", "isOperation(bytes32)", "isOperationPending(bytes32)", "isOperationReady(bytes32)", "isOperationDone(bytes32)", "getTimestamp(bytes32)", "get<PERSON>in<PERSON><PERSON>y()", "hashOperation(address,uint256,bytes,bytes32,bytes32)", "hashOperationBatch(address[],uint256[],bytes[],bytes32,bytes32)", "schedule(address,uint256,bytes,bytes32,bytes32,uint256)", "scheduleBatch(address[],uint256[],bytes[],bytes32,bytes32,uint256)", "cancel(bytes32)", "execute(address,uint256,bytes,bytes32,bytes32)", "executeBatch(address[],uint256[],bytes[],bytes32,bytes32)", "updateDelay(uint256)", "onERC721Received(address,address,uint256,bytes)", "onERC1155Received(address,address,uint256,uint256,bytes)", "onERC1155BatchReceived(address,address,uint256[],uint256[],bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "__gap", "offset": 0, "slot": "51", "type": "t_array(t_uint256)50_storage", "contract": "ERC165Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:41"}, {"label": "_roles", "offset": 0, "slot": "101", "type": "t_mapping(t_bytes32,t_struct(RoleData)34_storage)", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:260"}, {"label": "_timestamps", "offset": 0, "slot": "151", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:33"}, {"label": "_minDelay", "offset": 0, "slot": "152", "type": "t_uint256", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:34"}, {"label": "__gap", "offset": 0, "slot": "153", "type": "t_array(t_uint256)48_storage", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:433"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)34_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_struct(RoleData)34_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\interfaces\\IERC1967Upgradeable.sol:11", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\interfaces\\draft-IERC1822Upgradeable.sol:10", "inherit": [], "libraries": [], "methods": ["proxiableUUID()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:19", "inherit": ["@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/utils/StorageSlotUpgradeable.sol:StorageSlotUpgradeable", "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:185"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:185"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:185"}], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}], "types": {"t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/beacon/IBeaconUpgradeable.sol:IBeaconUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\beacon\\IBeaconUpgradeable.sol:9", "inherit": [], "libraries": [], "methods": ["implementation()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:58", "inherit": [], "libraries": ["@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:22", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["proxiableUUID()", "upgradeTo(address)", "upgradeToAndCall(address,bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "51", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}], "types": {"t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:18", "inherit": ["@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["paused()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_paused", "offset": 0, "slot": "51", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}], "types": {"t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:23", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_status", "offset": 0, "slot": "1", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "2", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}], "types": {"t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC1155/IERC1155ReceiverUpgradeable.sol:IERC1155ReceiverUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC1155\\IERC1155ReceiverUpgradeable.sol:11", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable"], "libraries": [], "methods": ["onERC1155Received(address,address,uint256,uint256,bytes)", "onERC1155BatchReceived(address,address,uint256[],uint256[],bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol:IERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\IERC20Upgradeable.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/IERC20PermitUpgradeable.sol:IERC20PermitUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\extensions\\IERC20PermitUpgradeable.sol:14", "inherit": [], "libraries": [], "methods": ["permit(address,address,uint256,uint256,uint8,bytes32,bytes32)", "nonces(address)", "DOMAIN_SEPARATOR()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol:SafeERC20Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC20\\utils\\SafeERC20Upgradeable.sol:19", "version": {"withMetadata": "ea90aab56301c487f64f0a84b082a5193bb725a9d0c9ee33a4630b39ceb26494", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": ["@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/token/ERC721/IERC721ReceiverUpgradeable.sol:IERC721ReceiverUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\token\\ERC721\\IERC721ReceiverUpgradeable.sol:11", "inherit": [], "libraries": [], "methods": ["onERC721Received(address,address,uint256,bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:9", "version": {"withMetadata": "b2edb3018cc99ee56a5976db96e8c1dd8cd106347921451cca22bb45e746f7b1", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:185"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts-upgradeable\\utils\\AddressUpgradeable.sol:185"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:17", "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}], "types": {"t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/StorageSlotUpgradeable.sol:StorageSlotUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\StorageSlotUpgradeable.sol:34", "version": {"withMetadata": "2d2436c02898304151f13adf652d3f92c413053e909ead705868a00c13223b78", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/StringsUpgradeable.sol:StringsUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\StringsUpgradeable.sol:12", "version": {"withMetadata": "7ac63a75070a31090f22f00331d8743a2674200b2918c3898578f1e499915ec2", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": ["@openzeppelin/contracts-upgradeable/utils/math/MathUpgradeable.sol:MathUpgradeable", "@openzeppelin/contracts-upgradeable/utils/math/SignedMathUpgradeable.sol:SignedMathUpgradeable"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:23", "inherit": ["@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ERC165Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:41"}], "types": {"t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\IERC165Upgradeable.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/math/MathUpgradeable.sol:MathUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\math\\MathUpgradeable.sol:9", "version": {"withMetadata": "e0ae8477806c59a0eb916134fcf227619c06e1f9b5631a88420e40e4d19a41c6", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/math/SignedMathUpgradeable.sol:SignedMathUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\math\\SignedMathUpgradeable.sol:9", "version": {"withMetadata": "ec6f91f30050624cb37d0bef1e294544db8c4054f1a3ed775e76e31aabe3ca09", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts-upgradeable/utils/structs/EnumerableSetUpgradeable.sol:EnumerableSetUpgradeable": {"src": "@openzeppelin\\contracts-upgradeable\\utils\\structs\\EnumerableSetUpgradeable.sol:41", "version": {"withMetadata": "cf464a34b6f5e02e8b3a8574b1b93de9169cba7274ea97cfcd7e57be42a8e4a1", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/AccessControl.sol:AccessControl": {"src": "@openzeppelin\\contracts\\access\\AccessControl.sol:50", "inherit": ["@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": ["@openzeppelin/contracts/utils/Strings.sol:Strings"], "methods": ["supportsInterface(bytes4)", "hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)5320_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:56"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)5320_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_struct(RoleData)5320_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl": {"src": "@openzeppelin\\contracts\\access\\IAccessControl.sol:9", "inherit": [], "libraries": [], "methods": ["hasRole(bytes32,address)", "getRoleAdmin(bytes32)", "grantRole(bytes32,address)", "revokeRole(bytes32,address)", "renounceRole(bytes32,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/governance/TimelockController.sol:TimelockController": {"src": "@openzeppelin\\contracts\\governance\\TimelockController.sol:25", "version": {"withMetadata": "19df1373b0d70a4c6533ee5f5aacc062c1efc3dbaeeb95f88459d37e32544d66", "withoutMetadata": "19df1373b0d70a4c6533ee5f5aacc062c1efc3dbaeeb95f88459d37e32544d66", "linkedWithoutMetadata": "19df1373b0d70a4c6533ee5f5aacc062c1efc3dbaeeb95f88459d37e32544d66"}, "inherit": ["@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol:IERC1155Receiver", "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol:IERC721Receiver", "@openzeppelin/contracts/access/AccessControl.sol:AccessControl", "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165", "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165", "@openzeppelin/contracts/access/IAccessControl.sol:IAccessControl", "@openzeppelin/contracts/utils/Context.sol:Context"], "libraries": [], "methods": ["(uint256,address[],address[],address)", "()", "supportsInterface(bytes4)", "isOperation(bytes32)", "isOperationPending(bytes32)", "isOperationReady(bytes32)", "isOperationDone(bytes32)", "getTimestamp(bytes32)", "get<PERSON>in<PERSON><PERSON>y()", "hashOperation(address,uint256,bytes,bytes32,bytes32)", "hashOperationBatch(address[],uint256[],bytes[],bytes32,bytes32)", "schedule(address,uint256,bytes,bytes32,bytes32,uint256)", "scheduleBatch(address[],uint256[],bytes[],bytes32,bytes32,uint256)", "cancel(bytes32)", "execute(address,uint256,bytes,bytes32,bytes32)", "executeBatch(address[],uint256[],bytes[],bytes32,bytes32)", "updateDelay(uint256)", "onERC721Received(address,address,uint256,bytes)", "onERC1155Received(address,address,uint256,uint256,bytes)", "onERC1155BatchReceived(address,address,uint256[],uint256[],bytes)"], "linkReferences": [], "errors": [{"kind": "constructor", "contract": "TimelockController", "src": "@openzeppelin\\contracts\\governance\\TimelockController.sol:81"}], "layout": {"storage": [{"label": "_roles", "offset": 0, "slot": "0", "type": "t_mapping(t_bytes32,t_struct(RoleData)5320_storage)", "contract": "AccessControl", "src": "@openzeppelin\\contracts\\access\\AccessControl.sol:56"}, {"label": "_timestamps", "offset": 0, "slot": "1", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "TimelockController", "src": "@openzeppelin\\contracts\\governance\\TimelockController.sol:32"}, {"label": "_minDelay", "offset": 0, "slot": "2", "type": "t_uint256", "contract": "TimelockController", "src": "@openzeppelin\\contracts\\governance\\TimelockController.sol:33"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)5320_storage)": {"label": "mapping(bytes32 => struct AccessControl.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_struct(RoleData)5320_storage": {"label": "struct AccessControl.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC1155/IERC1155Receiver.sol:IERC1155Receiver": {"src": "@openzeppelin\\contracts\\token\\ERC1155\\IERC1155Receiver.sol:11", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["onERC1155Received(address,address,uint256,uint256,bytes)", "onERC1155BatchReceived(address,address,uint256[],uint256[],bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol:9", "inherit": [], "libraries": [], "methods": ["totalSupply()", "balanceOf(address)", "transfer(address,uint256)", "allowance(address,address)", "approve(address,uint256)", "transferFrom(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol:IERC20Permit": {"src": "@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Permit.sol:14", "inherit": [], "libraries": [], "methods": ["permit(address,address,uint256,uint256,uint8,bytes32,bytes32)", "nonces(address)", "DOMAIN_SEPARATOR()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:SafeERC20": {"src": "@openzeppelin\\contracts\\token\\ERC20\\utils\\SafeERC20.sol:19", "version": {"withMetadata": "08cb82d31e2add749ab0d153ec9cecf01d6f9a5611deee160dc9d085ad99bada", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/Address.sol:Address"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol:IERC721Receiver": {"src": "@openzeppelin\\contracts\\token\\ERC721\\IERC721Receiver.sol:11", "inherit": [], "libraries": [], "methods": ["onERC721Received(address,address,uint256,bytes)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Address.sol:Address": {"src": "@openzeppelin\\contracts\\utils\\Address.sol:9", "version": {"withMetadata": "8aa441c28f8a58e8c8f7e5eafbb170f8284d80a61bc649b5a44e5ac520f98f95", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [{"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:185"}, {"kind": "delegatecall", "src": "@openzeppelin\\contracts\\utils\\Address.sol:185"}], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Context.sol:Context": {"src": "@openzeppelin\\contracts\\utils\\Context.sol:16", "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/Strings.sol:Strings": {"src": "@openzeppelin\\contracts\\utils\\Strings.sol:12", "version": {"withMetadata": "bef2ac9570f08972f65435add6f9452cf7867bbcf728659f32fa687334b69b5e", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": ["@openzeppelin/contracts/utils/math/Math.sol:Math", "@openzeppelin/contracts/utils/math/SignedMath.sol:SignedMath"], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/ERC165.sol:ERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\ERC165.sol:22", "inherit": ["@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165"], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/introspection/IERC165.sol:IERC165": {"src": "@openzeppelin\\contracts\\utils\\introspection\\IERC165.sol:15", "inherit": [], "libraries": [], "methods": ["supportsInterface(bytes4)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/math/Math.sol:Math": {"src": "@openzeppelin\\contracts\\utils\\math\\Math.sol:9", "version": {"withMetadata": "f5bc6decfbf01ba186cd03abfbf1b85595c1734297e1c5dd4b01f598473d46c7", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "@openzeppelin/contracts/utils/math/SignedMath.sol:SignedMath": {"src": "@openzeppelin\\contracts\\utils\\math\\SignedMath.sol:9", "version": {"withMetadata": "d46576295539bba3b1a06dec2500c681e93c0092feee9e9d3657a4863e2e8ecc", "withoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a", "linkedWithoutMetadata": "e2a89a66865458f0d9fedd4bc7faf0ee62e4c341d176ab79d29bd77a1bdfc32a"}, "inherit": [], "libraries": [], "methods": [], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/AddressManagement.sol:AddressManagement": {"src": "contracts\\AddressManagement.sol:12", "version": {"withMetadata": "8e1be72f06534567fa6e1b089472193549697dd322c39c10f09896845b010e62", "withoutMetadata": "f78f33348a12569dbd56137909be1ec64600f170e2b198761e74326469fb5f2d", "linkedWithoutMetadata": "f78f33348a12569dbd56137909be1ec64600f170e2b198761e74326469fb5f2d"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": ["initialize(address)", "setTimelock(address)", "setAuthorizedContract(address,bool)", "setAuthorizedContracts(address[],bool)", "isAuthorizedContract(address)", "addAddress(string,string,string,string,string,string,bool)", "updateAddress(uint256,string,string,string,string,string,string,bool)", "deleteAddress(uint256)", "getUserAddresses(address)", "getMyAddresses()", "getDefaultA<PERSON>ress(address)", "getMyDefaultAddress()", "set<PERSON>efaultAddress(uint256)", "getAddress(address,uint256)", "pause()", "unpause()", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "timelock", "offset": 0, "slot": "303", "type": "t_address", "contract": "AddressManagement", "src": "contracts\\AddressManagement.sol:23"}, {"label": "_userAddresses", "offset": 0, "slot": "304", "type": "t_mapping(t_address,t_array(t_struct(AddressInfo)8879_storage)dyn_storage)", "contract": "AddressManagement", "src": "contracts\\AddressManagement.sol:70"}, {"label": "_defaultAddressIds", "offset": 0, "slot": "305", "type": "t_mapping(t_address,t_uint256)", "contract": "AddressManagement", "src": "contracts\\AddressManagement.sol:71"}, {"label": "authorizedContracts", "offset": 0, "slot": "306", "type": "t_mapping(t_address,t_bool)", "contract": "AddressManagement", "src": "contracts\\AddressManagement.sol:76"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_struct(AddressInfo)8879_storage)dyn_storage": {"label": "struct AddressManagement.AddressInfo[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_struct(AddressInfo)8879_storage)dyn_storage)": {"label": "mapping(address => struct AddressManagement.AddressInfo[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(AddressInfo)8879_storage": {"label": "struct AddressManagement.AddressInfo", "members": [{"label": "addressId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "user", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "name", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "phone", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "province", "type": "t_string_storage", "offset": 0, "slot": "4"}, {"label": "city", "type": "t_string_storage", "offset": 0, "slot": "5"}, {"label": "district", "type": "t_string_storage", "offset": 0, "slot": "6"}, {"label": "detail", "type": "t_string_storage", "offset": 0, "slot": "7"}, {"label": "isDefault", "type": "t_bool", "offset": 0, "slot": "8"}, {"label": "createTime", "type": "t_uint256", "offset": 0, "slot": "9"}], "numberOfBytes": "320"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/AgentSystemMinimal.sol:AgentSystem": {"src": "contracts\\AgentSystemMinimal.sol:13", "version": {"withMetadata": "ea77a36f5164dee11d500c91f630df5c32c3155e98a786c5f53edf7bc5a72ada", "withoutMetadata": "14d6dec40dcb952f6f5d909d753f67fdaa3096dd5f2560b177102909b90ce308", "linkedWithoutMetadata": "14d6dec40dcb952f6f5d909d753f67fdaa3096dd5f2560b177102909b90ce308"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable", "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol:IAccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": ["initialize(address,address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "register(address)", "addPerformance(address,uint256)", "setUserLevel(address,uint8)", "addToBlacklist(address)", "removeFromBlacklist(address)", "freezeUser(address)", "unfreezeUser(address)", "updateSystemAdmin(address)", "grantUploaderRole(address)", "tryUpgrade(address)", "getUserInfo(address)", "getUserReferrals(address)", "getLevel(address)", "getInviter(address)", "getPerformance(address)", "getPersonalPerformance(address)", "getTeamMemberCount(address,uint8)", "getTeamStats(address)", "isBlacklisted(address)", "is<PERSON><PERSON>zen(address)", "pause()", "unpause()", "canCreateGroupBuyRoom(address)", "canJoinGroupBuyRoom(address)", "canTransferPoints(address)", "canReceivePoints(address)", "canStakeNode(address)", "canJoinQPTBuyback(address)", "canRegisterMerchant(address)", "getUserPermissions(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "__gap", "offset": 0, "slot": "53", "type": "t_array(t_uint256)50_storage", "contract": "ERC165Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:41"}, {"label": "_roles", "offset": 0, "slot": "103", "type": "t_mapping(t_bytes32,t_struct(RoleData)34_storage)", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:260"}, {"label": "_paused", "offset": 0, "slot": "153", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "203", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "204", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "303", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "systemAdmin", "offset": 0, "slot": "353", "type": "t_address", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:26"}, {"label": "timelock", "offset": 0, "slot": "354", "type": "t_contract(TimelockController)6635", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:27"}, {"label": "users", "offset": 0, "slot": "355", "type": "t_mapping(t_address,t_struct(User)10131_storage)", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:37"}, {"label": "teamPerformance", "offset": 0, "slot": "356", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:38"}, {"label": "blacklist", "offset": 0, "slot": "357", "type": "t_mapping(t_address,t_bool)", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:39"}, {"label": "frozenUsers", "offset": 0, "slot": "358", "type": "t_mapping(t_address,t_bool)", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:40"}, {"label": "__gap", "offset": 0, "slot": "359", "type": "t_array(t_uint256)50_storage", "contract": "AgentSystem", "src": "contracts\\AgentSystemMinimal.sol:445"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(TimelockController)6635": {"label": "contract TimelockController", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(User)10131_storage)": {"label": "mapping(address => struct AgentSystem.User)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)34_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoleData)34_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_struct(User)10131_storage": {"label": "struct AgentSystem.User", "members": [{"label": "inviter", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "level", "type": "t_uint8", "offset": 20, "slot": "0"}, {"label": "totalPerformance", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "referrals", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "2"}, {"label": "personalPerformance", "type": "t_uint256", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/FeeSplitManager.sol:FeeSplitManager": {"src": "contracts\\FeeSplitManager.sol:23", "version": {"withMetadata": "7b1041f7f6772c4f55454e7644fa2090e52ffda9092d817f9dbb72377591c031", "withoutMetadata": "7789d1bded480aa250eb961d7bb38b3041704ba2eaa696b24aea87d8c31c7c15", "linkedWithoutMetadata": "7789d1bded480aa250eb961d7bb38b3041704ba2eaa696b24aea87d8c31c7c15"}, "inherit": ["contracts/interfaces/IFeeSplitManager.sol:IFeeSplitManager", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol:SafeERC20Upgradeable"], "methods": ["initialize(address)", "initializePlatformAmounts()", "setTimelock(address)", "setNodeBuybackCombination(uint8)", "getCurrentCombination()", "getFixedAllocations(uint256)", "isTierSupported(uint256)", "getSupportedTiers()", "getPointsAllocation(uint256)", "getNodeStakingAllocation(uint256)", "getBuybackAllocation(uint256)", "getTotalFixedAllocation(uint256)", "getPlatformAllocation(uint256,uint256)", "calcSplitAmountsByTier(uint256)", "calcSplitAmountsByTierAndLevel(uint256,uint256)", "setPlatformAmount(uint256,uint256,uint256)", "setPlatformAmountsForTier(uint256,uint256[5])", "distribute(address,uint256,uint256,address,address,address,address)", "clearResidualBalance(address,address)", "()", "()", "getBNBBalance()", "withdrawBNB(address payable,uint256)", "pause()", "unpause()", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()"], "linkReferences": [], "errors": [{"kind": "incorrect-initializer-order", "src": "contracts\\FeeSplitManager.sol:100", "expectedLinearization": ["OwnableUpgradeable", "PausableUpgradeable", "ReentrancyGuardUpgradeable"], "foundOrder": ["OwnableUpgradeable", "ReentrancyGuardUpgradeable", "PausableUpgradeable"]}], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "timelock", "offset": 0, "slot": "303", "type": "t_address", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:43"}, {"label": "supportedTiers", "offset": 0, "slot": "304", "type": "t_array(t_uint256)6_storage", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:51"}, {"label": "pointsAmounts", "offset": 0, "slot": "310", "type": "t_array(t_uint256)6_storage", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:54"}, {"label": "nodeAmounts", "offset": 0, "slot": "316", "type": "t_array(t_uint256)6_storage", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:57"}, {"label": "buybackAmounts", "offset": 0, "slot": "322", "type": "t_array(t_uint256)6_storage", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:59"}, {"label": "platformAmounts", "offset": 0, "slot": "328", "type": "t_mapping(t_uint256,t_mapping(t_uint256,t_uint256))", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:68"}, {"label": "__gap", "offset": 0, "slot": "329", "type": "t_array(t_uint256)50_storage", "contract": "FeeSplitManager", "src": "contracts\\FeeSplitManager.sol:525"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)6_storage": {"label": "uint256[6]", "numberOfBytes": "192"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_uint256,t_uint256))": {"label": "mapping(uint256 => mapping(uint256 => uint256))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/GroupBuyRoomMinimal.sol:GroupBuyRoomMinimal": {"src": "contracts\\GroupBuyRoomMinimal.sol:20", "version": {"withMetadata": "d4643f60d02a5908f336cab97d27fbf349276083188809b6288be23956ebec80", "withoutMetadata": "f60e203cc2a0c12154361bf2f56c90fa3439e59b680b3ce7e9fa6719efb86d9a", "linkedWithoutMetadata": "f60e203cc2a0c12154361bf2f56c90fa3439e59b680b3ce7e9fa6719efb86d9a"}, "inherit": ["contracts/interfaces/IGroupBuyRoom.sol:IGroupBuyRoom", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol:SafeERC20Upgradeable"], "methods": ["initialize(address,address,address,address,address,address,address,address,address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "setFeeSplitManager(address)", "setNodeStakingAddress(address)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(address)", "setPointsManagementAddress(address)", "setPlatformAddress(address)", "createRoom(uint256)", "joinRoom(uint256)", "closeRoom(uint256)", "setWinner(uint256,address,bytes32,uint256)", "expireRoom(uint256)", "claimCreatorCommission(uint256)", "claimParticipantRefund(uint256)", "claimWinnerQPT(uint256)", "claimWinnerPoints(uint256)", "totalRooms()", "getSupportedTiers()", "isGroupBuyRoom()", "getRoomTier(uint256)", "<PERSON><PERSON><PERSON><PERSON>(uint256)", "getWinnerPerformance(uint256)", "getRoom(uint256)", "getRoomDetails(uint256)", "isRoomExpired(uint256)", "getRoomStatus(uint256)", "getUserPermissions(uint256,address)", "getUserClaimStatus(uint256,address)", "checkClaimed(uint256,address)", "getWinnerRewardStatus(uint256,address)", "emergencyResetClaimStatus(uint256,address,uint8)", "getRoomRewardInfo(uint256)", "getRoomTimeInfo(uint256)", "getUserPaidAmount(uint256,address)", "getLotteryInfo(uint256)", "getRoomsBatch(uint256[])", "getContractStats()", "canCloseRoom(uint256)", "canExpireRoom(uint256)", "withdrawUSDT(address,uint256)", "()", "initializeV2()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "usdt", "offset": 0, "slot": "303", "type": "t_contract(IERC20Upgradeable)2519", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:48"}, {"label": "agentSystem", "offset": 0, "slot": "304", "type": "t_contract(IAgentSystem)35674", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:49"}, {"label": "q<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "305", "type": "t_contract(IQPTLock)35964", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:50"}, {"label": "feeSplitManager", "offset": 0, "slot": "306", "type": "t_contract(IFeeSplitManager)35735", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:51"}, {"label": "nodeStakingAddress", "offset": 0, "slot": "307", "type": "t_address", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:52"}, {"label": "buybackAddress", "offset": 0, "slot": "308", "type": "t_address", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:53"}, {"label": "pointsManagementAddress", "offset": 0, "slot": "309", "type": "t_address", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:54"}, {"label": "platformAddress", "offset": 0, "slot": "310", "type": "t_address", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:55"}, {"label": "timelock", "offset": 0, "slot": "311", "type": "t_address", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:56"}, {"label": "rooms", "offset": 0, "slot": "312", "type": "t_mapping(t_uint256,t_struct(Room)13231_storage)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:58"}, {"label": "hasJoined", "offset": 0, "slot": "313", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:59"}, {"label": "nonWinnerSubsidies", "offset": 0, "slot": "314", "type": "t_mapping(t_uint256,t_uint256)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:60"}, {"label": "tierPoints", "offset": 0, "slot": "315", "type": "t_mapping(t_uint256,t_uint256)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:61"}, {"label": "expireFailed", "offset": 0, "slot": "316", "type": "t_mapping(t_uint256,t_bool)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:62"}, {"label": "nextRoomId", "offset": 0, "slot": "317", "type": "t_uint256", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:63"}, {"label": "userPaidAmount", "offset": 0, "slot": "318", "type": "t_mapping(t_uint256,t_mapping(t_address,t_uint256))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:69"}, {"label": "roomReadyForWinner", "offset": 0, "slot": "319", "type": "t_mapping(t_uint256,t_bool)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:70"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "320", "type": "t_mapping(t_uint256,t_address)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:71"}, {"label": "roomLotteryTxHash", "offset": 0, "slot": "321", "type": "t_mapping(t_uint256,t_bytes32)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:73"}, {"label": "roomLotteryTimestamp", "offset": 0, "slot": "322", "type": "t_mapping(t_uint256,t_uint256)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:75"}, {"label": "roomLastActionTime", "offset": 0, "slot": "323", "type": "t_mapping(t_uint256,t_uint256)", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:76"}, {"label": "creator<PERSON>laimed", "offset": 0, "slot": "324", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:1043"}, {"label": "participantClaimed", "offset": 0, "slot": "325", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:1043"}, {"label": "winnerQPTClaimed", "offset": 0, "slot": "326", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:1043"}, {"label": "winnerPointsClaimed", "offset": 0, "slot": "327", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:1043"}, {"label": "__gap", "offset": 0, "slot": "328", "type": "t_array(t_uint256)41_storage", "contract": "GroupBuyRoomMinimal", "src": "contracts\\GroupBuyRoomMinimal.sol:1043"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)41_storage": {"label": "uint256[41]", "numberOfBytes": "1312"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IAgentSystem)35674": {"label": "contract IAgentSystem", "numberOfBytes": "20"}, "t_contract(IERC20Upgradeable)2519": {"label": "contract IERC20Upgradeable", "numberOfBytes": "20"}, "t_contract(IFeeSplitManager)35735": {"label": "contract IFeeSplitManager", "numberOfBytes": "20"}, "t_contract(IQPTLock)35964": {"label": "contract IQPTLock", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_address)": {"label": "mapping(uint256 => address)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bytes32)": {"label": "mapping(uint256 => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_address,t_bool))": {"label": "mapping(uint256 => mapping(address => bool))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_address,t_uint256))": {"label": "mapping(uint256 => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Room)13231_storage)": {"label": "mapping(uint256 => struct GroupBuyRoomMinimal.Room)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(Room)13231_storage": {"label": "struct GroupBuyRoomMinimal.Room", "members": [{"label": "creator", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "tier", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "participants", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "2"}, {"label": "createTime", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "isClosed", "type": "t_bool", "offset": 0, "slot": "4"}, {"label": "isSuccessful", "type": "t_bool", "offset": 1, "slot": "4"}, {"label": "winnerIndex", "type": "t_uint8", "offset": 2, "slot": "4"}, {"label": "subsidyPer", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "creatorCommission", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "systemFee", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "systemFeeDistributed", "type": "t_bool", "offset": 0, "slot": "8"}], "numberOfBytes": "288"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/MerchantManagement.sol:IPointsManagement": {"src": "contracts\\MerchantManagement.sol:13", "inherit": [], "libraries": [], "methods": ["exchangeGoods(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/MerchantManagement.sol:MerchantManagement": {"src": "contracts\\MerchantManagement.sol:20", "version": {"withMetadata": "a3019d596cfb0045a9a99f2ae0166cb629adc23c857625d590ad004305f60872", "withoutMetadata": "542d5e6ca5280d5190307c8f6bbd590858342da81620d22d90a13685f0be896f", "linkedWithoutMetadata": "542d5e6ca5280d5190307c8f6bbd590858342da81620d22d90a13685f0be896f"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["initialize(address,address,address)", "setTimelock(address)", "setSystemAdmin(address)", "setProductManagementAddress(address)", "getSystemAdmin()", "getProductManagementAddress()", "addToBlacklist(address)", "removeFromBlacklist(address)", "registerMerchant(string,string,string)", "submitVerification()", "verifyMerchant(address)", "rejectMerchantVerification(address)", "resetMerchantVerification(address)", "updateMerchantInfo(string,string,string)", "updateMerchantOrders(address,uint256)", "updateMerchantSalesPoints(address,uint256)", "updateMerchantExchangedPoints(address,uint256)", "getMerchantInfo(address)", "getMerchantPointsStats(address)", "getMerchantOrders(address)", "getMerchantVerification(address)", "hasSubmittedVerification(address)", "getMerchantStatus(address)", "isMerchant(address)", "getMerchantCount()", "getMerchantAddresses(uint256,uint256)", "getAllMerchantAddresses()", "fixStorageLayout()", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "pause()", "unpause()"], "linkReferences": [], "errors": [{"kind": "incorrect-initializer-order", "src": "contracts\\MerchantManagement.sol:120", "expectedLinearization": ["OwnableUpgradeable", "PausableUpgradeable", "ReentrancyGuardUpgradeable"], "foundOrder": ["OwnableUpgradeable", "ReentrancyGuardUpgradeable", "PausableUpgradeable"]}], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_storageChecksums", "offset": 0, "slot": "51", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "52", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "_owner", "offset": 1, "slot": "52", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "53", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "102", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "103", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "152", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "153", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "202", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "252", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "timelock", "offset": 0, "slot": "302", "type": "t_address", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:33"}, {"label": "systemAdmin", "offset": 0, "slot": "303", "type": "t_address", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:34"}, {"label": "merchants", "offset": 0, "slot": "304", "type": "t_mapping(t_address,t_struct(MerchantInfo)17167_storage)", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:71"}, {"label": "verifications", "offset": 0, "slot": "305", "type": "t_mapping(t_address,t_struct(MerchantVerification)17172_storage)", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:72"}, {"label": "merchantCounter", "offset": 0, "slot": "306", "type": "t_uint256", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:77"}, {"label": "merchantAddresses", "offset": 0, "slot": "307", "type": "t_array(t_address)dyn_storage", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:79"}, {"label": "isMerchantRegistered", "offset": 0, "slot": "308", "type": "t_mapping(t_address,t_bool)", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:80"}, {"label": "pointsManagement", "offset": 0, "slot": "309", "type": "t_contract(IPointsManagement)17089", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:83"}, {"label": "blacklist", "offset": 0, "slot": "310", "type": "t_mapping(t_address,t_bool)", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:84"}, {"label": "productManagementAddress", "offset": 0, "slot": "311", "type": "t_address", "contract": "MerchantManagement", "src": "contracts\\MerchantManagement.sol:86"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IPointsManagement)17089": {"label": "contract IPointsManagement", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(MerchantInfo)17167_storage)": {"label": "mapping(address => struct MerchantManagement.MerchantInfo)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(MerchantVerification)17172_storage)": {"label": "mapping(address => struct MerchantManagement.MerchantVerification)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(MerchantInfo)17167_storage": {"label": "struct MerchantManagement.MerchantInfo", "members": [{"label": "name", "type": "t_string_storage", "offset": 0, "slot": "0"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "1"}, {"label": "logo", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "isActive", "type": "t_bool", "offset": 0, "slot": "3"}, {"label": "createTime", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "totalSales", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "totalOrders", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "totalPoints", "type": "t_uint256", "offset": 0, "slot": "7"}, {"label": "exchangedPoints", "type": "t_uint256", "offset": 0, "slot": "8"}], "numberOfBytes": "288"}, "t_struct(MerchantVerification)17172_storage": {"label": "struct MerchantManagement.MerchantVerification", "members": [{"label": "isVerified", "type": "t_bool", "offset": 0, "slot": "0"}, {"label": "verifyTime", "type": "t_uint256", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/MultiSigWallet.sol:MultiSigWallet": {"src": "contracts\\MultiSigWallet.sol:19", "version": {"withMetadata": "5846121129530d45e0e191dc46b7dfca4d0d9755d8e984a49919c2d14019ae2f", "withoutMetadata": "12d2ffdc300751cd24c2644a751a68646e486202f703c3c5d11a8feb0ea5e3ec", "linkedWithoutMetadata": "12d2ffdc300751cd24c2644a751a68646e486202f703c3c5d11a8feb0ea5e3ec"}, "inherit": ["contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["initialize(address[],uint256,address)", "()", "submitTransaction(address,uint256,bytes)", "confirmTransaction(uint256)", "executeTransaction(uint256)", "revokeConfirmation(uint256)", "getOwners()", "getTransactionCount()", "getTransaction(uint256)", "isTransactionConfirmed(uint256,address)", "getConfirmationCount(uint256)", "isConfirmed(uint256)", "isConfirmedBy(uint256,address)", "getPendingTransactions()", "batchConfirmTransactions(uint256[])", "emergencyPause()", "emergencyUnpause()", "updateTimelock(address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "51", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "__gap", "offset": 0, "slot": "101", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "151", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "152", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "201", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "202", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "251", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "252", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "_storageChecksums", "offset": 0, "slot": "301", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "302", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "owners", "offset": 0, "slot": "303", "type": "t_array(t_address)dyn_storage", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:40"}, {"label": "isOwner", "offset": 0, "slot": "304", "type": "t_mapping(t_address,t_bool)", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:40"}, {"label": "numConfirmationsRequired", "offset": 0, "slot": "305", "type": "t_uint256", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:43"}, {"label": "timelock", "offset": 0, "slot": "306", "type": "t_address", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:45"}, {"label": "confirmations", "offset": 0, "slot": "307", "type": "t_mapping(t_uint256,t_mapping(t_address,t_bool))", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:53"}, {"label": "transactions", "offset": 0, "slot": "308", "type": "t_array(t_struct(Transaction)18826_storage)dyn_storage", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:57"}, {"label": "emergencyPaused", "offset": 0, "slot": "309", "type": "t_bool", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:378"}, {"label": "emergencyVotes", "offset": 0, "slot": "310", "type": "t_mapping(t_address,t_bool)", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:378"}, {"label": "emergencyVoteCount", "offset": 0, "slot": "311", "type": "t_uint256", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:381"}, {"label": "__gap", "offset": 0, "slot": "312", "type": "t_array(t_uint256)50_storage", "contract": "MultiSigWallet", "src": "contracts\\MultiSigWallet.sol:463"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_struct(Transaction)18826_storage)dyn_storage": {"label": "struct MultiSigWallet.Transaction[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_bytes_storage": {"label": "bytes", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_mapping(t_address,t_bool))": {"label": "mapping(uint256 => mapping(address => bool))", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(Transaction)18826_storage": {"label": "struct MultiSigWallet.Transaction", "members": [{"label": "to", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "value", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "data", "type": "t_bytes_storage", "offset": 0, "slot": "2"}, {"label": "executed", "type": "t_bool", "offset": 0, "slot": "3"}, {"label": "numConfirmations", "type": "t_uint256", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/NodeStaking.sol:NodeStaking": {"src": "contracts\\NodeStaking.sol:24", "version": {"withMetadata": "31278f034dc0636b3a9a44d54d5d1c32cef301271d61a36d56b0294dfc13e4dc", "withoutMetadata": "f967550e98fef29d86a6377c4fd692ad6922e62d029800443532ac6407869b10", "linkedWithoutMetadata": "f967550e98fef29d86a6377c4fd692ad6922e62d029800443532ac6407869b10"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol:SafeERC20Upgradeable", "@openzeppelin/contracts-upgradeable/utils/structs/EnumerableSetUpgradeable.sol:EnumerableSetUpgradeable"], "methods": ["()", "setTimelock(address)", "initialize(address,address,address)", "receiveDividendFund(uint256)", "calculateDailyReward()", "getCurrentRequiredStake()", "stakeNode()", "unstakeNode()", "claimReward()", "getNodeAddressesPaginated(uint256,uint256)", "getNodeAddresses()", "getDailyRewardPerNode()", "hasClaimedTodayReward(address)", "getTotalDividends()", "pause()", "unpause()", "()", "()", "getBNBBalance()", "withdrawBNB(address payable,uint256)", "withdrawUSDT(address,uint256)", "getDividendDetails()", "getNodeActivationTime(address)", "getUserNodeStatus(address)", "getBatchUserNodeStatus(address[])", "checkNodeOperationEligibility(address,uint8)", "getSystemNodeStats()", "getVersion()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "51", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "101", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "151", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "152", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "201", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "251", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "timelock", "offset": 0, "slot": "301", "type": "t_address", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:32"}, {"label": "q<PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "302", "type": "t_contract(IQPTLock)35964", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:42"}, {"label": "usdtToken", "offset": 0, "slot": "303", "type": "t_contract(IERC20Upgradeable)2519", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:43"}, {"label": "startTime", "offset": 0, "slot": "304", "type": "t_uint256", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:44"}, {"label": "initialStakeAmount", "offset": 0, "slot": "305", "type": "t_uint256", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:47"}, {"label": "totalEffectiveNodes", "offset": 0, "slot": "306", "type": "t_uint256", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:50"}, {"label": "currentDay", "offset": 0, "slot": "307", "type": "t_uint256", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:53"}, {"label": "dailyRewardPerNode", "offset": 0, "slot": "308", "type": "t_uint256", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:54"}, {"label": "isNodeActive", "offset": 0, "slot": "309", "type": "t_mapping(t_address,t_bool)", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:56"}, {"label": "lastClaimDay", "offset": 0, "slot": "310", "type": "t_mapping(t_address,t_uint256)", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:58"}, {"label": "nodeActivationTime", "offset": 0, "slot": "311", "type": "t_mapping(t_address,t_uint256)", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:60"}, {"label": "nodeAddresses", "offset": 0, "slot": "312", "type": "t_struct(AddressSet)5013_storage", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:62"}, {"label": "__gap", "offset": 0, "slot": "314", "type": "t_array(t_uint256)48_storage", "contract": "NodeStaking", "src": "contracts\\NodeStaking.sol:593"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_bytes32)dyn_storage": {"label": "bytes32[]", "numberOfBytes": "32"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IERC20Upgradeable)2519": {"label": "contract IERC20Upgradeable", "numberOfBytes": "20"}, "t_contract(IQPTLock)35964": {"label": "contract IQPTLock", "numberOfBytes": "20"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_struct(AddressSet)5013_storage": {"label": "struct EnumerableSetUpgradeable.AddressSet", "members": [{"label": "_inner", "type": "t_struct(Set)4698_storage", "offset": 0, "slot": "0"}], "numberOfBytes": "64"}, "t_struct(Set)4698_storage": {"label": "struct EnumerableSetUpgradeable.Set", "members": [{"label": "_values", "type": "t_array(t_bytes32)dyn_storage", "offset": 0, "slot": "0"}, {"label": "_indexes", "type": "t_mapping(t_bytes32,t_uint256)", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/OrderManagement.sol:OrderManagement": {"src": "contracts\\OrderManagement.sol:14", "version": {"withMetadata": "c7f6c0090b972ca8fe51986c99c63bc94631cd6579c597dbba7a6491bd5860e2", "withoutMetadata": "c5cedfb09c450a956dcfa138e4dd0d41a421c837acad79ee961721451cf4250c", "linkedWithoutMetadata": "c5cedfb09c450a956dcfa138e4dd0d41a421c837acad79ee961721451cf4250c"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["()", "initialize(address)", "setAuthorizedContract(address,bool)", "transferOwnershipToTimelock()", "setTimelock(address)", "createOrder(address,address,uint256,uint256,uint256,uint256)", "shipOrder(uint256,string,string)", "updateOrderStatus(uint256,uint8)", "getMerchantOrders(address)", "getBuyerOrders(address)", "getOrderInfo(uint256)", "getOrderInfoBatch(uint256[])", "pause()", "unpause()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "51", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "52", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_status", "offset": 0, "slot": "101", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "_paused", "offset": 0, "slot": "151", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "152", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "__gap", "offset": 0, "slot": "201", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "251", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "orders", "offset": 0, "slot": "301", "type": "t_mapping(t_uint256,t_struct(OrderInfo)21404_storage)", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:80"}, {"label": "buyerOrders", "offset": 0, "slot": "302", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:81"}, {"label": "merchantOrders", "offset": 0, "slot": "303", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:84"}, {"label": "orderCount", "offset": 0, "slot": "304", "type": "t_uint256", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:87"}, {"label": "authorizedContracts", "offset": 0, "slot": "305", "type": "t_mapping(t_address,t_bool)", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:92"}, {"label": "timelock", "offset": 0, "slot": "306", "type": "t_address", "contract": "OrderManagement", "src": "contracts\\OrderManagement.sol:97"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_enum(OrderStatus)21375": {"label": "enum OrderManagement.OrderStatus", "members": ["Pending", "Shipped", "Delivered", "Completed"], "numberOfBytes": "1"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(OrderInfo)21404_storage)": {"label": "mapping(uint256 => struct OrderManagement.OrderInfo)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(OrderInfo)21404_storage": {"label": "struct OrderManagement.OrderInfo", "members": [{"label": "orderId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "buyer", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "merchant", "type": "t_address", "offset": 0, "slot": "2"}, {"label": "productId", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "quantity", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "totalPrice", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "addressId", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "status", "type": "t_enum(OrderStatus)21375", "offset": 0, "slot": "7"}, {"label": "trackingNumber", "type": "t_string_storage", "offset": 0, "slot": "8"}, {"label": "shippingCompany", "type": "t_string_storage", "offset": 0, "slot": "9"}, {"label": "createTime", "type": "t_uint256", "offset": 0, "slot": "10"}, {"label": "shippedTime", "type": "t_uint256", "offset": 0, "slot": "11"}, {"label": "deliveredTime", "type": "t_uint256", "offset": 0, "slot": "12"}], "numberOfBytes": "416"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/PointsManagement.sol:IMerchantManagement": {"src": "contracts\\PointsManagement.sol:20", "inherit": [], "libraries": [], "methods": ["isMerchant(address)", "getMerchantStatus(address)", "updateMerchantExchangedPoints(address,uint256)", "blacklist(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/PointsManagement.sol:PointsManagement": {"src": "contracts\\PointsManagement.sol:29", "version": {"withMetadata": "ebbf804d9c99468740ae2651afc5f1bddce8b37d16a90e67b5395df8a391e3bf", "withoutMetadata": "3d613bb196b009cf29a876d714d32e56fe998cb89598bebab3bdcd969ca3500c", "linkedWithoutMetadata": "3d613bb196b009cf29a876d714d32e56fe998cb89598bebab3bdcd969ca3500c"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol:AddressUpgradeable"], "methods": ["initialize(address,address,address,address,address)", "setTimelock(address)", "isSystemConfigured()", "getSystemConfiguration()", "setGroupBuyRoomAddress(address)", "setMarket<PERSON><PERSON><PERSON><PERSON>(address)", "generatePoints(address,uint256,string,string)", "generateSalesPoints(address,uint256,string)", "transferGroupBuyPoints(address,uint256)", "exchangeGoods(address,address,uint256)", "exchangeSalesPointsForUSDT(uint256,address)", "setUSDTToken(address)", "setMerchantManager(address)", "withdrawUSDT(address,uint256)", "()", "()", "getBNBBalance()", "withdrawBNB(address payable,uint256)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "getPointsDecimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_storageChecksums", "offset": 0, "slot": "51", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "52", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "_owner", "offset": 1, "slot": "52", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "53", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "102", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "103", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "152", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "153", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "202", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "252", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "groupBuyRoomAddress", "offset": 0, "slot": "302", "type": "t_address", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:41"}, {"label": "timelock", "offset": 0, "slot": "303", "type": "t_address", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:49"}, {"label": "usdtToken", "offset": 0, "slot": "304", "type": "t_contract(IERC20Upgradeable)2519", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:78"}, {"label": "marketplaceAddress", "offset": 0, "slot": "305", "type": "t_address", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:78"}, {"label": "merchantManager", "offset": 0, "slot": "306", "type": "t_address", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:81"}, {"label": "groupBuyPointsNonExchangeable", "offset": 0, "slot": "307", "type": "t_mapping(t_address,t_uint256)", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:96"}, {"label": "salesPointsExchangeable", "offset": 0, "slot": "308", "type": "t_mapping(t_address,t_uint256)", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:102"}, {"label": "userPointsRecords", "offset": 0, "slot": "309", "type": "t_mapping(t_address,t_array(t_struct(PointsRecord)22226_storage)dyn_storage)", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:110"}, {"label": "exchangeRecords", "offset": 0, "slot": "310", "type": "t_mapping(t_address,t_array(t_struct(ExchangeRecord)22244_storage)dyn_storage)", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:128"}, {"label": "lastExchangeTime", "offset": 0, "slot": "311", "type": "t_mapping(t_address,t_uint256)", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:139"}, {"label": "pointsStats", "offset": 0, "slot": "312", "type": "t_struct(PointsStatistics)22271_storage", "contract": "PointsManagement", "src": "contracts\\PointsManagement.sol:148"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_struct(ExchangeRecord)22244_storage)dyn_storage": {"label": "struct PointsManagement.ExchangeRecord[]", "numberOfBytes": "32"}, "t_array(t_struct(PointsRecord)22226_storage)dyn_storage": {"label": "struct PointsManagement.PointsRecord[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IERC20Upgradeable)2519": {"label": "contract IERC20Upgradeable", "numberOfBytes": "20"}, "t_mapping(t_address,t_array(t_struct(ExchangeRecord)22244_storage)dyn_storage)": {"label": "mapping(address => struct PointsManagement.ExchangeRecord[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_array(t_struct(PointsRecord)22226_storage)dyn_storage)": {"label": "mapping(address => struct PointsManagement.PointsRecord[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(ExchangeRecord)22244_storage": {"label": "struct PointsManagement.ExchangeRecord", "members": [{"label": "points", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "usdtAmount", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "isExchanged", "type": "t_bool", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(PointsRecord)22226_storage": {"label": "struct PointsManagement.PointsRecord", "members": [{"label": "amount", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "source", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "pointType", "type": "t_string_storage", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(PointsStatistics)22271_storage": {"label": "struct PointsManagement.PointsStatistics", "members": [{"label": "totalGroupBuyPoints", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "totalSalesPoints", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "totalBurnedPoints", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "totalUSDTExchanged", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "totalUSDTBalance", "type": "t_uint256", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/ProductManagement.sol:IMerchantManagement": {"src": "contracts\\ProductManagement.sol:16", "inherit": [], "libraries": [], "methods": ["isMerchant(address)", "updateMerchantSalesPoints(address,uint256)", "blacklist(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/ProductManagement.sol:IOrderManagement": {"src": "contracts\\ProductManagement.sol:22", "inherit": [], "libraries": [], "methods": ["createOrder(address,address,uint256,uint256,uint256,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/ProductManagement.sol:IPointsManagement": {"src": "contracts\\ProductManagement.sol:12", "inherit": [], "libraries": [], "methods": ["exchangeGoods(address,address,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/ProductManagement.sol:ProductManagement": {"src": "contracts\\ProductManagement.sol:33", "version": {"withMetadata": "2e7924fd293c81b7df9bc659cd2e8b2ba3e5197fdd3ead0e0f1675d44b71a3ce", "withoutMetadata": "3148d57a950ee7e0d73f6cc247758254acf924df53c8587b2ce5d6fe62de162c", "linkedWithoutMetadata": "3148d57a950ee7e0d73f6cc247758254acf924df53c8587b2ce5d6fe62de162c"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["initialize(address,address,address,address,address)", "setTimelock(address)", "createProduct(string,string,string[],uint256,uint256)", "updateProduct(uint256,string,string,string[],uint256,uint256,bool)", "buyProductWithDefaultAddress(uint256,uint256)", "buyProduct(uint256,uint256)", "createCategory(string,string)", "updateCategory(uint256,string,string,bool)", "getMerchantProducts(address)", "getMerchantStats(address)", "getActiveProducts(uint256,uint256)", "toggleProductStatus(uint256,bool)", "updateProductStock(uint256,uint256)", "batchToggleProductStatus(uint256[],bool)", "deleteProduct(uint256)", "pause()", "unpause()", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "getProductStats()", "getProductsBatch(uint256[])", "getTopSellingProducts(uint256)", "getProductsByTag(string)", "addProductTag(uint256,string)", "setProductRating(uint256,uint256)", "incrementProductViews(uint256)", "setFeaturedProduct(uint256,bool)", "getFeaturedProducts()", "validateUserAddress(address,uint256)", "getUserDefaultAddress(address)", "setOrderManagement(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "timelock", "offset": 0, "slot": "303", "type": "t_address", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:35"}, {"label": "products", "offset": 0, "slot": "304", "type": "t_mapping(t_uint256,t_struct(Product)24251_storage)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:128"}, {"label": "categories", "offset": 0, "slot": "305", "type": "t_mapping(t_uint256,t_struct(Category)24260_storage)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:129"}, {"label": "merchantProducts", "offset": 0, "slot": "306", "type": "t_mapping(t_address,t_array(t_uint256)dyn_storage)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:130"}, {"label": "productCount", "offset": 0, "slot": "307", "type": "t_uint256", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:133"}, {"label": "categoryCount", "offset": 0, "slot": "308", "type": "t_uint256", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:133"}, {"label": "orderManagement", "offset": 0, "slot": "309", "type": "t_address", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:134"}, {"label": "merchantManagement", "offset": 0, "slot": "310", "type": "t_contract(IMerchantManagement)24077", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:135"}, {"label": "pointsManagement", "offset": 0, "slot": "311", "type": "t_contract(IPointsManagement)24055", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:136"}, {"label": "addressManagement", "offset": 0, "slot": "312", "type": "t_contract(IAddressManagement)35544", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:139"}, {"label": "productsByTag", "offset": 0, "slot": "313", "type": "t_mapping(t_string_memory_ptr,t_array(t_uint256)dyn_storage)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:143"}, {"label": "featuredProducts", "offset": 0, "slot": "314", "type": "t_mapping(t_uint256,t_bool)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:145"}, {"label": "productViews", "offset": 0, "slot": "315", "type": "t_mapping(t_uint256,t_uint256)", "contract": "ProductManagement", "src": "contracts\\ProductManagement.sol:147"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_string_storage)dyn_storage": {"label": "string[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IAddressManagement)35544": {"label": "contract IAddressManagement", "numberOfBytes": "20"}, "t_contract(IMerchantManagement)24077": {"label": "contract IMerchantManagement", "numberOfBytes": "20"}, "t_contract(IPointsManagement)24055": {"label": "contract IPointsManagement", "numberOfBytes": "20"}, "t_mapping(t_address,t_array(t_uint256)dyn_storage)": {"label": "mapping(address => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_array(t_uint256)dyn_storage)": {"label": "mapping(string => uint256[])", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Category)24260_storage)": {"label": "mapping(uint256 => struct ProductManagement.Category)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(Product)24251_storage)": {"label": "mapping(uint256 => struct ProductManagement.Product)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(Category)24260_storage": {"label": "struct ProductManagement.Category", "members": [{"label": "categoryId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "name", "type": "t_string_storage", "offset": 0, "slot": "1"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "isActive", "type": "t_bool", "offset": 0, "slot": "3"}], "numberOfBytes": "128"}, "t_struct(Product)24251_storage": {"label": "struct ProductManagement.Product", "members": [{"label": "productId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "merchant", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "name", "type": "t_string_storage", "offset": 0, "slot": "2"}, {"label": "description", "type": "t_string_storage", "offset": 0, "slot": "3"}, {"label": "images", "type": "t_array(t_string_storage)dyn_storage", "offset": 0, "slot": "4"}, {"label": "price", "type": "t_uint256", "offset": 0, "slot": "5"}, {"label": "stock", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "isActive", "type": "t_bool", "offset": 0, "slot": "7"}, {"label": "sales", "type": "t_uint256", "offset": 0, "slot": "8"}, {"label": "tags", "type": "t_array(t_string_storage)dyn_storage", "offset": 0, "slot": "9"}, {"label": "rating", "type": "t_uint256", "offset": 0, "slot": "10"}, {"label": "reviewCount", "type": "t_uint256", "offset": 0, "slot": "11"}], "numberOfBytes": "384"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/QPTBuyback.sol:IAgentSystem": {"src": "contracts\\QPTBuyback.sol:13", "inherit": [], "libraries": [], "methods": ["getLevel(address)", "getUserInfo(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/QPTBuyback.sol:QPTBuyback": {"src": "contracts\\QPTBuyback.sol:44", "version": {"withMetadata": "27d1c42e3e4a0bce42639f915c0b310c33f50a6f4a7878552c41abd2e9c64f21", "withoutMetadata": "8c68f64d07fe9ee1dc955c1be6e0dd7fb219cc3ce6ccea00dfa0b2334a44d117", "linkedWithoutMetadata": "8c68f64d07fe9ee1dc955c1be6e0dd7fb219cc3ce6ccea00dfa0b2334a44d117"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts-upgradeable/token/ERC20/utils/SafeERC20Upgradeable.sol:SafeERC20Upgradeable"], "methods": ["()", "initialize(address,address,address,address,address)", "createRoom()", "createRoom(uint8)", "joinRoom(uint256)", "startLottery(uint256)", "setWinner(uint256,address,bytes32,uint256)", "finalizeTimeout(uint256)", "refundFailed(uint256)", "refundSuccess(uint256)", "refundExpired(uint256)", "getRoomInfo(uint256)", "getRoomInfoComplete(uint256)", "getBalanceDetails()", "getTotalPendingRewards()", "getRoomInfoWithTier(uint256)", "getRoomFinalizeStatus(uint256)", "getRefundStatus(uint256,address)", "setAdmin(address)", "updateTierConfig(uint8,uint256,uint256)", "getTierConfig(uint8)", "addToBuybackPool(uint256)", "removeFromBuybackPool(uint256)", "setRoomLockedAmount(uint256,uint256)", "syncBuybackPool()", "recoverRoomRemainingAmount(uint256)", "getBuybackPoolBalance()", "getRoomTimeInfo(uint256)", "()", "()", "getBNBBalance()", "withdrawBNB(address payable,uint256)", "withdrawUSDT(address,uint256)", "claimReward(uint256)", "getWinnerRewardInfo(uint256,address)", "getRewardPercentByLevel(uint8)", "getBatchWinnerRewardInfo(uint256[],address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "getLotteryInfo(uint256)", "getUserRoomLocked(address,uint256)", "getUserRoomIds(address)", "getUserRoomLockedBatch(address,uint256[])", "emergencyWithdrawQPT(address,uint256)", "setQPTToken(address)", "setAgentSystem(address)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(address)", "batchUpdateTierConfigs(enum QPTBuyback.QPTTier[],uint256[],uint256[])", "getQPTBalanceInfo()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "rooms", "offset": 0, "slot": "303", "type": "t_mapping(t_uint256,t_struct(GroupRoom)26525_storage)", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:121"}, {"label": "roomCounter", "offset": 0, "slot": "304", "type": "t_uint256", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:122"}, {"label": "totalBuybackPool", "offset": 0, "slot": "305", "type": "t_uint256", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:122"}, {"label": "tierConfigs", "offset": 0, "slot": "306", "type": "t_mapping(t_enum(QPTTier)26453,t_struct(TierConfig)26458_storage)", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:123"}, {"label": "agentSystem", "offset": 0, "slot": "307", "type": "t_contract(IAgentSystem)26431", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:124"}, {"label": "usdtToken", "offset": 0, "slot": "308", "type": "t_contract(IERC20Upgradeable)2519", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:125"}, {"label": "qptToken", "offset": 0, "slot": "309", "type": "t_contract(IERC20Upgradeable)2519", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:125"}, {"label": "timelock", "offset": 0, "slot": "310", "type": "t_address", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:126"}, {"label": "admin<PERSON><PERSON><PERSON>", "offset": 0, "slot": "311", "type": "t_address", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:126"}, {"label": "userLocks", "offset": 0, "slot": "312", "type": "t_mapping(t_address,t_struct(UserLockInfo)26535_storage)", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:127"}, {"label": "totalLockedQPT", "offset": 0, "slot": "313", "type": "t_uint256", "contract": "QPTBuyback", "src": "contracts\\QPTBuyback.sol:128"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_address)dyn_storage": {"label": "address[]", "numberOfBytes": "32"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_array(t_uint256)dyn_storage": {"label": "uint256[]", "numberOfBytes": "32"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IAgentSystem)26431": {"label": "contract IAgentSystem", "numberOfBytes": "20"}, "t_contract(IERC20Upgradeable)2519": {"label": "contract IERC20Upgradeable", "numberOfBytes": "20"}, "t_enum(QPTTier)26453": {"label": "enum QPTBuyback.QPTTier", "members": ["TIER_100", "TIER_200", "TIER_500", "TIER_1000"], "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_struct(UserLockInfo)26535_storage)": {"label": "mapping(address => struct QPTBuyback.UserLockInfo)", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_enum(QPTTier)26453,t_struct(TierConfig)26458_storage)": {"label": "mapping(enum QPTBuyback.QPTTier => struct QPTBuyback.TierConfig)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(GroupRoom)26525_storage)": {"label": "mapping(uint256 => struct QPTBuyback.GroupRoom)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(GroupRoom)26525_storage": {"label": "struct QPTBuyback.GroupRoom", "members": [{"label": "id", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "creator", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "participants", "type": "t_array(t_address)dyn_storage", "offset": 0, "slot": "2"}, {"label": "refunded", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "3"}, {"label": "deadline", "type": "t_uint256", "offset": 0, "slot": "4"}, {"label": "locked", "type": "t_bool", "offset": 0, "slot": "5"}, {"label": "closeBlock", "type": "t_uint256", "offset": 0, "slot": "6"}, {"label": "winner", "type": "t_address", "offset": 0, "slot": "7"}, {"label": "lockedBuybackAmount", "type": "t_uint256", "offset": 0, "slot": "8"}, {"label": "rewardClaimed", "type": "t_bool", "offset": 0, "slot": "9"}, {"label": "tier", "type": "t_enum(QPTTier)26453", "offset": 1, "slot": "9"}, {"label": "winner<PERSON><PERSON><PERSON>", "type": "t_uint256", "offset": 0, "slot": "10"}, {"label": "participantLocked", "type": "t_mapping(t_address,t_uint256)", "offset": 0, "slot": "11"}, {"label": "ready<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "t_bool", "offset": 0, "slot": "12"}, {"label": "lotteryTxHash", "type": "t_bytes32", "offset": 0, "slot": "13"}, {"label": "lotteryTimestamp", "type": "t_uint256", "offset": 0, "slot": "14"}, {"label": "lastActionTime", "type": "t_uint256", "offset": 0, "slot": "15"}, {"label": "createTime", "type": "t_uint256", "offset": 0, "slot": "16"}], "numberOfBytes": "544"}, "t_struct(TierConfig)26458_storage": {"label": "struct QPTBuyback.TierConfig", "members": [{"label": "participantQPT", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "qptSubsidy", "type": "t_uint256", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_struct(UserLockInfo)26535_storage": {"label": "struct QPTBuyback.UserLockInfo", "members": [{"label": "totalLocked", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "roomIds", "type": "t_array(t_uint256)dyn_storage", "offset": 0, "slot": "1"}, {"label": "roomLocked", "type": "t_mapping(t_uint256,t_uint256)", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/QPTLocker.sol:QPTLocker": {"src": "contracts\\QPTLocker.sol:26", "version": {"withMetadata": "92562e7b34c134c477a21500688b8e73ee0ecc9be44fe3b1a1d1203655f8ac4f", "withoutMetadata": "013ed087fec41ba4f551dbadb7e6b526354f7b1ae0801fc20543a846ceb0b318", "linkedWithoutMetadata": "013ed087fec41ba4f551dbadb7e6b526354f7b1ae0801fc20543a846ceb0b318"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "contracts/interfaces/IQPTLock.sol:IQPTLock", "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol:ReentrancyGuardUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": ["@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol:SafeERC20", "@openzeppelin/contracts/utils/Address.sol:Address"], "methods": ["initialize(address,address,address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "setTimelock(address)", "setGroupBuyRoomAddress(address)", "setNodeStakingContract(address)", "lockForRoom(uint256,uint256)", "rewardWinner(uint256,address)", "getRoomInfo(uint256)", "getRoomUSDTAmount(uint256)", "get<PERSON><PERSON><PERSON><PERSON>(address)", "getAgentLevel(address)", "forceUnlock(uint256)", "markRoomSuccess(uint256)", "claimLockedQPT(uint256)", "hasLocked(uint256)", "lockNodeQPT(address,uint256)", "unlockNodeQPT(address)", "getUserLockedAmount(address)", "claimReferrerReward(uint256)", "getReferrerPendingReward(address,uint256)", "hasReferrerClaimed(address,uint256)", "setLockDays(uint8,uint256)", "setBatchLockDays(uint256[5])", "reinitializeLockDays()", "pause()", "unpause()", "withdrawQPT(address,uint256)", "()", "()", "getBNBBalance()", "withdrawBNB(address payable,uint256)", "getReferrerRewardCount(address)", "getReferrerRewardHistory(address)", "getReferrerRewardByIndex(address,uint256)", "getDistributionStats()", "getUserClaimedStats(address)", "getWinnerRewardInfo(uint256,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "_status", "offset": 0, "slot": "153", "type": "t_uint256", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:38"}, {"label": "__gap", "offset": 0, "slot": "154", "type": "t_array(t_uint256)49_storage", "contract": "ReentrancyGuardUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\ReentrancyGuardUpgradeable.sol:88"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "253", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "qptToken", "offset": 0, "slot": "303", "type": "t_contract(IERC20)6754", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:37"}, {"label": "agentSystem", "offset": 0, "slot": "304", "type": "t_contract(IAgentSystem)35674", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:37"}, {"label": "groupBuyRoomAddress", "offset": 0, "slot": "305", "type": "t_address", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:38"}, {"label": "nodeStakingContract", "offset": 0, "slot": "306", "type": "t_address", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:40"}, {"label": "timelock", "offset": 0, "slot": "307", "type": "t_address", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:42"}, {"label": "roomLocks", "offset": 0, "slot": "308", "type": "t_mapping(t_uint256,t_struct(RoomLock)30336_storage)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:72"}, {"label": "amountMappings", "offset": 0, "slot": "309", "type": "t_mapping(t_uint256,t_struct(AmountMapping)30343_storage)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:75"}, {"label": "lockDays", "offset": 0, "slot": "310", "type": "t_mapping(t_uint8,t_uint256)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:79"}, {"label": "lockedNodeQPT", "offset": 0, "slot": "311", "type": "t_mapping(t_address,t_uint256)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:81"}, {"label": "referrerPendingRewards", "offset": 0, "slot": "312", "type": "t_mapping(t_address,t_mapping(t_uint256,t_uint256))", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:84"}, {"label": "referrerClaimedRewards", "offset": 0, "slot": "313", "type": "t_mapping(t_address,t_mapping(t_uint256,t_bool))", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:90"}, {"label": "referrerRewardHistory", "offset": 0, "slot": "314", "type": "t_mapping(t_address,t_array(t_struct(ReferrerRewardInfo)30354_storage)dyn_storage)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:93"}, {"label": "hasRewarded", "offset": 0, "slot": "315", "type": "t_mapping(t_uint256,t_bool)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:95"}, {"label": "totalClaimedQPT", "offset": 0, "slot": "316", "type": "t_uint256", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:95"}, {"label": "userClaimedQPT", "offset": 0, "slot": "317", "type": "t_mapping(t_address,t_uint256)", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:96"}, {"label": "__gap", "offset": 0, "slot": "318", "type": "t_array(t_uint256)48_storage", "contract": "QPTLocker", "src": "contracts\\QPTLocker.sol:670"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_struct(ReferrerRewardInfo)30354_storage)dyn_storage": {"label": "struct QPTLocker.ReferrerRewardInfo[]", "numberOfBytes": "32"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_contract(IAgentSystem)35674": {"label": "contract IAgentSystem", "numberOfBytes": "20"}, "t_contract(IERC20)6754": {"label": "contract IERC20", "numberOfBytes": "20"}, "t_mapping(t_address,t_array(t_struct(ReferrerRewardInfo)30354_storage)dyn_storage)": {"label": "mapping(address => struct QPTLocker.ReferrerRewardInfo[])", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_bool))": {"label": "mapping(address => mapping(uint256 => bool))", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_uint256,t_uint256))": {"label": "mapping(address => mapping(uint256 => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_bool)": {"label": "mapping(uint256 => bool)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(AmountMapping)30343_storage)": {"label": "mapping(uint256 => struct QPTLocker.AmountMapping)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_struct(RoomLock)30336_storage)": {"label": "mapping(uint256 => struct QPTLocker.RoomLock)", "numberOfBytes": "32"}, "t_mapping(t_uint256,t_uint256)": {"label": "mapping(uint256 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_uint8,t_uint256)": {"label": "mapping(uint8 => uint256)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(AmountMapping)30343_storage": {"label": "struct QPTLocker.AmountMapping", "members": [{"label": "usdtAmount", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "lockAmount", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "rewardAmount", "type": "t_uint256", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_struct(ReferrerRewardInfo)30354_storage": {"label": "struct QPTLocker.ReferrerRewardInfo", "members": [{"label": "roomId", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "winner", "type": "t_address", "offset": 0, "slot": "1"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "timestamp", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "claimed", "type": "t_bool", "offset": 0, "slot": "4"}], "numberOfBytes": "160"}, "t_struct(RoomLock)30336_storage": {"label": "struct QPTLocker.RoomLock", "members": [{"label": "creator", "type": "t_address", "offset": 0, "slot": "0"}, {"label": "amount", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "usdtAmount", "type": "t_uint256", "offset": 0, "slot": "2"}, {"label": "unlockTime", "type": "t_uint256", "offset": 0, "slot": "3"}, {"label": "isSuccess", "type": "t_bool", "offset": 0, "slot": "4"}, {"label": "isClaimed", "type": "t_bool", "offset": 1, "slot": "4"}], "numberOfBytes": "160"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IAgentSystem": {"src": "contracts\\SimpleQueryContract.sol:12", "inherit": [], "libraries": [], "methods": ["getUserInfo(address)", "hasRole(bytes32,address)", "systemAdmin()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IGroupBuyRoom": {"src": "contracts\\SimpleQueryContract.sol:51", "inherit": [], "libraries": [], "methods": ["hasJoined(uint256,address)", "claimed(uint256,address)", "getRoom(uint256)", "totalRooms()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:INodeStaking": {"src": "contracts\\SimpleQueryContract.sol:61", "inherit": [], "libraries": [], "methods": ["isNodeActive(address)", "lastClaimDate(address)", "getCurrentRequiredStake()", "totalEffectiveNodes()", "MAX_NODES()", "totalDividends()", "snapshotDividends()", "lastSnapshotTime()", "dailyClaimedCount()", "hasClaimedTodayReward(address)", "getNodeActivationTime(address)", "canNodeCreateSnapshot(address)", "getNodeAddresses()", "getUserNodeStatus(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IPointsManagement": {"src": "contracts\\SimpleQueryContract.sol:21", "inherit": [], "libraries": [], "methods": ["groupBuyPointsNonExchangeable(address)", "salesPointsExchangeable(address)", "lastExchangeTime(address)", "MIN_EXCHANGE_POINTS()", "EXCHANGE_COOLDOWN()", "pointsStats()", "getPointsDecimals()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IProductManagement": {"src": "contracts\\SimpleQueryContract.sol:37", "inherit": [], "libraries": [], "methods": ["productCount()", "products(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IQPTBuyback": {"src": "contracts\\SimpleQueryContract.sol:120", "inherit": [], "libraries": [], "methods": ["hasParticipated(uint256,address)", "getCurrentRound()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:IQPTLocker": {"src": "contracts\\SimpleQueryContract.sol:107", "inherit": [], "libraries": [], "methods": ["getUserLockInfo(address)", "lockedNodeQPT(address)", "buybackLocked(address)", "getRoomInfo(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/SimpleQueryContract.sol:SimpleQueryContract": {"src": "contracts\\SimpleQueryContract.sol:126", "version": {"withMetadata": "684f4e33279e42a973ed34294f925a8e04996198bcf1fcf7548bedd76fbfba3b", "withoutMetadata": "29989458229237f40f17587aa565d026807e2b39652e5adf26e20b841e902743", "linkedWithoutMetadata": "29989458229237f40f17587aa565d026807e2b39652e5adf26e20b841e902743"}, "inherit": ["@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol:PausableUpgradeable", "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:OwnableUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["initialize(address,address,address,address,address,address,address)", "setProductManagementAddress(address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()", "initializeTimelock(address)", "getUserCompleteStatus(address)", "getUserRoleInRoom(uint256,address)", "checkClaimEligibility(uint256,address)", "getGroupBuyStats(uint256)", "updateContractAddresses(address,address,address,address,address,address)", "getNodeStakingStats()", "getLockingStats(uint256)", "getPointsSystemStats()", "batchGetProducts(uint256[])", "getProductStats(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "_storageChecksums", "offset": 0, "slot": "1", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "2", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "3", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "_owner", "offset": 0, "slot": "53", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "54", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\OwnableUpgradeable.sol:94"}, {"label": "_paused", "offset": 0, "slot": "103", "type": "t_bool", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:29"}, {"label": "__gap", "offset": 0, "slot": "104", "type": "t_array(t_uint256)49_storage", "contract": "PausableUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\security\\PausableUpgradeable.sol:116"}, {"label": "__gap", "offset": 0, "slot": "153", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "203", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "agentSystemAddress", "offset": 0, "slot": "253", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:131"}, {"label": "groupBuyRoomAddress", "offset": 0, "slot": "254", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:132"}, {"label": "pointsManagementAddress", "offset": 0, "slot": "255", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:132"}, {"label": "nodeStakingAddress", "offset": 0, "slot": "256", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:134"}, {"label": "qpt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "257", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:135"}, {"label": "qpt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "258", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:137"}, {"label": "timelock", "offset": 0, "slot": "259", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:167"}, {"label": "productManagementAddress", "offset": 0, "slot": "260", "type": "t_address", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:168"}, {"label": "__gap", "offset": 0, "slot": "261", "type": "t_array(t_uint256)49_storage", "contract": "SimpleQueryContract", "src": "contracts\\SimpleQueryContract.sol:985"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/StorageValidator.sol:StorageValidator": {"src": "contracts\\StorageValidator.sol:15", "inherit": [], "libraries": [], "methods": ["validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "getStorageChecksum(string)", "validateUpgradeConsistency()", "setEmergencyFixMode(bool)", "isEmergencyFixMode()", "validateBasicState()", "getHealthReport()", "postUpgradeValidation()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_storageChecksums", "offset": 0, "slot": "0", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "1", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}], "types": {"t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/TimelockControllerUpgradeable.sol:CustomTimelockController": {"src": "contracts\\TimelockControllerUpgradeable.sol:13", "version": {"withMetadata": "8fae9137b5cb44e79eacead4a4904e461f5860fb6a16dc0d719caaee1841c596", "withoutMetadata": "44368eb269524e0d0a96da07fd5ef1cdacf3e39246b6098d5ec36367374b894a", "linkedWithoutMetadata": "44368eb269524e0d0a96da07fd5ef1cdacf3e39246b6098d5ec36367374b894a"}, "inherit": ["contracts/StorageValidator.sol:StorageValidator", "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol:UUPSUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/ERC1967/ERC1967UpgradeUpgradeable.sol:ERC1967UpgradeUpgradeable", "@openzeppelin/contracts-upgradeable/interfaces/IERC1967Upgradeable.sol:IERC1967Upgradeable", "@openzeppelin/contracts-upgradeable/interfaces/draft-IERC1822Upgradeable.sol:IERC1822ProxiableUpgradeable", "@openzeppelin/contracts-upgradeable/governance/TimelockControllerUpgradeable.sol:TimelockControllerUpgradeable", "@openzeppelin/contracts-upgradeable/token/ERC1155/IERC1155ReceiverUpgradeable.sol:IERC1155ReceiverUpgradeable", "@openzeppelin/contracts-upgradeable/token/ERC721/IERC721ReceiverUpgradeable.sol:IERC721ReceiverUpgradeable", "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol:AccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/ERC165Upgradeable.sol:ERC165Upgradeable", "@openzeppelin/contracts-upgradeable/utils/introspection/IERC165Upgradeable.sol:IERC165Upgradeable", "@openzeppelin/contracts-upgradeable/access/IAccessControlUpgradeable.sol:IAccessControlUpgradeable", "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:ContextUpgradeable", "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:Initializable"], "libraries": [], "methods": ["initialize(uint256,address[],address[],address)", "validateStorageLayout()", "calculateStorageChecksum()", "emergencyStorageFix()", "savePreUpgradeState()", "setEmergencyFixMode(bool)", "postUpgradeValidation()"], "linkReferences": [], "errors": [], "layout": {"storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\ContextUpgradeable.sol:36"}, {"label": "__gap", "offset": 0, "slot": "51", "type": "t_array(t_uint256)50_storage", "contract": "ERC165Upgradeable", "src": "@openzeppelin\\contracts-upgradeable\\utils\\introspection\\ERC165Upgradeable.sol:41"}, {"label": "_roles", "offset": 0, "slot": "101", "type": "t_mapping(t_bytes32,t_struct(RoleData)34_storage)", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:62"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "AccessControlUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\access\\AccessControlUpgradeable.sol:260"}, {"label": "_timestamps", "offset": 0, "slot": "151", "type": "t_mapping(t_bytes32,t_uint256)", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:33"}, {"label": "_minDelay", "offset": 0, "slot": "152", "type": "t_uint256", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:34"}, {"label": "__gap", "offset": 0, "slot": "153", "type": "t_array(t_uint256)48_storage", "contract": "TimelockControllerUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\governance\\TimelockControllerUpgradeable.sol:433"}, {"label": "__gap", "offset": 0, "slot": "201", "type": "t_array(t_uint256)50_storage", "contract": "ERC1967UpgradeUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\ERC1967\\ERC1967UpgradeUpgradeable.sol:169"}, {"label": "__gap", "offset": 0, "slot": "251", "type": "t_array(t_uint256)50_storage", "contract": "UUPSUpgradeable", "src": "@openzeppelin\\contracts-upgradeable\\proxy\\utils\\UUPSUpgradeable.sol:111"}, {"label": "_storageChecksums", "offset": 0, "slot": "301", "type": "t_mapping(t_string_memory_ptr,t_bytes32)", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:29"}, {"label": "_emergencyFixMode", "offset": 0, "slot": "302", "type": "t_bool", "contract": "StorageValidator", "src": "contracts\\StorageValidator.sol:31"}, {"label": "__gap", "offset": 0, "slot": "303", "type": "t_array(t_uint256)50_storage", "contract": "CustomTimelockController", "src": "contracts\\TimelockControllerUpgradeable.sol:91"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)48_storage": {"label": "uint256[48]", "numberOfBytes": "1536"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_struct(RoleData)34_storage)": {"label": "mapping(bytes32 => struct AccessControlUpgradeable.RoleData)", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_uint256)": {"label": "mapping(bytes32 => uint256)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bytes32)": {"label": "mapping(string => bytes32)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_struct(RoleData)34_storage": {"label": "struct AccessControlUpgradeable.RoleData", "members": [{"label": "members", "type": "t_mapping(t_address,t_bool)", "offset": 0, "slot": "0"}, {"label": "adminRole", "type": "t_bytes32", "offset": 0, "slot": "1"}], "numberOfBytes": "64"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}, "t_string_storage": {"label": "string"}}, "layoutVersion": "1.2", "flat": true, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IAddressManagement.sol:IAddressManagement": {"src": "contracts\\interfaces\\IAddressManagement.sol:17", "inherit": [], "libraries": [], "methods": ["getAddress(address,uint256)", "getDefaultA<PERSON>ress(address)", "getMyDefaultAddress()", "getMyAddresses()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IAgentSystem.sol:IAgentSystem": {"src": "contracts\\interfaces\\IAgentSystem.sol:4", "inherit": [], "libraries": [], "methods": ["getInviter(address)", "getLevel(address)", "isAgent(address)", "isBlacklisted(address)", "addPerformance(address,uint256)", "getPerformance(address)", "tryUpgrade(address)", "isAgentSystem()", "canCreateGroupBuyRoom(address)", "canJoinGroupBuyRoom(address)", "canTransferPoints(address)", "canReceivePoints(address)", "canStakeNode(address)", "canJoinQPTBuyback(address)", "canRegisterMerchant(address)", "getUserPermissions(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IFeeSplitManager.sol:IFeeSplitManager": {"src": "contracts\\interfaces\\IFeeSplitManager.sol:4", "inherit": [], "libraries": [], "methods": ["distribute(address,uint256,uint256,address,address,address,address)", "calcSplitAmountsByTier(uint256)", "calcSplitAmountsByTierAndLevel(uint256,uint256)", "getPlatformAllocation(uint256,uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IGroupBuyRoom.sol:IGroupBuyRoom": {"src": "contracts\\interfaces\\IGroupBuyRoom.sol:7", "inherit": [], "libraries": [], "methods": ["isGroupBuyRoom()", "getRoomTier(uint256)", "<PERSON><PERSON><PERSON><PERSON>(uint256)", "getWinnerPerformance(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/INodeStaking.sol:INodeStaking": {"src": "contracts\\interfaces\\INodeStaking.sol:4", "inherit": [], "libraries": [], "methods": ["receiveDividendFund(uint256)", "getNodeActivationTime(address)", "canNodeCreateSnapshot(address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IPointsManagement.sol:IPointsManagement": {"src": "contracts\\interfaces\\IPointsManagement.sol:4", "inherit": [], "libraries": [], "methods": ["generatePoints(address,uint256,string,string)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IQPTBuyback.sol:IQPTBuyback": {"src": "contracts\\interfaces\\IQPTBuyback.sol:4", "inherit": [], "libraries": [], "methods": ["executeBuyback(uint256)", "setGroupBuyRoom(address)", "setQPTLocker(address)", "receiveBuybackFund(uint256)", "claimReward(uint256)", "refundFailed(uint256)", "refundSuccess(uint256)", "recoverFunds(address,address,uint256)", "depositQPT(uint256)", "burnQPT(uint256)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IQPTLock.sol:IQPTLock": {"src": "contracts\\interfaces\\IQPTLock.sol:4", "inherit": [], "libraries": [], "methods": ["lockForRoom(uint256,uint256)", "forceUnlock(uint256)", "markRoomSuccess(uint256)", "claimLockedQPT(uint256)", "hasLocked(uint256)", "rewardWinner(uint256,address)", "lockNodeQPT(address,uint256)", "unlockNodeQPT(address)", "getUserLockedAmount(address)", "getRoomInfo(uint256)", "getUserClaimedStats(address)", "getWinnerRewardInfo(uint256,address)"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}, "contracts/interfaces/IVersioned.sol:IVersioned": {"src": "contracts\\interfaces\\IVersioned.sol:4", "inherit": [], "libraries": [], "methods": ["getVersion()"], "linkReferences": [], "errors": [], "layout": {"storage": [], "types": {}, "layoutVersion": "1.2", "flat": false, "namespaces": {}}, "solcVersion": "0.8.22"}}]}