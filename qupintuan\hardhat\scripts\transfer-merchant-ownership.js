// 转移 MerchantManagement 合约所有权给 Timelock
const { ethers } = require("hardhat");
require('dotenv').config();

async function main() {
  console.log("🔄 转移 MerchantManagement 合约所有权给 Timelock");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 1. 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log("👤 操作者地址:", deployer.address);

  // 2. 获取合约地址
  const MERCHANT_MANAGEMENT_ADDRESS = process.env.MERCHANT_MANAGEMENT_ADDRESS;
  const TIMELOCK_ADDRESS = process.env.SECURE_TIMELOCK_ADDRESS || process.env.TIMELOCK_ADDRESS;

  if (!MERCHANT_MANAGEMENT_ADDRESS || !TIMELOCK_ADDRESS) {
    throw new Error("请在 .env 文件中设置 MERCHANT_MANAGEMENT_ADDRESS 和 TIMELOCK_ADDRESS");
  }

  console.log("📍 MerchantManagement:", MERCHANT_MANAGEMENT_ADDRESS);
  console.log("📍 Timelock:", TIMELOCK_ADDRESS);

  console.log("\n✅ 检查当前状态");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 3. 获取合约实例
  const merchantManagement = await ethers.getContractAt("MerchantManagement", MERCHANT_MANAGEMENT_ADDRESS);
  
  const currentOwner = await merchantManagement.owner();
  const timelock = await merchantManagement.timelock();
  const productMgmtAddr = await merchantManagement.productManagementAddress();
  
  console.log("📋 当前状态:");
  console.log("   - 当前所有者:", currentOwner);
  console.log("   - 目标所有者:", TIMELOCK_ADDRESS);
  console.log("   - 合约 Timelock:", timelock);
  console.log("   - ProductManagement:", productMgmtAddr);
  console.log("   - 操作者:", deployer.address);
  console.log("   - 权限检查:", currentOwner.toLowerCase() === deployer.address.toLowerCase() ? "✅ 有权限" : "❌ 无权限");
  console.log("   - Timelock 一致:", timelock.toLowerCase() === TIMELOCK_ADDRESS.toLowerCase() ? "✅ 一致" : "❌ 不一致");

  if (currentOwner.toLowerCase() === TIMELOCK_ADDRESS.toLowerCase()) {
    console.log("✅ 所有权已经是 Timelock，无需转移");
    return;
  }

  if (currentOwner.toLowerCase() !== deployer.address.toLowerCase()) {
    throw new Error("当前账户不是合约所有者，无法转移所有权");
  }

  if (timelock.toLowerCase() !== TIMELOCK_ADDRESS.toLowerCase()) {
    throw new Error("合约中的 Timelock 地址与环境变量不匹配");
  }

  console.log("\n🔍 配置完整性检查");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  // 4. 检查配置是否完整
  const configChecks = {
    productManagementSet: productMgmtAddr !== ethers.ZeroAddress,
    timelockConfigured: timelock !== ethers.ZeroAddress,
    ownershipReady: currentOwner === deployer.address
  };

  console.log("📋 配置检查:");
  console.log("   - ProductManagement 已设置:", configChecks.productManagementSet ? "✅ 是" : "❌ 否");
  console.log("   - Timelock 已配置:", configChecks.timelockConfigured ? "✅ 是" : "❌ 否");
  console.log("   - 所有权准备就绪:", configChecks.ownershipReady ? "✅ 是" : "❌ 否");

  if (!configChecks.productManagementSet) {
    console.log("⚠️ 警告：ProductManagement 地址尚未设置");
    console.log("💡 建议先运行: npx hardhat run scripts/set-product-management-direct.js --network bscTestnet");
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question('是否继续转移所有权？(y/N): ', resolve);
    });
    rl.close();

    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log("❌ 用户取消操作");
      return;
    }
  }

  console.log("\n🔄 转移所有权");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  try {
    // 5. 转移所有权
    console.log("⏳ 调用 transferOwnership...");
    console.log("   - 从:", currentOwner);
    console.log("   - 到:", TIMELOCK_ADDRESS);
    
    const tx = await merchantManagement.transferOwnership(TIMELOCK_ADDRESS);
    console.log("📋 交易已提交，等待确认...");
    console.log("   - 交易哈希:", tx.hash);

    const receipt = await tx.wait();
    console.log("✅ 交易已确认");
    console.log("   - 区块号:", receipt.blockNumber);
    console.log("   - Gas 使用量:", receipt.gasUsed.toString());

    // 6. 验证转移结果
    console.log("\n🔍 验证转移结果");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const newOwner = await merchantManagement.owner();

    console.log("📋 验证结果:");
    console.log("   - 转移前所有者:", currentOwner);
    console.log("   - 转移后所有者:", newOwner);
    console.log("   - 目标所有者:", TIMELOCK_ADDRESS);
    console.log("   - 转移成功:", newOwner.toLowerCase() === TIMELOCK_ADDRESS.toLowerCase() ? "✅ 是" : "❌ 否");

    if (newOwner.toLowerCase() !== TIMELOCK_ADDRESS.toLowerCase()) {
      throw new Error("所有权转移失败：新所有者不匹配");
    }

    // 7. 检查事件
    console.log("\n📋 检查事件日志");
    console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

    const events = receipt.logs;
    let eventFound = false;

    for (const log of events) {
      try {
        const parsed = merchantManagement.interface.parseLog(log);
        if (parsed.name === "OwnershipTransferred") {
          console.log("✅ 找到 OwnershipTransferred 事件:");
          console.log("   - previousOwner:", parsed.args.previousOwner);
          console.log("   - newOwner:", parsed.args.newOwner);
          eventFound = true;
        }
      } catch (error) {
        // 忽略解析错误
      }
    }

    if (!eventFound) {
      console.log("⚠️ 未找到 OwnershipTransferred 事件");
    }

  } catch (error) {
    console.error("❌ 转移所有权失败:", error.message);
    throw error;
  }

  console.log("\n🔒 安全性提升");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

  console.log("📋 安全性变化:");
  console.log("   - 所有权控制: 部署者 → Timelock");
  console.log("   - 安全级别: MEDIUM → HIGH");
  console.log("   - 权限管理: 直接控制 → 多签+延迟");
  console.log("   - 升级控制: 立即执行 → 延迟执行");

  console.log("\n⚠️ 重要提醒:");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("🔸 所有权已转移给 Timelock");
  console.log("🔸 后续所有管理操作都需要通过多签钱包");
  console.log("🔸 所有操作都有 10 分钟延迟期");
  console.log("🔸 合约升级需要多签确认");

  // 8. 保存转移信息
  const transferInfo = {
    timestamp: new Date().toISOString(),
    stage: "transfer-ownership",
    merchantManagementAddress: MERCHANT_MANAGEMENT_ADDRESS,
    previousOwner: currentOwner,
    newOwner: newOwner,
    timelockAddress: TIMELOCK_ADDRESS,
    transactionHash: tx.hash,
    blockNumber: receipt.blockNumber,
    gasUsed: receipt.gasUsed.toString(),
    operator: deployer.address,
    status: "SUCCESS",
    securityLevel: "HIGH",
    description: "转移 MerchantManagement 所有权给 Timelock"
  };

  const fs = require('fs');
  fs.writeFileSync(
    './ownership-transfer.json',
    JSON.stringify(transferInfo, null, 2)
  );

  console.log("\n🎉 所有权转移完成！");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("✅ 所有权已转移给 Timelock");
  console.log("✅ 安全级别已提升");
  console.log("✅ 转移信息已保存");
  
  console.log("\n📋 下一步操作:");
  console.log("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
  console.log("1. 🔍 验证最终配置:");
  console.log("   npx hardhat run scripts/verify-merchant-config.js --network bscTestnet");
  console.log("2. 🛒 测试商城购买功能");
  console.log("3. 📊 监控合约运行状态");
  
  console.log("\n💾 转移信息已保存到 ownership-transfer.json");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 转移所有权失败:", error);
    process.exit(1);
  });
