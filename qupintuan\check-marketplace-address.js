// 检查 PointsManagement 的 marketplaceAddress 设置
import { createPublicClient, http } from 'viem';
import { bscTestnet } from 'viem/chains';

// 合约地址
const POINTS_MANAGEMENT = "0xC3486bf571914BC5c83f89c69fA23fD3C75Aa5f4";
const PRODUCT_MANAGEMENT = "0xAFFFd165b2265a737DB8014C62eeB1Eabe54702A";

// PointsManagement ABI
const POINTS_MANAGEMENT_ABI = [
  {
    "inputs": [],
    "name": "marketplaceAddress",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "groupBuyRoomAddress",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "merchantManager",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "timelock",
    "outputs": [{"internalType": "address", "name": "", "type": "address"}],
    "stateMutability": "view",
    "type": "function"
  }
];

async function checkMarketplaceAddress() {
  console.log("🔍 检查 PointsManagement 的 marketplaceAddress 设置...");
  console.log("📍 PointsManagement:", POINTS_MANAGEMENT);
  console.log("📍 ProductManagement:", PRODUCT_MANAGEMENT);

  try {
    const publicClient = createPublicClient({
      chain: bscTestnet,
      transport: http()
    });

    // 1. 检查 marketplaceAddress
    console.log("\n🔍 步骤1：检查 marketplaceAddress...");
    const marketplaceAddress = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'marketplaceAddress'
    });

    console.log("当前 marketplaceAddress:", marketplaceAddress);
    console.log("期望 ProductManagement:", PRODUCT_MANAGEMENT);
    
    const isCorrect = marketplaceAddress.toLowerCase() === PRODUCT_MANAGEMENT.toLowerCase();
    console.log("地址是否匹配:", isCorrect ? "✅ 是" : "❌ 否");

    if (!isCorrect) {
      console.log("\n❌ 发现问题！marketplaceAddress 设置错误");
      console.log("💡 这就是购买失败的根本原因！");
      console.log("🔧 解决方案：需要将 ProductManagement 设置为 marketplaceAddress");
    }

    // 2. 检查其他相关地址
    console.log("\n🔍 步骤2：检查其他相关地址...");
    
    const groupBuyRoomAddress = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'groupBuyRoomAddress'
    });

    const merchantManager = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'merchantManager'
    });

    const timelock = await publicClient.readContract({
      address: POINTS_MANAGEMENT,
      abi: POINTS_MANAGEMENT_ABI,
      functionName: 'timelock'
    });

    console.log("groupBuyRoomAddress:", groupBuyRoomAddress);
    console.log("merchantManager:", merchantManager);
    console.log("timelock:", timelock);

    // 3. 总结
    console.log("\n📊 PointsManagement 配置总结:");
    console.log("marketplaceAddress:", isCorrect ? "✅ 正确" : "❌ 错误");
    console.log("groupBuyRoomAddress:", groupBuyRoomAddress !== "0x0000000000000000000000000000000000000000" ? "✅ 已设置" : "❌ 未设置");
    console.log("merchantManager:", merchantManager !== "0x0000000000000000000000000000000000000000" ? "✅ 已设置" : "❌ 未设置");
    console.log("timelock:", timelock !== "0x0000000000000000000000000000000000000000" ? "✅ 已设置" : "❌ 未设置");

    if (!isCorrect) {
      console.log("\n🔧 修复步骤:");
      console.log("1. 调用 PointsManagement.setMarketplaceAddress('" + PRODUCT_MANAGEMENT + "')");
      console.log("2. 确保调用者是合约所有者");
      console.log("3. 重新测试购买功能");
    } else {
      console.log("\n🤔 marketplaceAddress 设置正确，问题可能在其他地方...");
    }

  } catch (error) {
    console.error("❌ 检查过程失败:", error.message);
  }
}

// 运行检查
checkMarketplaceAddress().catch(console.error);
